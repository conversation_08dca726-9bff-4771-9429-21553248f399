<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Ajouter le rôle 'RU' (Responsable Université) à l'enum role_agent
     * APPROCHE CHIRURGICALE : Modifier uniquement l'enum sans affecter les données existantes
     */
    public function up(): void
    {
        // Modifier l'enum pour inclure le nouveau rôle 'RU'
        DB::statement("ALTER TABLE agents MODIFY COLUMN role_agent ENUM('DPAF', 'MS', 'RS', 'RU') NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Vérifier qu'aucun agent n'a le rôle 'RU' avant de le supprimer
        $ruAgents = DB::table('agents')->where('role_agent', 'RU')->count();
        
        if ($ruAgents > 0) {
            throw new Exception("Impossible de supprimer le rôle 'RU' : {$ruAgents} agent(s) utilisent encore ce rôle.");
        }
        
        // Restaurer l'enum original
        DB::statement("ALTER TABLE agents MODIFY COLUMN role_agent ENUM('DPAF', 'MS', 'RS') NULL");
    }
};
