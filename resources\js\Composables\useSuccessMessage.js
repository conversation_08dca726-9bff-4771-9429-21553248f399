import { ref } from 'vue'

// État global pour les messages de succès
const successMessages = ref([])
let messageId = 0

export function useSuccessMessage() {
    const showSuccess = (message, title = 'Succès', duration = 5000) => {
        const id = ++messageId
        const successMessage = {
            id,
            title,
            message,
            duration,
            show: true
        }
        
        successMessages.value.push(successMessage)
        
        // Auto-suppression après la durée spécifiée
        setTimeout(() => {
            removeMessage(id)
        }, duration)
        
        return id
    }
    
    const removeMessage = (id) => {
        const index = successMessages.value.findIndex(msg => msg.id === id)
        if (index > -1) {
            successMessages.value.splice(index, 1)
        }
    }
    
    const clearAllMessages = () => {
        successMessages.value = []
    }
    
    // Messages prédéfinis pour les actions courantes - Version professionnelle
    const showProfileUpdated = () => {
        return showSuccess('Vos informations personnelles ont été mises à jour avec succès.', 'Profil mis à jour')
    }

    const showPasswordChanged = () => {
        return showSuccess('Votre mot de passe a été modifié avec succès. Veuillez vous reconnecter lors de votre prochaine session.', 'Mot de passe modifié')
    }

    const showUserCreated = (nom, prenom, email) => {
        return showSuccess(`L'utilisateur "${prenom} ${nom}" (${email}) a été créé avec succès et peut maintenant accéder à la plateforme.`, 'Utilisateur créé')
    }

    const showUserUpdated = (nom, prenom) => {
        return showSuccess(`Les informations de l'utilisateur "${prenom} ${nom}" ont été mises à jour avec succès.`, 'Utilisateur mis à jour')
    }

    const showUserDeleted = (nom, prenom) => {
        return showSuccess(`L'utilisateur "${prenom} ${nom}" a été supprimé avec succès du système.`, 'Utilisateur supprimé')
    }

    const showAgentCreated = (nom, prenom, structure) => {
        return showSuccess(`L'agent "${prenom} ${nom}" a été ajouté avec succès dans la structure "${structure}".`, 'Agent créé')
    }

    const showAgentUpdated = (nom, prenom) => {
        return showSuccess(`Les informations de l'agent "${prenom} ${nom}" ont été mises à jour avec succès.`, 'Agent mis à jour')
    }

    const showAgentDeleted = (nom, prenom) => {
        return showSuccess(`L'agent "${prenom} ${nom}" a été supprimé avec succès du système.`, 'Agent supprimé')
    }

    const showStructureCreated = (nomStructure) => {
        return showSuccess(`La structure "${nomStructure}" a été créée avec succès et est maintenant disponible pour les affectations.`, 'Structure créée')
    }

    const showStructureUpdated = (nomStructure) => {
        return showSuccess(`La structure "${nomStructure}" a été mise à jour avec succès.`, 'Structure mise à jour')
    }

    const showStructureDeleted = (nomStructure) => {
        return showSuccess(`La structure "${nomStructure}" a été supprimée avec succès du système.`, 'Structure supprimée')
    }

    const showStagiaireCreated = (nom, prenom) => {
        return showSuccess(`Le stagiaire "${prenom} ${nom}" a été enregistré avec succès dans le système.`, 'Stagiaire créé')
    }

    const showStagiaireUpdated = (nom, prenom) => {
        return showSuccess(`Les informations du stagiaire "${prenom} ${nom}" ont été mises à jour avec succès.`, 'Stagiaire mis à jour')
    }

    const showStagiaireDeleted = (nom, prenom) => {
        return showSuccess(`Le stagiaire "${prenom} ${nom}" a été supprimé avec succès du système.`, 'Stagiaire supprimé')
    }

    const showDemandeSubmitted = (codeSuivi) => {
        return showSuccess(`Votre demande de stage a été soumise avec succès. Code de suivi : ${codeSuivi}. Conservez ce code pour suivre l'évolution de votre demande.`, 'Demande soumise')
    }

    const showDemandeApproved = (codeSuivi) => {
        return showSuccess(`La demande de stage ${codeSuivi} a été approuvée avec succès. Le stagiaire sera notifié automatiquement.`, 'Demande approuvée')
    }

    const showDemandeRejected = (codeSuivi, motif) => {
        return showSuccess(`La demande de stage ${codeSuivi} a été rejetée. Motif : ${motif}. Le stagiaire sera notifié automatiquement.`, 'Demande rejetée')
    }

    const showDataExported = (type, nombre) => {
        return showSuccess(`Export réussi : ${nombre} enregistrement(s) de type "${type}" ont été exportés avec succès.`, 'Export réussi')
    }

    const showDataImported = (type, nombre) => {
        return showSuccess(`Import réussi : ${nombre} enregistrement(s) de type "${type}" ont été importés avec succès.`, 'Import réussi')
    }

    const showSettingsSaved = () => {
        return showSuccess('Tous les paramètres ont été sauvegardés avec succès et sont maintenant actifs.', 'Paramètres sauvegardés')
    }
    
    // Messages d'erreur professionnels
    const showError = (message, title = 'Erreur', duration = 6000) => {
        const id = ++messageId
        const errorMessage = {
            id,
            title,
            message,
            duration,
            show: true,
            type: 'error'
        }

        successMessages.value.push(errorMessage)

        setTimeout(() => {
            removeMessage(id)
        }, duration)

        return id
    }

    const showValidationError = (errors) => {
        const errorList = Object.values(errors).flat().join('<br>')
        return showError(`Veuillez corriger les erreurs suivantes :<br>${errorList}`, 'Erreur de validation')
    }

    const showNetworkError = () => {
        return showError('Une erreur de connexion s\'est produite. Veuillez vérifier votre connexion internet et réessayer.', 'Erreur de connexion')
    }

    const showPermissionError = () => {
        return showError('Vous n\'avez pas les permissions nécessaires pour effectuer cette action.', 'Accès refusé')
    }

    return {
        successMessages,
        showSuccess,
        showError,
        removeMessage,
        clearAllMessages,
        // Messages prédéfinis de succès
        showProfileUpdated,
        showPasswordChanged,
        showUserCreated,
        showUserUpdated,
        showUserDeleted,
        showAgentCreated,
        showAgentUpdated,
        showAgentDeleted,
        showStructureCreated,
        showStructureUpdated,
        showStructureDeleted,
        showStagiaireCreated,
        showStagiaireUpdated,
        showStagiaireDeleted,
        showDemandeSubmitted,
        showDemandeApproved,
        showDemandeRejected,
        showDataExported,
        showDataImported,
        showSettingsSaved,
        // Messages d'erreur prédéfinis
        showValidationError,
        showNetworkError,
        showPermissionError
    }
}
