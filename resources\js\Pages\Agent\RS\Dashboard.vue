<template>
  <Head title="Dashboard RS" />

  <RSLayout :notifications="notifications">
    <template #header>
      <div class="flex items-center gap-4 mb-2">
        <div class="bg-gradient-to-br from-green-600 via-green-700 to-emerald-600 text-white rounded-2xl w-14 h-14 flex items-center justify-center shadow-xl shadow-green-500/30">
          <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
          </svg>
        </div>
        <div>
          <h1 class="text-3xl font-black bg-gradient-to-r from-green-800 to-green-600 bg-clip-text text-transparent leading-tight">
            Tableau de bord
          </h1>
          <p class="text-base text-green-600 mt-1 font-mono bg-green-50 px-3 py-1 rounded-lg inline-block">Espace RS</p>
        </div>
      </div>
    </template>

    <!-- Arrière-plan professionnel harmonieux -->
    <div class="min-h-screen bg-gradient-to-br from-green-50/40 via-slate-50/60 to-emerald-50/50">
      <div class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
          <!-- Message d'erreur amélioré -->
          <div v-if="error" class="bg-gradient-to-r from-red-100 to-rose-100 border-2 border-red-300/50 text-red-800 p-6 rounded-3xl shadow-xl backdrop-blur-sm" role="alert">
            <div class="flex items-center gap-4">
              <div class="flex-shrink-0 p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl shadow-lg">
                <svg class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-bold text-red-900">Erreur détectée</h3>
                <p class="text-base mt-1 font-medium">{{ error }}</p>
              </div>
            </div>
          </div>

          <!-- Information de la structure - thème vert harmonisé -->
          <div v-if="structure" class="bg-gradient-to-br from-green-50 via-emerald-50 to-green-50 shadow-2xl rounded-3xl overflow-hidden border-2 border-green-200/50 backdrop-blur-sm">
            <div class="bg-gradient-to-r from-green-600 via-green-700 to-emerald-700 py-6 px-8 relative overflow-hidden">
              <div class="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
              <div class="absolute inset-0 opacity-20">
                <div class="w-full h-full" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23ffffff&quot; fill-opacity=&quot;0.05&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;4&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"></div>
              </div>
              <div class="relative">
                <h3 class="text-xl font-bold text-white flex items-center gap-3">
                  <div class="p-2 bg-white/20 rounded-xl backdrop-blur-sm shadow-lg">
                    <svg class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                      <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                      <line x1="6" y1="6" x2="6" y2="6"></line>
                      <line x1="6" y1="18" x2="6" y2="18"></line>
                    </svg>
                  </div>
                  Votre Structure
                </h3>
              </div>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="group hover:scale-105 transition-all duration-300">
                  <div class="bg-white/80 rounded-2xl p-6 border-2 border-green-200/50 hover:border-green-300/50 hover:shadow-xl transition-all duration-300 shadow-lg">
                    <p class="text-sm font-bold text-green-700 mb-2 uppercase tracking-wide">Sigle</p>
                    <p class="text-2xl font-black text-slate-800">{{ structure.sigle }}</p>
                  </div>
                </div>
                <div class="group hover:scale-105 transition-all duration-300">
                  <div class="bg-white/80 rounded-2xl p-6 border-2 border-green-200/50 hover:border-green-300/50 hover:shadow-xl transition-all duration-300 shadow-lg">
                    <p class="text-sm font-bold text-green-700 mb-2 uppercase tracking-wide">Libellé</p>
                    <p class="text-lg font-bold text-slate-800">{{ structure.libelle }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Cartes statistiques professionnelles -->
          <div class="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3 mb-6 md:mb-8">
            <!-- Demandes en attente -->
            <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-xl shadow-sm border border-amber-200/50 p-3 sm:p-4 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="bg-gradient-to-br from-amber-500 via-orange-500 to-amber-600 rounded-xl p-3 shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <p class="text-sm font-semibold text-amber-700 mb-1 uppercase tracking-wide">
                    En attente
                  </p>
                  <div class="flex items-center justify-between">
                    <p class="text-2xl font-bold text-amber-900">
                      {{ stats.demandesEnAttente }}
                    </p>
                    <div class="text-amber-600 bg-amber-100 rounded-full p-1">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Demandes acceptées -->
            <div class="bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl shadow-sm border border-emerald-200/50 p-3 sm:p-4 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="bg-gradient-to-br from-emerald-500 via-green-500 to-emerald-600 rounded-xl p-3 shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <p class="text-sm font-semibold text-emerald-700 mb-1 uppercase tracking-wide">
                    Acceptées
                  </p>
                  <div class="flex items-center justify-between">
                    <p class="text-2xl font-bold text-emerald-900">
                      {{ stats.demandesAcceptees }}
                    </p>
                    <div class="text-emerald-600 bg-emerald-100 rounded-full p-1">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Demandes rejetées -->
            <div class="bg-gradient-to-br from-red-50 to-rose-50 rounded-xl shadow-sm border border-red-200/50 p-3 sm:p-4 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="bg-gradient-to-br from-red-500 via-rose-500 to-red-600 rounded-xl p-3 shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <p class="text-sm font-semibold text-red-700 mb-1 uppercase tracking-wide">
                    Rejetées
                  </p>
                  <div class="flex items-center justify-between">
                    <p class="text-2xl font-bold text-red-900">
                      {{ stats.demandesRejetees }}
                    </p>
                    <div class="text-red-600 bg-red-100 rounded-full p-1">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Panneau d'activités récentes -->
          <div class="bg-gradient-to-br from-green-50/60 to-slate-50/80 rounded-xl shadow-sm border border-green-200/30 overflow-hidden">
            <div class="px-6 py-4 border-b border-green-200/30 bg-gradient-to-r from-green-50/40 to-slate-50/60">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="bg-gradient-to-br from-green-600 via-green-700 to-emerald-700 text-white rounded-xl w-10 h-10 flex items-center justify-center shadow-lg">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-slate-800">Dernières demandes</h3>
                    <p class="text-xs text-slate-600 font-medium">Activité récente</p>
                  </div>
                </div>
                <Link
                  :href="route('agent.rs.demandes')"
                  class="text-green-600 hover:text-green-700 font-medium text-sm flex items-center gap-1 transition-colors"
                >
                  <span>Voir toutes</span>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                  </svg>
                </Link>
              </div>
            </div>

            <div v-if="dernieresDemandes.length === 0" class="p-12 text-center">
              <div class="max-w-sm mx-auto">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune demande récente</h3>
                <p class="text-gray-500">Les nouvelles demandes de stage apparaîtront ici dès qu'elles seront soumises</p>
              </div>
            </div>

            <div v-else class="overflow-x-auto">
              <table class="min-w-full divide-y divide-slate-200">
                <thead class="bg-gradient-to-r from-slate-100/50 to-gray-100/50">
                  <tr>
                    <th scope="col" class="px-3 sm:px-6 py-3 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Stagiaire</th>
                    <th scope="col" class="hidden sm:table-cell px-6 py-3 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Date de soumission</th>
                    <th scope="col" class="hidden md:table-cell px-6 py-3 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Date d'affectation</th>
                    <th scope="col" class="px-3 sm:px-6 py-3 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Statut</th>
                  </tr>
                </thead>
                <tbody class="bg-gradient-to-br from-white/80 to-slate-50/60 divide-y divide-slate-200">
                  <tr v-for="demande in dernieresDemandes" :key="demande.id" class="hover:bg-slate-50/50 transition-colors duration-200">
                    <td class="px-3 sm:px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-blue-600 text-white flex items-center justify-center font-medium text-xs sm:text-sm">
                          {{ getInitials(demande.stagiaire.user.nom, demande.stagiaire.user.prenom) }}
                        </div>
                        <div class="ml-3 sm:ml-4">
                          <div class="text-sm font-medium text-gray-900">
                            {{ demande.stagiaire.user.nom }} {{ demande.stagiaire.user.prenom }}
                          </div>
                          <div class="text-xs sm:text-sm text-gray-500 sm:hidden">{{ formatDate(demande.created_at) }}</div>
                          <div class="hidden sm:block text-sm text-gray-500">{{ demande.stagiaire.user.email }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">
                        {{ formatDate(demande.created_at) }}
                      </div>
                    </td>
                    <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap">
                      <div v-if="demande.date_affectation" class="text-sm text-gray-900">
                        {{ formatDate(demande.date_affectation) }}
                      </div>
                      <div v-else class="text-sm text-gray-400 italic">
                        Non affectée
                      </div>
                    </td>
                    <td class="px-3 sm:px-6 py-4 whitespace-nowrap">
                      <span :class="getStatusClass(demande.statut) + ' px-2 py-1 rounded-full text-xs font-medium'">
                        {{ demande.statut }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </RSLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import RSLayout from '@/Layouts/RSLayout.vue';

defineProps({
  stats: Object,
  structure: Object,
  dernieresDemandes: Array,
  error: String,
  notifications: Array, // AMÉLIORATION URGENTE : Ajout notifications
});

function formatDate(date) {
  return new Date(date).toLocaleString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

// Fonction améliorée pour les couleurs de statut
function getEnhancedStatusClass(status) {
  switch (status) {
    case 'En attente':
      return 'bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 border-2 border-amber-300/50 shadow-amber-200/50';
    case 'En cours':
      return 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-2 border-blue-300/50 shadow-blue-200/50';
    case 'Acceptée':
      return 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-2 border-emerald-300/50 shadow-emerald-200/50';
    case 'Refusée':
      return 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border-2 border-red-300/50 shadow-red-200/50';
    default:
      return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-2 border-gray-300/50 shadow-gray-200/50';
  }
}

function getStatusClass(status) {
  switch (status) {
    case 'En attente':
      return 'bg-yellow-100 text-yellow-800';
    case 'En cours':
      return 'bg-blue-100 text-blue-800';
    case 'Acceptée':
      return 'bg-green-100 text-green-800';
    case 'Refusée':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

function getStatusDotClass(status) {
  switch (status) {
    case 'En attente':
      return 'bg-amber-500';
    case 'En cours':
      return 'bg-blue-500';
    case 'Acceptée':
      return 'bg-emerald-500';
    case 'Refusée':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
}

function getInitials(nom, prenom) {
  return `${prenom.charAt(0)}${nom.charAt(0)}`.toUpperCase();
}
</script>