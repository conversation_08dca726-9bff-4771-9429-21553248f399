<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\Http;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): Response
    {
        return Inertia::render('Auth/Login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => session('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        // Désactivation temporaire du reCAPTCHA pour le développement local sans connexion Internet
        /*
        $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => 'VOTRE_CLÉ_SECRÈTE', // Remplace par ta clé secrète
            'response' => $request->recaptcha,
            'remoteip' => $request->ip(),
        ]);

        if (!$response->json('success')) {
            return back()->withErrors(['recaptcha' => 'Veuillez valider le reCAPTCHA.']);
        }
        */

        $request->authenticate();

        $request->session()->regenerate();

        // Supprimer toutes les autres sessions de cet utilisateur pour forcer une session unique
        $userId = Auth::id();
        $currentSessionId = $request->session()->getId();

        DB::table('sessions')
            ->where('user_id', $userId)
            ->where('id', '!=', $currentSessionId)
            ->delete();

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        // Redirection vers la page de login au lieu de la homepage
        return redirect()->route('login')->with('status', 'Vous avez été déconnecté avec succès.');
    }
}
