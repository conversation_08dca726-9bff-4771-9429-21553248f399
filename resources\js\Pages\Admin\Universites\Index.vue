<template>
  <AdminLayout>
    <template #header>
      <div class="flex items-center gap-3 mb-2">
        <div class="bg-gradient-to-br from-emerald-600 via-emerald-700 to-green-600 text-white rounded-xl w-12 h-12 flex items-center justify-center shadow-lg">
          <AcademicCapIcon class="w-6 h-6" />
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800">
            Gestion des Universités
          </h1>
          <p class="text-sm text-gray-600 mt-1">Administration des établissements universitaires</p>
        </div>
      </div>
    </template>

    <AdminToast ref="toastRef" />

    <div class="space-y-6">
      <!-- Statistiques -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-emerald-100">
              <AcademicCapIcon class="w-6 h-6 text-emerald-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Universités</p>
              <p class="text-2xl font-bold text-gray-900">{{ universites.length }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <CheckCircleIcon class="w-6 h-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Actives</p>
              <p class="text-2xl font-bold text-gray-900">{{ universitesActives }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100">
              <UserIcon class="w-6 h-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Avec Responsable</p>
              <p class="text-2xl font-bold text-gray-900">{{ universitesAvecResponsable }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100">
              <ExclamationTriangleIcon class="w-6 h-6 text-orange-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Sans Responsable</p>
              <p class="text-2xl font-bold text-gray-900">{{ universitesAvecResponsable - universites.length }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions et filtres -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex items-center gap-4">
            <div class="relative">
              <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                v-model="searchTerm"
                type="text"
                placeholder="Rechercher une université..."
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 w-64"
              />
            </div>
            <select
              v-model="statusFilter"
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            >
              <option value="">Tous les statuts</option>
              <option value="active">Actives</option>
              <option value="inactive">Inactives</option>
            </select>
          </div>
          <Link
            :href="route('admin.universites.create')"
            class="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <PlusIcon class="w-5 h-5" />
            Nouvelle Université
          </Link>
        </div>
      </div>

      <!-- Liste des universités -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 class="text-lg font-semibold text-gray-900">Liste des Universités</h3>
          <p class="text-sm text-gray-600 mt-1">Gestion complète des établissements universitaires</p>
        </div>

        <div v-if="filteredUniversites.length > 0" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Université
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Localisation
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Responsable
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="universite in filteredUniversites" :key="universite.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                        <AcademicCapIcon class="h-5 w-5 text-emerald-600" />
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ universite.nom_complet }}</div>
                      <div class="text-sm text-gray-500">{{ universite.sigle }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ universite.localisation }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div v-if="universite.responsable" class="text-sm text-gray-900">
                    {{ universite.responsable.user.prenom }} {{ universite.responsable.user.nom }}
                  </div>
                  <div v-else class="text-sm text-gray-500 italic">Non assigné</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      universite.active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ universite.active ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end gap-2">
                    <Link
                      :href="route('admin.universites.show', universite.id)"
                      class="text-blue-600 hover:text-blue-900 p-1 rounded"
                      title="Voir les détails"
                    >
                      <EyeIcon class="w-4 h-4" />
                    </Link>
                    <Link
                      :href="route('admin.universites.edit', universite.id)"
                      class="text-emerald-600 hover:text-emerald-900 p-1 rounded"
                      title="Modifier"
                    >
                      <PencilIcon class="w-4 h-4" />
                    </Link>
                    <button
                      @click="toggleActive(universite)"
                      :class="[
                        'p-1 rounded',
                        universite.active
                          ? 'text-orange-600 hover:text-orange-900'
                          : 'text-green-600 hover:text-green-900'
                      ]"
                      :title="universite.active ? 'Désactiver' : 'Activer'"
                    >
                      <component :is="universite.active ? XMarkIcon : CheckIcon" class="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-else class="text-center py-12">
          <AcademicCapIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune université</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{ searchTerm || statusFilter ? 'Aucune université ne correspond aux critères de recherche.' : 'Commencez par créer une nouvelle université.' }}
          </p>
          <div v-if="!searchTerm && !statusFilter" class="mt-6">
            <Link
              :href="route('admin.universites.create')"
              class="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <PlusIcon class="w-5 h-5" />
              Nouvelle Université
            </Link>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';
import { Link, router } from '@inertiajs/vue3';
import { computed, ref } from 'vue';
import {
  AcademicCapIcon,
  CheckCircleIcon,
  UserIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  universites: Array
});

const toastRef = ref(null);
const searchTerm = ref('');
const statusFilter = ref('');

const universitesActives = computed(() => {
  return props.universites.filter(u => u.active).length;
});

const universitesAvecResponsable = computed(() => {
  return props.universites.filter(u => u.responsable).length;
});

const filteredUniversites = computed(() => {
  let filtered = props.universites;

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase();
    filtered = filtered.filter(u => 
      u.nom_complet.toLowerCase().includes(search) ||
      u.sigle.toLowerCase().includes(search) ||
      u.localisation.toLowerCase().includes(search)
    );
  }

  if (statusFilter.value) {
    filtered = filtered.filter(u => 
      statusFilter.value === 'active' ? u.active : !u.active
    );
  }

  return filtered;
});

const toggleActive = (universite) => {
  router.post(route('admin.universites.toggle-active', universite.id), {}, {
    preserveScroll: true,
    onSuccess: () => {
      if (toastRef.value) {
        const status = !universite.active ? 'activée' : 'désactivée';
        toastRef.value.show(`Université ${status} avec succès`, 'success');
      }
    }
  });
};
</script>
