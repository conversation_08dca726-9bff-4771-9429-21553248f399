<template>
    <div class="flex h-screen ms-bg">
        <!-- SIDEBAR STATIQUE -->
        <aside
            :class="[
                'sidebar-glass text-white transition-all duration-300 ease-in-out flex flex-col shadow-lg z-30',
                'relative flex-shrink-0',
                sidebarExpanded ? 'w-64' : 'w-20'
            ]"
        >
            <!-- Header du Sidebar -->
            <div class="p-4 border-b border-blue-500/30">
                <div class="flex items-center justify-between">
                    <Link :href="route('agent.ms.dashboard')" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center shadow-sm">
                            <span class="text-gray-600 font-bold text-lg">GS</span>
                        </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" key="logo-text" class="transition-all duration-300">
                            <h1 class="text-lg font-bold text-white">Ma<PERSON>tre de Stage</h1>
                            <p class="text-xs text-blue-200">Ministère des Finances</p>
                        </div>
                        </transition>
                    </Link>
                    <button
                        @click="toggleSidebar"
                        class="p-2 rounded-lg hover:bg-blue-500/30 transition-colors duration-200"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 p-4 space-y-2">
                <Link
                    v-for="item in menuItems"
                    :key="item.route"
                    :href="route(item.route)"
                    :class="[
                        'flex items-center rounded-xl text-sm font-medium transition-all duration-200 group relative',
                        sidebarExpanded ? 'px-4 py-3' : 'justify-center py-3 mx-2',
                        isActive(item.active)
                            ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/10'
                            : 'text-blue-100 hover:bg-white/10 hover:text-white hover:shadow-md'
                    ]"
                >
                    <!-- Indicateur actif -->
                    <div
                        v-if="isActive(item.active)"
                        class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full"
                    ></div>

                    <!-- Icône -->
                    <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                    </svg>

                    <!-- Texte du menu -->
                    <transition name='fade' mode='out-in'>
                        <span v-if="sidebarExpanded" class="ml-3 transition-all duration-300">{{ item.name }}</span>
                    </transition>

                    <!-- Tooltip pour sidebar réduite -->
                    <div
                        v-if="!sidebarExpanded"
                        class="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
                    >
                        {{ item.name }}
                    </div>
                </Link>
            </nav>

            <!-- Avatar utilisateur -->
            <div class="p-4 border-t border-blue-500/30">
                <button
                    @click="toggleUserMenu"
                    :class="[
                        'w-full flex items-center text-left transition-all duration-200 rounded-xl p-2 hover:bg-white/10',
                        sidebarExpanded ? 'space-x-3' : 'justify-center'
                    ]"
                >
                    <div class="relative flex-shrink-0">
                        <img
                            v-if="user && user.avatar"
                            :src="'/storage/' + user.avatar"
                            alt="Photo de profil"
                            class="w-8 h-8 rounded-full object-cover border-2 border-blue-300"
                        />
                            <div v-else class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold border-2 border-blue-300">
                            {{ user?.nom?.charAt(0) || 'M' }}
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-blue-600 rounded-full"></div>
                    </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" class="ml-3 text-left flex-1" key="user-info">
                        <div class="font-medium text-white text-sm">{{ user?.nom || 'Maître de Stage' }}</div>
                        <div class="text-xs text-blue-200 truncate">Maître de Stage</div>
                    </div>
                        </transition>
                        <transition name='fade' mode='out-in'>
                          <svg v-if="sidebarExpanded" class="w-4 h-4 text-blue-200 ml-2 transition-transform" :class="{ 'rotate-180': showUserMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                          </svg>
                        </transition>
                </button>

                <!-- Menu utilisateur -->
                <transition name="slide-up">
                    <div v-if="showUserMenu" class="absolute bottom-full left-0 right-0 mb-2 mx-2 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden">
                        <div class="p-2">
                            <Link
                                :href="route('profile.edit')"
                                class="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                                @click="showUserMenu = false"
                            >
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profil
                            </Link>
                            <Link
                                :href="route('logout')"
                                method="post"
                                as="button"
                                class="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                @click="showUserMenu = false"
                            >
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Déconnexion
                            </Link>
                        </div>
                    </div>
                </transition>
                    </div>
                </div>
            </div>

        </aside>

        <!-- Overlay mobile -->
        <div
            v-if="sidebarExpanded"
            @click="toggleSidebar"
            class="sidebar-overlay lg:hidden"
            :class="{ 'show': sidebarExpanded }"
        ></div>

        <!-- MAIN CONTENT AREA -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Header glassmorphism -->
            <header class="header-glass shadow-sm border-b border-gray-200 z-20">
                <div class="flex items-center justify-between px-4 md:px-6 py-3 md:py-4 mx-auto max-w-6xl">
                    <div class="flex items-center space-x-4">
                        <!-- Bouton toggle mobile -->
                        <button
                            @click="toggleSidebar"
                            class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                        >
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>

                        <!-- Logo Ministère -->
                        <div class="flex-shrink-0">
                            <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-2 rounded-lg border border-blue-200/50 shadow-sm">
                                <img
                                    src="/images/logoministere.png"
                                    alt="Logo du Ministère"
                                    class="h-8 w-auto object-contain"
                                />
                            </div>
                        </div>
                        <div class="hidden md:block w-px h-8 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                        <!-- Titre et description -->
                        <div class="hidden sm:block">
                            <h1 class="text-xl md:text-2xl font-bold text-gray-800 mb-1">Maître de Stage</h1>
                            <p class="text-gray-600 text-xs md:text-sm">Gestion des Stages - Ministère des Finances</p>
                        </div>

                        <!-- Version mobile du titre -->
                        <div class="sm:hidden">
                            <h1 class="text-lg font-bold text-gray-800">MS</h1>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <!-- Cloche de notifications -->
                        <NotificationBell
                            :notifications="$page.props.notifications || []"
                            :notifications-route="route('agent.ms.notifications.index')"
                        />

                        <div class="hidden lg:flex items-center space-x-3">
                            <div class="flex items-center space-x-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-full text-sm font-medium border border-blue-200">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <span>Espace MS</span>
                            </div>
                        </div>
                        <div class="lg:hidden bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                <span>Espace MS</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Contenu principal -->
            <main class="flex-1 overflow-auto">
                <!-- En-tête de la page -->
                <header v-if="$slots.header" class="bg-white shadow-sm border-b border-gray-200">
                    <div class="px-4 py-6 sm:px-6 lg:px-8">
                        <slot name="header" />
                    </div>
                </header>

                <!-- Contenu de la page -->
                <div class="p-4 sm:p-6 lg:p-8">
                    <slot />
                </div>
            </main>
        </div>


    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link, usePage } from '@inertiajs/vue3'
import NotificationBell from '@/Components/NotificationBell.vue'

// État de l'interface
const sidebarExpanded = ref(true)
const showUserMenu = ref(false)

// Données utilisateur
const page = usePage()
const user = computed(() => page.props.auth.user)

// Menu items pour MS
const menuItems = computed(() => [
    {
        name: 'Tableau de bord',
        route: 'agent.ms.dashboard',
        active: 'agent.ms.dashboard',
        icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'
    },
    {
        name: 'Mes Stages',
        route: 'agent.ms.stages',
        active: 'agent.ms.stages*',
        icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2'
    }
])

// Fonctions
const toggleSidebar = () => {
    sidebarExpanded.value = !sidebarExpanded.value
}

const toggleUserMenu = () => {
    showUserMenu.value = !showUserMenu.value
}

const isActive = (routePattern) => {
    return route().current(routePattern)
}


</script>

<style scoped>
/* Arrière-plan MS */
.ms-bg {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Sidebar glassmorphism */
.sidebar-glass {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.95) 0%,
        rgba(37, 99, 235, 0.98) 50%,
        rgba(29, 78, 216, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 25px 45px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Header glassmorphism */
.header-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Overlay mobile */
.sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 20;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Animations */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
    transition: all 0.3s ease;
}

.slide-up-enter-from, .slide-up-leave-to {
    opacity: 0;
    transform: translateY(10px);
}



</style>