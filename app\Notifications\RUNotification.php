<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RUNotification extends Notification
{
    use Queueable;

    protected $message;
    protected $actionUrl;
    protected $type;
    protected $metadata;

    /**
     * Create a new notification instance.
     */
    public function __construct($message, $actionUrl = null, $type = 'evaluation', $metadata = [])
    {
        $this->message = $message;
        $this->actionUrl = $actionUrl;
        $this->type = $type;
        $this->metadata = $metadata;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mailMessage = (new MailMessage)
            ->subject($this->getSubject())
            ->greeting('Bonjour ' . $notifiable->prenom . ' ' . $notifiable->nom)
            ->line($this->message);

        if ($this->actionUrl) {
            $mailMessage->action($this->getActionText(), $this->actionUrl);
        }

        return $mailMessage->line('Merci de traiter cette demande dans les meilleurs délais.')
            ->salutation('Cordialement, Le système de gestion des stages');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message' => $this->message,
            'action_url' => $this->actionUrl,
            'type' => $this->type,
            'metadata' => $this->metadata,
            'icon' => $this->getIcon(),
            'priority' => $this->getPriority()
        ];
    }

    /**
     * Obtenir le sujet de l'email selon le type
     */
    private function getSubject(): string
    {
        switch ($this->type) {
            case 'evaluation':
                return '📋 Évaluation académique à valider';
            case 'inscription':
                return '🎓 Nouvelle inscription d\'étudiant';
            case 'demande':
                return '📝 Nouvelle demande de stage';
            case 'urgent':
                return '🚨 Action urgente requise';
            default:
                return '🔔 Notification RU';
        }
    }

    /**
     * Obtenir le texte du bouton d'action
     */
    private function getActionText(): string
    {
        switch ($this->type) {
            case 'evaluation':
                return 'Valider l\'évaluation';
            case 'inscription':
                return 'Voir les inscriptions';
            case 'demande':
                return 'Voir les demandes';
            default:
                return 'Voir les détails';
        }
    }

    /**
     * Obtenir l'icône selon le type
     */
    private function getIcon(): string
    {
        switch ($this->type) {
            case 'evaluation':
                return '📋';
            case 'inscription':
                return '🎓';
            case 'demande':
                return '📝';
            case 'urgent':
                return '🚨';
            default:
                return '🔔';
        }
    }

    /**
     * Obtenir la priorité selon le type
     */
    private function getPriority(): string
    {
        switch ($this->type) {
            case 'urgent':
                return 'high';
            case 'evaluation':
                return 'medium';
            default:
                return 'normal';
        }
    }
}
