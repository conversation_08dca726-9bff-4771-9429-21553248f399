<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\DemandeStage;

class StagiaireRefusDefinitifMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $demande;
    public $stagiaire;
    public $motifRefus;

    /**
     * Create a new message instance.
     */
    public function __construct(DemandeStage $demande, string $motifRefus)
    {
        $this->demande = $demande;
        $this->stagiaire = $demande->stagiaire->user;
        $this->motifRefus = $motifRefus;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Décision concernant votre demande de stage - ' . $this->demande->code_suivi,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.stagiaire-refus-definitif',
            with: [
                'demande' => $this->demande,
                'stagiaire' => $this->stagiaire,
                'motifRefus' => $this->motifRefus,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
