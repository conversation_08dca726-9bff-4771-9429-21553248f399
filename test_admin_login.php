<?php

/**
 * Script de test pour vérifier les utilisateurs admin créés
 * À exécuter dans Tinker : php artisan tinker
 * Puis : include 'test_admin_login.php';
 */

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "=== Test des utilisateurs administrateurs ===\n\n";

// Tester les deux admins
$adminEmails = ['<EMAIL>', '<EMAIL>'];
$password = 'password123';

foreach ($adminEmails as $email) {
    echo "🔍 Test de l'utilisateur : $email\n";
    
    $user = User::where('email', $email)->first();
    
    if (!$user) {
        echo "❌ Utilisateur non trouvé !\n\n";
        continue;
    }
    
    echo "✅ Utilisateur trouvé :\n";
    echo "   - ID: {$user->id}\n";
    echo "   - Nom complet: {$user->prenom} {$user->nom}\n";
    echo "   - Email: {$user->email}\n";
    echo "   - Rôle: {$user->role}\n";
    echo "   - Téléphone: {$user->telephone}\n";
    echo "   - Adresse: {$user->adresse}\n";
    
    // Test du mot de passe
    if (Hash::check($password, $user->password)) {
        echo "✅ Mot de passe correct\n";
    } else {
        echo "❌ Mot de passe incorrect\n";
    }
    
    // Test des rôles Spatie
    try {
        if (method_exists($user, 'hasRole')) {
            if ($user->hasRole('admin')) {
                echo "✅ Rôle Spatie 'admin' confirmé\n";
            } else {
                echo "❌ Rôle Spatie 'admin' manquant\n";
            }
            
            $roles = $user->getRoleNames();
            echo "   - Tous les rôles Spatie: " . $roles->implode(', ') . "\n";
        }
    } catch (Exception $e) {
        echo "⚠️  Erreur lors de la vérification des rôles Spatie: " . $e->getMessage() . "\n";
    }
    
    // Test des méthodes de vérification de rôle
    if (method_exists($user, 'isAdmin')) {
        if ($user->isAdmin()) {
            echo "✅ Méthode isAdmin() retourne true\n";
        } else {
            echo "❌ Méthode isAdmin() retourne false\n";
        }
    }
    
    echo "\n";
}

echo "=== Résumé des tests ===\n";
$totalAdmins = User::where('role', 'admin')->count();
echo "Nombre total d'administrateurs dans la base : $totalAdmins\n";

if ($totalAdmins >= 2) {
    echo "✅ Test réussi ! Les deux administrateurs sont prêts à être utilisés.\n";
    echo "\n📋 Informations de connexion :\n";
    echo "- <EMAIL> / password123\n";
    echo "- <EMAIL> / password123\n";
    echo "\n🌐 URL de connexion : http://127.0.0.1:8000/login\n";
} else {
    echo "❌ Test échoué ! Nombre d'administrateurs insuffisant.\n";
}

echo "\n=== Test terminé ===\n";
