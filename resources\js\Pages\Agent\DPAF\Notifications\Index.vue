<template>
  <AgentDPAF>
    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-2xl font-bold text-gray-900">
                Notifications DPAF
              </h2>
              
              <div class="flex space-x-3">
                <button
                  @click="markAllAsRead"
                  :disabled="!hasUnreadNotifications"
                  class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Tout marquer comme lu
                </button>
              </div>
            </div>

            <!-- Liste des notifications -->
            <div v-if="notifications.data.length > 0" class="space-y-4">
              <div
                v-for="notification in notifications.data"
                :key="notification.id"
                :class="[
                  'p-4 rounded-lg border transition-colors duration-200',
                  notification.read_at 
                    ? 'bg-gray-50 border-gray-200' 
                    : 'bg-blue-50 border-blue-200'
                ]"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div
                        v-if="!notification.read_at"
                        class="w-2 h-2 bg-blue-500 rounded-full"
                      ></div>
                      <h3 class="text-sm font-medium text-gray-900">
                        {{ getNotificationTitle(notification) }}
                      </h3>
                      <span class="text-xs text-gray-500">
                        {{ formatDate(notification.created_at) }}
                      </span>
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-2">
                      {{ getNotificationMessage(notification) }}
                    </p>
                    
                    <div class="flex items-center space-x-3">
                      <button
                        v-if="!notification.read_at"
                        @click="markAsRead(notification.id)"
                        class="text-xs text-blue-600 hover:text-blue-800"
                      >
                        Marquer comme lu
                      </button>
                      
                      <button
                        @click="deleteNotification(notification.id)"
                        class="text-xs text-red-600 hover:text-red-800"
                      >
                        Supprimer
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Message si aucune notification -->
            <div v-else class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune notification</h3>
              <p class="mt-1 text-sm text-gray-500">
                Vous n'avez aucune notification pour le moment.
              </p>
            </div>

            <!-- Pagination -->
            <div v-if="notifications.data.length > 0" class="mt-6">
              <nav class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                  <Link
                    v-if="notifications.prev_page_url"
                    :href="notifications.prev_page_url"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Précédent
                  </Link>
                  <Link
                    v-if="notifications.next_page_url"
                    :href="notifications.next_page_url"
                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Suivant
                  </Link>
                </div>
                
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700">
                      Affichage de
                      <span class="font-medium">{{ notifications.from || 0 }}</span>
                      à
                      <span class="font-medium">{{ notifications.to || 0 }}</span>
                      sur
                      <span class="font-medium">{{ notifications.total || 0 }}</span>
                      résultats
                    </p>
                  </div>
                  
                  <div v-if="notifications.last_page > 1">
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <Link
                        v-if="notifications.prev_page_url"
                        :href="notifications.prev_page_url"
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                      >
                        <span class="sr-only">Précédent</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </Link>
                      
                      <Link
                        v-if="notifications.next_page_url"
                        :href="notifications.next_page_url"
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                      >
                        <span class="sr-only">Suivant</span>
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                      </Link>
                    </nav>
                  </div>
                </div>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AgentDPAF>
</template>

<script setup>
import { computed } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AgentDPAF from '@/Layouts/AgentDPAF.vue'

const props = defineProps({
  notifications: Object
})

const hasUnreadNotifications = computed(() => {
  return props.notifications.data.some(notification => !notification.read_at)
})

const markAsRead = (notificationId) => {
  router.post(route('agent.dpaf.notifications.markAsRead', notificationId), {}, {
    preserveScroll: true,
    onSuccess: () => {
      // La page se rechargera automatiquement
    }
  })
}

const markAllAsRead = () => {
  router.post(route('agent.dpaf.notifications.markAllAsRead'), {}, {
    preserveScroll: true,
    onSuccess: () => {
      // La page se rechargera automatiquement
    }
  })
}

const deleteNotification = (notificationId) => {
  if (confirm('Êtes-vous sûr de vouloir supprimer cette notification ?')) {
    router.delete(route('agent.dpaf.notifications.destroy', notificationId), {
      preserveScroll: true,
      onSuccess: () => {
        // La page se rechargera automatiquement
      }
    })
  }
}

const getNotificationTitle = (notification) => {
  const data = notification.data || {}
  return data.title || 'Notification DPAF'
}

const getNotificationMessage = (notification) => {
  const data = notification.data || {}
  return data.message || 'Nouvelle notification pour l\'agent DPAF'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
