<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * CORRECTION CRITIQUE: Standardiser sur 'Professionnel' (sans 'e')
     */
    public function up(): void
    {
        echo "🔧 CORRECTION CRITIQUE - Standardisation sur 'Professionnel'\n";

        // Étape 1: Vérifier la structure actuelle
        $columns = DB::select("SHOW COLUMNS FROM demande_stages WHERE Field = 'type'");

        if (!empty($columns)) {
            $currentType = $columns[0]->Type;
            echo "📋 Type actuel: " . $currentType . "\n";

            // Étape 2: Élargir temporairement l'ENUM
            echo "🔄 Élargissement temporaire de l'ENUM...\n";
            DB::statement("ALTER TABLE demande_stages MODIFY COLUMN type ENUM('Académique', 'Professionnelle', 'Professionnel') DEFAULT 'Académique'");
            echo "✅ ENUM élargi temporairement.\n";

            // Étape 3: Mettre à jour les données existantes
            $count = DB::table('demande_stages')->where('type', 'Professionnelle')->count();
            if ($count > 0) {
                echo "🔄 Mise à jour de {$count} enregistrements 'Professionnelle' → 'Professionnel'...\n";
                DB::table('demande_stages')
                    ->where('type', 'Professionnelle')
                    ->update(['type' => 'Professionnel']);
                echo "✅ Données mises à jour.\n";
            }

            // Étape 4: Finaliser avec les valeurs correctes
            echo "🎯 Finalisation avec les valeurs correctes...\n";
            DB::statement("ALTER TABLE demande_stages MODIFY COLUMN type ENUM('Académique', 'Professionnel') DEFAULT 'Académique'");
            echo "✅ CORRECTION TERMINÉE - Colonne 'type' standardisée sur 'Professionnel'.\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revenir à l'état précédent si nécessaire
        Schema::table('demande_stages', function (Blueprint $table) {
            $table->enum('type', ['Académique', 'Professionnel'])->default('Académique')->change();
        });
    }
};
