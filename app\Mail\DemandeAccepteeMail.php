<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class DemandeAccepteeMail extends Mailable
{
    use Queueable, SerializesModels;

    public $stagiaire;
    public $demande;

    /**
     * Create a new message instance.
     */
    public function __construct($stagiaire, $demande)
    {
        $this->stagiaire = $stagiaire;
        $this->demande = $demande;
    }

    public function build()
    {
        return $this->view('emails.demande_acceptee')
            ->subject('🎉 Votre demande de stage a été acceptée - MEF Bénin')
            ->with([
                'stagiaire' => $this->stagiaire,
                'demande' => $this->demande,
            ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
