<template>
  <Head title="Détails de la structure" />

  <AdminLayout>
    <div class="py-8">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- En-tête avec fil d'Ariane -->
        <div class="mb-6">
          <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
              <li class="inline-flex items-center">
                <Link :href="route('admin.structures.index')"
                      class="text-sm font-medium text-gray-700 hover:text-blue-600">
                  Structures
                </Link>
              </li>
              <li v-if="structure.parent">
                <div class="flex items-center">
                  <svg class="w-3 h-3 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-500">{{ structure.parent.sigle || structure.parent.libelle }}</span>
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <svg class="w-3 h-3 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-900">{{ structure.sigle || structure.libelle }}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold text-gray-900 mb-2">
                {{ structure.sigle ? structure.sigle + ' - ' : '' }}{{ structure.libelle }}
              </h1>
              <div class="flex items-center space-x-4">
                <span :class="getStructureTypeClass()" class="inline-flex px-3 py-1 text-xs font-semibold rounded-full">
                  {{ getStructureTypeLabel() }}
                </span>
                <span :class="structure.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                      class="inline-flex px-3 py-1 text-xs font-semibold rounded-full">
                  {{ structure.active ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
            <Link :href="route('admin.structures.index')"
                  class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
              Retour à la liste
            </Link>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Colonne principale -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Informations générales -->
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Informations générales</h3>
              </div>
              <div class="px-6 py-4">
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Sigle</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ structure.sigle || 'Non défini' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Libellé</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ structure.libelle }}</dd>
                  </div>
                  <div class="md:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ structure.description || 'Aucune description disponible' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Type de structure</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ structure.type_structure || 'Non défini' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Niveau hiérarchique</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ structure.niveau || 'Non défini' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Ordre d'affichage</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ structure.ordre || 'Non défini' }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Statut</dt>
                    <dd class="mt-1">
                      <span :class="structure.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                            class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ structure.active ? 'Structure active' : 'Structure inactive' }}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            <!-- Hiérarchie organisationnelle -->
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Hiérarchie organisationnelle</h3>
              </div>
              <div class="px-6 py-4">
                <!-- Structure parente -->
                <div v-if="structure.parent" class="mb-6">
                  <h4 class="text-sm font-medium text-gray-700 mb-3">Structure parente</h4>
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="font-medium text-blue-900">
                          {{ structure.parent.sigle ? structure.parent.sigle + ' - ' : '' }}{{ structure.parent.libelle }}
                        </p>
                        <p v-if="structure.parent.responsable" class="text-sm text-blue-700 mt-1">
                          Responsable: {{ structure.parent.responsable.user.prenom }} {{ structure.parent.responsable.user.nom }}
                        </p>
                      </div>
                      <Link :href="route('admin.structures.show', structure.parent.id)"
                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Voir détails →
                      </Link>
                    </div>
                  </div>
                </div>

                <!-- Sous-structures -->
                <div v-if="structure.children && structure.children.length > 0">
                  <h4 class="text-sm font-medium text-gray-700 mb-3">
                    Sous-structures ({{ structure.children.length }})
                  </h4>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div v-for="child in structure.children" :key="child.id"
                         class="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <p class="font-medium text-green-900">
                            {{ child.sigle ? child.sigle + ' - ' : '' }}{{ child.libelle }}
                          </p>
                          <p v-if="child.responsable" class="text-sm text-green-700 mt-1">
                            Responsable: {{ child.responsable.user.prenom }} {{ child.responsable.user.nom }}
                          </p>
                          <p v-else class="text-sm text-green-600 mt-1 italic">
                            Aucun responsable assigné
                          </p>
                          <div class="flex items-center space-x-4 mt-2">
                            <span class="text-xs text-green-600">
                              {{ child.children_count || 0 }} sous-structure(s)
                            </span>
                            <span class="text-xs text-green-600">
                              {{ child.stagiaires_count || 0 }} stagiaire(s)
                            </span>
                          </div>
                        </div>
                        <Link :href="route('admin.structures.show', child.id)"
                              class="text-green-600 hover:text-green-800 text-sm font-medium ml-4">
                          Voir →
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-else-if="!structure.parent">
                  <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 21V5a2 2 0 012-2h10a2 2 0 012 2v16M7 21h10" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">Cette structure de direction générale n'a pas encore de sous-structures.</p>
                  </div>
                </div>

                <div v-else>
                  <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 21V5a2 2 0 012-2h10a2 2 0 012 2v16M7 21h10" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">Cette sous-structure n'a pas de sous-structures enfants.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Colonne latérale -->
          <div class="space-y-6">
            <!-- Responsable -->
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Responsable</h3>
              </div>
              <div class="px-6 py-4">
                <div v-if="structure.responsable">
                  <div class="flex items-center space-x-4 mb-4">
                    <div class="flex-shrink-0">
                      <div class="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    </div>
                    <div>
                      <p class="font-medium text-gray-900">
                        {{ structure.responsable.user.prenom }} {{ structure.responsable.user.nom }}
                      </p>
                      <p class="text-sm text-gray-500">{{ structure.responsable.fonction || 'Fonction non définie' }}</p>
                    </div>
                  </div>
                  <dl class="space-y-3">
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Email</dt>
                      <dd class="mt-1 text-sm text-gray-900">
                        <a :href="'mailto:' + structure.responsable.user.email"
                           class="text-blue-600 hover:text-blue-800">
                          {{ structure.responsable.user.email }}
                        </a>
                      </dd>
                    </div>
                    <div>
                      <dt class="text-sm font-medium text-gray-500">Matricule</dt>
                      <dd class="mt-1 text-sm text-gray-900">{{ structure.responsable.matricule }}</dd>
                    </div>
                    <div v-if="structure.responsable.date_embauche">
                      <dt class="text-sm font-medium text-gray-500">Date d'embauche</dt>
                      <dd class="mt-1 text-sm text-gray-900">{{ formatDate(structure.responsable.date_embauche) }}</dd>
                    </div>
                  </dl>
                </div>
                <div v-else class="text-center py-8">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <p class="mt-2 text-sm text-gray-500">Aucun responsable assigné à cette structure</p>
                </div>
              </div>
            </div>

            <!-- Statistiques -->
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Statistiques</h3>
              </div>
              <div class="px-6 py-4">
                <div class="grid grid-cols-2 gap-4">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ structure.children ? structure.children.length : 0 }}</div>
                    <div class="text-xs text-gray-500">Sous-structures</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ structure.stagiaires_count || 0 }}</div>
                    <div class="text-xs text-gray-500">Stagiaires</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ structure.stages_count || 0 }}</div>
                    <div class="text-xs text-gray-500">Stages</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">{{ structure.demandes_count || 0 }}</div>
                    <div class="text-xs text-gray-500">Demandes</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Informations système -->
            <div class="bg-white shadow-sm rounded-lg border border-gray-200">
              <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Informations système</h3>
              </div>
              <div class="px-6 py-4">
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Date de création</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ formatDate(structure.created_at) }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Dernière modification</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ formatDate(structure.updated_at) }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">ID Structure</dt>
                    <dd class="mt-1 text-sm text-gray-900 font-mono">#{{ structure.id }}</dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';

const props = defineProps({
  structure: {
    type: Object,
    required: true
  }
});

// Fonction pour déterminer le type de structure et sa classe CSS
function getStructureTypeClass() {
  if (!props.structure.parent_id) {
    return 'bg-blue-100 text-blue-800';
  } else {
    return 'bg-green-100 text-green-800';
  }
}

// Fonction pour obtenir le libellé du type de structure
function getStructureTypeLabel() {
  if (!props.structure.parent_id) {
    return 'Structure de direction générale';
  } else {
    return 'Sous-structure';
  }
}

// Fonction pour formater les dates
function formatDate(dateString) {
  if (!dateString) return 'Non définie';

  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}
</script>
