<?php

use Illuminate\Support\Facades\Route;
use App\Models\Stage;
use App\Models\Evaluation;
use App\Models\FormatEvaluationUniversite;
use App\Models\Universite;
use App\Models\Agent;
use App\Models\Stagiaire;

// Route de test pour le système d'évaluation complet
Route::get('/test-evaluation-system', function () {
    
    // Créer des données de test
    $testData = [
        'stages_professionnels' => [],
        'stages_academiques' => [],
        'formats_universites' => [],
        'evaluations_test' => []
    ];

    // 1. Créer des stages professionnels de test
    $stagesPro = Stage::where('type', 'professionnel')->take(3)->get();
    foreach ($stagesPro as $stage) {
        $testData['stages_professionnels'][] = [
            'id' => $stage->id,
            'stagiaire' => $stage->stagiaire?->user?->nom . ' ' . $stage->stagiaire?->user?->prenom,
            'structure' => $stage->structure?->libelle,
            'type' => $stage->type,
            'evaluation_existante' => $stage->evaluation ? 'Oui' : 'Non',
            'note' => $stage->evaluation?->note_totale ?? 'N/A'
        ];
    }

    // 2. Créer des stages académiques de test
    $stagesAcad = Stage::where('type', 'academique')->take(3)->get();
    foreach ($stagesAcad as $stage) {
        $universite = $stage->stagiaire?->universite_id ? 
            Universite::find($stage->stagiaire->universite_id) : null;
        
        $testData['stages_academiques'][] = [
            'id' => $stage->id,
            'stagiaire' => $stage->stagiaire?->user?->nom . ' ' . $stage->stagiaire?->user?->prenom,
            'structure' => $stage->structure?->libelle,
            'universite' => $universite?->nom_complet ?? 'Non définie',
            'type' => $stage->type,
            'evaluation_existante' => $stage->evaluation ? 'Oui' : 'Non',
            'note' => $stage->evaluation?->note_totale ?? 'N/A',
            'validee_ru' => $stage->evaluation?->validee_par_ru ? 'Oui' : 'Non'
        ];
    }

    // 3. Lister les formats d'évaluation par université
    $formats = FormatEvaluationUniversite::with('universite')->get();
    foreach ($formats as $format) {
        $testData['formats_universites'][] = [
            'id' => $format->id,
            'universite' => $format->universite?->nom_complet,
            'nombre_criteres' => $format->nombre_criteres,
            'points_par_critere' => $format->points_par_critere,
            'total_points' => $format->nombre_criteres * $format->points_par_critere,
            'criteres' => $format->criteres,
            'actif' => $format->actif ? 'Oui' : 'Non'
        ];
    }

    // 4. Statistiques des évaluations
    $stats = [
        'total_evaluations' => Evaluation::count(),
        'evaluations_professionnelles' => Evaluation::whereHas('stage', function($q) {
            $q->where('type', 'professionnel');
        })->count(),
        'evaluations_academiques' => Evaluation::whereHas('stage', function($q) {
            $q->where('type', 'academique');
        })->count(),
        'evaluations_validees_ru' => Evaluation::where('validee_par_ru', true)->count(),
        'evaluations_en_attente_ru' => Evaluation::whereHas('stage', function($q) {
            $q->where('type', 'academique');
        })->where('validee_par_ru', false)->count(),
        'moyenne_generale' => round(Evaluation::avg('note_totale'), 2),
        'universites_avec_format' => FormatEvaluationUniversite::distinct('universite_id')->count(),
        'agents_ru' => Agent::where('role_agent', 'RU')->count()
    ];

    // 5. Tests de workflow
    $workflowTests = [
        [
            'test' => 'Stage professionnel - Évaluation immédiatement visible',
            'description' => 'Un MS évalue un stage professionnel → Le stagiaire voit sa note immédiatement',
            'statut' => 'À tester manuellement',
            'etapes' => [
                '1. MS accède à un stage professionnel',
                '2. MS remplit l\'évaluation avec critères standards (10 × 2 pts)',
                '3. MS soumet l\'évaluation',
                '4. Stagiaire reçoit notification et voit sa note'
            ]
        ],
        [
            'test' => 'Stage académique - Première évaluation université',
            'description' => 'Un MS évalue pour la première fois un étudiant d\'une université → Choix du format',
            'statut' => 'À tester manuellement',
            'etapes' => [
                '1. MS accède à un stage académique (université sans format)',
                '2. MS choisit format (10×2, 5×4, ou 2×10)',
                '3. MS définit les intitulés des critères',
                '4. MS sauvegarde le format',
                '5. MS évalue avec ce format',
                '6. RU reçoit notification'
            ]
        ],
        [
            'test' => 'Stage académique - Format existant',
            'description' => 'Un MS évalue un étudiant d\'une université avec format existant',
            'statut' => 'À tester manuellement',
            'etapes' => [
                '1. MS accède à un stage académique (université avec format)',
                '2. Système propose le format existant',
                '3. MS peut utiliser ou créer nouveau format',
                '4. MS évalue selon le format choisi',
                '5. RU reçoit notification'
            ]
        ],
        [
            'test' => 'RU - Validation des notes',
            'description' => 'Un RU valide les évaluations de son université',
            'statut' => 'À tester manuellement',
            'etapes' => [
                '1. RU accède à son dashboard',
                '2. RU voit les évaluations en attente',
                '3. RU consulte une évaluation',
                '4. RU valide l\'évaluation',
                '5. Stagiaire reçoit notification et voit sa note'
            ]
        ]
    ];

    return view('test-evaluation-system', [
        'testData' => $testData,
        'stats' => $stats,
        'workflowTests' => $workflowTests
    ]);
});

// Route pour tester la création d'un format d'évaluation
Route::post('/test-create-format', function () {
    try {
        // Trouver une université sans format
        $universite = Universite::whereDoesntHave('formatsEvaluation')->first();
        
        if (!$universite) {
            return response()->json(['error' => 'Aucune université sans format trouvée']);
        }

        // Créer un format de test
        $format = FormatEvaluationUniversite::create([
            'universite_id' => $universite->id,
            'nombre_criteres' => 5,
            'points_par_critere' => 4,
            'criteres' => [
                'Maîtrise technique',
                'Capacité d\'analyse',
                'Travail en équipe',
                'Communication',
                'Initiative'
            ],
            'cree_par_agent_id' => 1, // Agent de test
            'actif' => true
        ]);

        return response()->json([
            'success' => true,
            'format' => $format,
            'message' => 'Format de test créé avec succès'
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Erreur lors de la création du format de test',
            'details' => $e->getMessage()
        ]);
    }
});

// Route pour tester une évaluation académique
Route::post('/test-evaluation-academique', function () {
    try {
        // Trouver un stage académique
        $stage = Stage::where('type', 'academique')
            ->whereHas('stagiaire', function($q) {
                $q->whereNotNull('universite_id');
            })
            ->first();

        if (!$stage) {
            return response()->json(['error' => 'Aucun stage académique trouvé']);
        }

        // Trouver ou créer un format pour cette université
        $format = FormatEvaluationUniversite::getFormatActifPourUniversite($stage->stagiaire->universite_id);
        
        if (!$format) {
            $format = FormatEvaluationUniversite::create([
                'universite_id' => $stage->stagiaire->universite_id,
                'nombre_criteres' => 5,
                'points_par_critere' => 4,
                'criteres' => [
                    'Compétences techniques',
                    'Autonomie',
                    'Respect des délais',
                    'Qualité du travail',
                    'Intégration équipe'
                ],
                'cree_par_agent_id' => 1,
                'actif' => true
            ]);
        }

        // Créer une évaluation de test
        $evaluation = Evaluation::create([
            'stage_id' => $stage->id,
            'agent_id' => 1, // Agent de test
            'criteres_personnalises' => [3.5, 4.0, 3.0, 3.5, 4.0], // Notes de test
            'format_evaluation_universite_id' => $format->id,
            'note_totale' => 18.0,
            'commentaire_general' => 'Excellent stagiaire, très motivé et compétent.',
            'date_evaluation' => now(),
            'validee_par_ru' => false
        ]);

        return response()->json([
            'success' => true,
            'evaluation' => $evaluation,
            'format' => $format,
            'stage' => $stage,
            'message' => 'Évaluation académique de test créée avec succès'
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Erreur lors de la création de l\'évaluation de test',
            'details' => $e->getMessage()
        ]);
    }
});

// Route pour tester la validation RU
Route::post('/test-validation-ru', function () {
    try {
        // Trouver une évaluation académique non validée
        $evaluation = Evaluation::whereHas('stage', function($q) {
            $q->where('type', 'academique');
        })
        ->where('validee_par_ru', false)
        ->first();

        if (!$evaluation) {
            return response()->json(['error' => 'Aucune évaluation en attente de validation trouvée']);
        }

        // Trouver un agent RU
        $ru = Agent::where('role_agent', 'RU')->first();
        
        if (!$ru) {
            return response()->json(['error' => 'Aucun agent RU trouvé']);
        }

        // Valider l'évaluation
        $evaluation->update([
            'validee_par_ru' => true,
            'date_validation_ru' => now(),
            'ru_validateur_id' => $ru->id,
            'commentaire_ru' => 'Évaluation validée lors des tests système.'
        ]);

        return response()->json([
            'success' => true,
            'evaluation' => $evaluation,
            'ru' => $ru,
            'message' => 'Validation RU de test effectuée avec succès'
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Erreur lors de la validation RU de test',
            'details' => $e->getMessage()
        ]);
    }
});
