<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\DemandeStage;
use App\Models\Universite;

class DemandeController extends Controller
{
    /**
     * Affiche la liste des demandes de stage des étudiants de l'université
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        // Récupérer les demandes de stage des étudiants de cette université
        $query = DemandeStage::with(['stagiaire.user', 'structure', 'maitreStage.user'])
            ->whereHas('stagiaire.user', function ($q) use ($universite) {
                $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                    $subQ->where('universite', $universite->nom_complet);
                });
            });

        // Filtres
        if ($request->filled('statut')) {
            $query->where('statut', $request->statut);
        }

        if ($request->filled('type_stage')) {
            $query->where('type_stage', $request->type_stage);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('stagiaire.user', function ($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                  ->orWhere('prenom', 'like', "%{$search}%");
            });
        }

        $demandes = $query->orderBy('created_at', 'desc')->paginate(15);

        return Inertia::render('Agent/RU/Demandes/Index', [
            'pageTitle' => 'Demandes de Stage',
            'universite' => $universite,
            'demandes' => $demandes,
            'filters' => $request->only(['statut', 'type_stage', 'search']),
        ]);
    }

    /**
     * Affiche les détails d'une demande de stage
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        // Récupérer la demande avec toutes les relations
        $demande = DemandeStage::with([
            'stagiaire.user', 
            'structure', 
            'maitreStage.user',
            'documents'
        ])
        ->whereHas('stagiaire.user', function ($q) use ($universite) {
            $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                $subQ->where('universite', $universite->nom_complet);
            });
        })
        ->findOrFail($id);

        return Inertia::render('Agent/RU/Demandes/Show', [
            'pageTitle' => 'Détails Demande',
            'universite' => $universite,
            'demande' => $demande,
        ]);
    }
}
