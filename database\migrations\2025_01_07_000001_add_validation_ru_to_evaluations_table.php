<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migration chirurgicale pour ajouter la validation RU aux évaluations académiques
     * ZÉRO IMPACT sur les données existantes
     */
    public function up(): void
    {
        Schema::table('evaluations', function (Blueprint $table) {
            // Champ pour indiquer si l'évaluation a été validée par le RU
            $table->boolean('validee_par_ru')->default(false)->after('date_evaluation');
            
            // Date de validation par le RU
            $table->timestamp('date_validation_ru')->nullable()->after('validee_par_ru');
            
            // ID de l'agent RU qui a validé l'évaluation
            $table->foreignId('ru_validateur_id')->nullable()->constrained('agents')->onDelete('set null')->after('date_validation_ru');
            
            // Commentaire du RU lors de la validation (optionnel)
            $table->text('commentaire_ru')->nullable()->after('ru_validateur_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('evaluations', function (Blueprint $table) {
            $table->dropForeign(['ru_validateur_id']);
            $table->dropColumn([
                'validee_par_ru',
                'date_validation_ru', 
                'ru_validateur_id',
                'commentaire_ru'
            ]);
        });
    }
};
