<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="show"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick"
      >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm"></div>
        
        <!-- Modal Container -->
        <div class="flex min-h-full items-center justify-center p-4">
          <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 scale-95 translate-y-4"
            enter-to-class="opacity-100 scale-100 translate-y-0"
            leave-active-class="transition-all duration-200 ease-in"
            leave-from-class="opacity-100 scale-100 translate-y-0"
            leave-to-class="opacity-0 scale-95 translate-y-4"
          >
            <div
              v-if="show"
              class="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl border border-gray-100"
              @click.stop
            >
              <!-- Header avec icône -->
              <div :class="headerClasses">
                <div class="flex items-center gap-4">
                  <div :class="iconContainerClasses">
                    <component :is="iconComponent" :class="iconClasses" />
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-white">
                      {{ title }}
                    </h3>
                    <p v-if="subtitle" class="text-sm text-white/80 mt-1">
                      {{ subtitle }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Contenu -->
              <div class="p-6">
                <div class="text-gray-700 leading-relaxed">
                  <p class="text-base">{{ message }}</p>
                  
                  <!-- Détails supplémentaires -->
                  <div v-if="details" class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <p class="text-sm text-gray-600 font-medium mb-2">Détails :</p>
                    <p class="text-sm text-gray-700">{{ details }}</p>
                  </div>

                  <!-- Suggestions d'actions -->
                  <div v-if="suggestions && suggestions.length > 0" class="mt-4">
                    <p class="text-sm text-gray-600 font-medium mb-2">Actions recommandées :</p>
                    <ul class="text-sm text-gray-700 space-y-1">
                      <li v-for="suggestion in suggestions" :key="suggestion" class="flex items-start gap-2">
                        <span class="text-blue-600 mt-1">•</span>
                        <span>{{ suggestion }}</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Actions -->
              <div class="px-6 pb-6 flex gap-3 justify-end">
                <button
                  v-if="showSecondaryAction"
                  @click="handleSecondaryAction"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                  {{ secondaryActionText }}
                </button>
                
                <button
                  @click="handlePrimaryAction"
                  :class="primaryButtonClasses"
                >
                  {{ primaryActionText }}
                </button>
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'info', // 'info', 'warning', 'error', 'success'
    validator: (value) => ['info', 'warning', 'error', 'success'].includes(value)
  },
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    required: true
  },
  details: {
    type: String,
    default: ''
  },
  suggestions: {
    type: Array,
    default: () => []
  },
  primaryActionText: {
    type: String,
    default: 'Compris'
  },
  secondaryActionText: {
    type: String,
    default: 'Plus tard'
  },
  showSecondaryAction: {
    type: Boolean,
    default: false
  },
  closeOnBackdrop: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['primary-action', 'secondary-action', 'close'])

// Classes CSS dynamiques selon le type
const headerClasses = computed(() => {
  const base = 'px-6 py-4 bg-gradient-to-r'
  switch (props.type) {
    case 'error':
      return `${base} from-red-600 to-red-700`
    case 'warning':
      return `${base} from-amber-600 to-amber-700`
    case 'success':
      return `${base} from-green-600 to-green-700`
    case 'info':
    default:
      return `${base} from-blue-600 to-blue-700`
  }
})

const iconContainerClasses = computed(() => {
  return 'p-2 rounded-xl bg-white/20 backdrop-blur-sm'
})

const iconClasses = computed(() => {
  return 'w-6 h-6 text-white'
})

const iconComponent = computed(() => {
  switch (props.type) {
    case 'error':
      return XCircleIcon
    case 'warning':
      return ExclamationTriangleIcon
    case 'success':
      return CheckCircleIcon
    case 'info':
    default:
      return InformationCircleIcon
  }
})

const primaryButtonClasses = computed(() => {
  const base = 'px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors'
  switch (props.type) {
    case 'error':
      return `${base} text-white bg-red-600 hover:bg-red-700 focus:ring-red-500`
    case 'warning':
      return `${base} text-white bg-amber-600 hover:bg-amber-700 focus:ring-amber-500`
    case 'success':
      return `${base} text-white bg-green-600 hover:bg-green-700 focus:ring-green-500`
    case 'info':
    default:
      return `${base} text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500`
  }
})

const handlePrimaryAction = () => {
  emit('primary-action')
  emit('close')
}

const handleSecondaryAction = () => {
  emit('secondary-action')
  emit('close')
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    emit('close')
  }
}
</script>
