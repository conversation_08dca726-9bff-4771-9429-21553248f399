<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Universite extends Model
{
    use HasFactory;

    protected $fillable = [
        'nom_universite',    // Champ legacy - à conserver pour compatibilité
        'nom_complet',       // Nouveau champ principal
        'sigle',
        'description',
        'localisation',
        'active',
        'responsable_id',
        'adresse',           // Champ legacy - à conserver pour compatibilité
    ];

    /**
     * Les attributs qui doivent être castés.
     */
    protected $casts = [
        'active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get all the stagiaires that belong to this universite.
     */
    public function stagiaires(): HasMany
    {
        return $this->hasMany(Stagiaire::class);
    }

    /**
     * Get the agent that is the responsable for this universite.
     */
    public function responsable(): BelongsTo
    {
        return $this->belongsTo(Agent::class, 'responsable_id');
    }

    /**
     * Get all the demandes de stage for this universite.
     */
    public function demandesStages(): HasMany
    {
        return $this->hasMany(DemandeStage::class, 'universite_id');
    }

    /**
     * Scope pour les universités actives uniquement.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Accessor pour obtenir le nom complet ou le nom legacy.
     */
    public function getNomAttribute()
    {
        return $this->nom_complet ?: $this->nom_universite;
    }
}