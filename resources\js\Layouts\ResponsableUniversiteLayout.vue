<template>
    <div class="flex h-screen ru-bg">
        <!-- SIDEBAR STATIQUE -->
        <aside
            :class="[
                'sidebar-glass text-white transition-all duration-300 ease-in-out flex flex-col shadow-lg z-30',
                'relative flex-shrink-0',
                sidebarExpanded ? 'w-64' : 'w-20'
            ]"
        >
            <!-- Header du Sidebar -->
            <div class="p-4 border-b border-blue-500/30">
                <div class="flex items-center justify-between">
                    <Link :href="route('agent.ru.dashboard')" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center shadow-sm">
                            <span class="text-gray-600 font-bold text-lg">GS</span>
                        </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" key="logo-text" class="transition-all duration-300">
                            <div class="text-sm font-bold text-white">Gestion Stages</div>
                            <div class="text-xs text-blue-200">Ministère Économie</div>
                          </div>
                        </transition>
                    </Link>
                    <button @click="toggleSidebar" class="p-2 rounded-lg hover:bg-blue-600/30 transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  :d="sidebarExpanded ? 'M11 19l-7-7 7-7m8 14l-7-7 7-7' : 'M13 5l7 7-7 7M5 5l7 7-7 7'" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                <div v-for="item in menuItems" :key="item.name" class="relative">
                    <Link
                        :href="route(item.route)"
                        :class="[
                            'flex items-center px-3 py-3 rounded-xl text-sm font-medium transition-all duration-200 group',
                            isActive(item.active)
                                ? 'bg-blue-600/40 text-white shadow-lg backdrop-blur-sm border border-blue-400/30'
                                : 'text-blue-100 hover:bg-blue-600/20 hover:text-white'
                        ]"
                    >
                        <svg class="w-5 h-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                        </svg>
                        <transition name='fade' mode='out-in'>
                            <span v-if="sidebarExpanded" class="transition-all duration-300">{{ item.name }}</span>
                        </transition>
                        <div v-if="isActive(item.active)" class="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-blue-300 rounded-l-full"></div>
                    </Link>
                </div>
            </nav>



            <!-- User Info Footer -->
            <div class="p-4">
                <div class="relative">
                    <button @click="toggleUserMenu" class="w-full flex items-center space-x-3 p-3 rounded-xl hover:bg-blue-600/20 transition-colors">
                        <div class="w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center">
                            <span class="text-blue-900 font-bold text-sm">{{ user?.prenom?.charAt(0) }}{{ user?.nom?.charAt(0) }}</span>
                        </div>
                        <transition name='fade' mode='out-in'>
                            <div v-if="sidebarExpanded" class="flex-1 text-left">
                                <div class="text-sm font-semibold text-white">{{ user?.prenom }} {{ user?.nom }}</div>
                                <div class="text-xs text-blue-200">Responsable Université</div>
                            </div>
                        </transition>
                        <svg v-if="sidebarExpanded" class="w-4 h-4 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>

                    <!-- User Menu Dropdown -->
                    <transition name="dropdown">
                        <div v-if="showUserMenu" class="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-xl shadow-xl border border-gray-200 py-2 z-50">
                            <Link :href="route('agent.ru.profile.edit')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 transition-colors">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                Profil
                            </Link>
                            <Link :href="route('logout')" method="post" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Déconnexion
                            </Link>
                        </div>
                    </transition>
                </div>
            </div>
        </aside>

        <!-- CONTENU PRINCIPAL -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm z-20">
                <div class="px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <h1 class="text-xl font-bold text-gray-800">{{ pageTitle || 'Dashboard RU' }}</h1>
                        <div class="text-sm text-gray-500">
                            {{ universiteInfo?.nom_complet || 'Université non assignée' }}
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <!-- Cloche de notifications (repositionnée dans le navbar) -->
                        <div class="relative">
                            <button @click="showNotifications = !showNotifications" class="relative p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none">
                                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                </svg>
                                <span v-if="($page.props.notifications || []).length" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-2 py-1 font-medium shadow-md min-w-[1.25rem] text-center">
                                    {{ ($page.props.notifications || []).length > 99 ? '99+' : ($page.props.notifications || []).length }}
                                </span>
                            </button>

                            <!-- Panel de notifications -->
                            <div v-if="showNotifications" class="absolute top-full right-0 mt-2 w-96 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden animate-fade-in z-50">
                                <div class="p-4 border-b bg-blue-50">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                            </svg>
                                            <h3 class="text-lg font-semibold text-blue-900">Notifications</h3>
                                        </div>
                                        <div class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                                            {{ ($page.props.notifications || []).length }}
                                        </div>
                                    </div>
                                </div>

                                <div v-if="($page.props.notifications || []).length === 0" class="p-8 text-center">
                                    <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414-2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                    </svg>
                                    <p class="text-gray-500 text-sm">Aucune notification</p>
                                </div>

                                <ul v-else class="divide-y divide-gray-100 max-h-96 overflow-y-auto">
                                    <li v-for="notif in ($page.props.notifications || [])" :key="notif.id" class="p-4 hover:bg-gray-50 transition-colors cursor-pointer" @click="markAsRead(notif.id)">
                                        <div class="flex items-start gap-3">
                                            <div class="pt-1 flex-shrink-0">
                                                <div :class="[
                                                    'w-8 h-8 rounded-full flex items-center justify-center',
                                                    getNotificationStyle(notif.data.priority || 'normal')
                                                ]">
                                                    <span class="text-sm">{{ notif.data.icon || '🔔' }}</span>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="text-sm font-medium text-gray-900 mb-1 leading-relaxed" v-html="notif.data.message"></div>
                                                <!-- Métadonnées spécifiques RU -->
                                                <div v-if="notif.data.metadata && notif.data.type === 'evaluation'" class="text-xs text-blue-600 mb-1">
                                                    📊 Note: {{ notif.data.metadata.note_totale }}/20 | 🏢 {{ notif.data.metadata.structure }}
                                                </div>
                                                <div class="text-xs text-gray-500">{{ formatDate(notif.created_at) }}</div>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- User Badge -->
                        <div class="flex items-center space-x-3 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                                </svg>
                            </div>
                            <div class="hidden sm:block">
                                <div class="text-sm font-semibold text-blue-800">Responsable Université</div>
                                <div class="text-xs text-blue-600">{{ user?.email }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- En-tête de la page -->
            <header v-if="$slots.header"
                class="page-header bg-gradient-to-r from-blue-50/50 to-indigo-50/30 border-b border-blue-100/50">
                <div class="px-4 md:px-6 py-3 md:py-4 mx-auto max-w-6xl">
                    <slot name="header" />
                </div>
            </header>

            <!-- Contenu principal -->
            <main class="flex-1 overflow-auto bg-gradient-to-br from-gray-50/30 to-blue-50/20">
                <div class="px-4 md:px-6 py-3 md:py-4">
                    <slot />
                </div>
            </main>
        </div>

        <!-- Messages de succès globaux -->
        <GlobalSuccessMessages />


    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link, usePage, router } from '@inertiajs/vue3'
import GlobalSuccessMessages from '@/Components/GlobalSuccessMessages.vue'

const sidebarExpanded = ref(true)
const showUserMenu = ref(false)
const showNotifications = ref(false) // APPROCHE CHIRURGICALE : État pour les notifications

const page = usePage()
const user = computed(() => page.props.auth?.user)
const universiteInfo = computed(() => page.props.universite)
const pageTitle = computed(() => page.props.pageTitle)

const toggleSidebar = () => {
    sidebarExpanded.value = !sidebarExpanded.value
}

const toggleUserMenu = () => {
    showUserMenu.value = !showUserMenu.value
}

// APPROCHE CHIRURGICALE : Fonctions pour gérer les notifications
const formatDate = (date) => {
    return new Date(date).toLocaleString('fr-FR')
}

const markAsRead = (notificationId) => {
    router.post(route('agent.notifications.markAsRead', notificationId), {}, {
        preserveScroll: true,
    })
}

// APPROCHE CHIRURGICALE : Styles spécifiques selon la priorité des notifications RU
const getNotificationStyle = (priority) => {
    switch (priority) {
        case 'high':
            return 'bg-red-100 text-red-600'
        case 'medium':
            return 'bg-amber-100 text-amber-600'
        default:
            return 'bg-blue-100 text-blue-600'
    }
}

const closeUserMenu = () => {
    showUserMenu.value = false
}

// Menu items pour RU
const menuItems = [
    {
        name: 'Tableau de bord',
        route: 'agent.ru.dashboard',
        icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z',
        active: ['agent.ru.dashboard']
    },
    {
        name: 'Mon Université',
        route: 'agent.ru.universite.show',
        icon: 'M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z',
        active: ['agent.ru.universite.*']
    },
    {
        name: 'Stages',
        route: 'agent.ru.stages.index',
        icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
        active: ['agent.ru.stages.*']
    },
    {
        name: 'Évaluations',
        route: 'agent.ru.evaluations.index',
        icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
        active: ['agent.ru.evaluations.*']
    }
]

const isActive = (routeNames) => {
    return routeNames.some(routeName => route().current(routeName))
}
</script>

<style scoped>
/* Arrière-plan principal */
.ru-bg {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    min-height: 100vh;
}

/* Glassmorphism pour la sidebar */
.sidebar-glass {
    background: linear-gradient(135deg,
        rgba(37, 99, 235, 0.95) 0%,
        rgba(29, 78, 216, 0.98) 50%,
        rgba(30, 64, 175, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 25px 45px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Animations */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

.dropdown-enter-active, .dropdown-leave-active {
    transition: all 0.2s ease;
}
.dropdown-enter-from, .dropdown-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* Header de page */
.page-header {
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(16, 185, 129, 0.1);
}

/* APPROCHE CHIRURGICALE : Animation pour les notifications */
.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
</style>
