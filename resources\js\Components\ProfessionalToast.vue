<template>
  <div class="toast-container fixed top-4 right-4 z-50 w-96 space-y-3">
    <TransitionGroup name="toast" tag="div">
      <div
        v-for="toast in toasts"
        :key="toast.id"
        class="toast-item rounded-lg shadow-lg overflow-hidden border-l-4 flex items-start p-4 bg-white"
        :class="getToastClasses(toast.type)"
      >
        <!-- Icône selon le type -->
        <div class="flex-shrink-0 mr-3 mt-0.5">
          <!-- Icône de succès -->
          <svg v-if="toast.type === 'success'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          
          <!-- Icône d'erreur -->
          <svg v-else-if="toast.type === 'error'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          
          <!-- Icône d'avertissement -->
          <svg v-else-if="toast.type === 'warning'" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          
          <!-- Icône d'information -->
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>

        <!-- Contenu du toast -->
        <div class="flex-1 min-w-0">
          <h4 v-if="toast.title" class="text-sm font-semibold text-gray-900 mb-1">
            {{ toast.title }}
          </h4>
          <div 
            class="text-sm text-gray-700 leading-relaxed"
            v-html="toast.message"
          ></div>
        </div>

        <!-- Bouton de fermeture -->
        <button
          @click="removeToast(toast.id)"
          class="flex-shrink-0 ml-3 text-gray-400 hover:text-gray-600 transition-colors duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { usePage } from '@inertiajs/vue3';

const toasts = ref([]);
let toastIdCounter = 0;

// Fonction pour ajouter un toast
function addToast({ type = 'info', title = '', message = '', duration = 6000 }) {
  const id = toastIdCounter++;
  toasts.value.push({ id, type, title, message });

  // Auto-suppression après la durée spécifiée
  if (duration > 0) {
    setTimeout(() => {
      removeToast(id);
    }, duration);
  }

  return id;
}

// Fonction pour supprimer un toast
function removeToast(id) {
  const index = toasts.value.findIndex(t => t.id === id);
  if (index !== -1) {
    toasts.value.splice(index, 1);
  }
}

// Fonction pour obtenir les classes CSS selon le type
function getToastClasses(type) {
  switch (type) {
    case 'success':
      return 'border-green-500 bg-green-50';
    case 'error':
      return 'border-red-500 bg-red-50';
    case 'warning':
      return 'border-amber-500 bg-amber-50';
    case 'info':
    default:
      return 'border-blue-500 bg-blue-50';
  }
}

// Exposer les fonctions pour utilisation externe
defineExpose({
  addToast,
  removeToast
});

// Gestion des messages flash du serveur
const page = usePage();
const processedFlashMessages = new Set();

function checkFlashMessages() {
  const flash = page.props.flash;
  
  if (flash) {
    if (flash.success && !processedFlashMessages.has(flash.success)) {
      processedFlashMessages.add(flash.success);
      addToast({
        type: 'success',
        title: 'Opération réussie',
        message: flash.success,
        duration: 5000
      });
    }

    if (flash.error && !processedFlashMessages.has(flash.error)) {
      processedFlashMessages.add(flash.error);
      addToast({
        type: 'error',
        title: 'Erreur',
        message: flash.error,
        duration: 6000
      });
    }

    if (flash.warning && !processedFlashMessages.has(flash.warning)) {
      processedFlashMessages.add(flash.warning);
      addToast({
        type: 'warning',
        title: 'Attention',
        message: flash.warning,
        duration: 5000
      });
    }

    if (flash.info && !processedFlashMessages.has(flash.info)) {
      processedFlashMessages.add(flash.info);
      addToast({
        type: 'info',
        title: 'Information',
        message: flash.info,
        duration: 4000
      });
    }
  }
}

onMounted(() => {
  checkFlashMessages();
});
</script>

<style scoped>
/* Animations pour les toasts */
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* Styles pour les liens dans les messages */
.toast-item :deep(a) {
  color: #3b82f6;
  text-decoration: underline;
  font-weight: 500;
}

.toast-item :deep(a:hover) {
  color: #1d4ed8;
}

/* Styles pour le code de suivi */
.toast-item :deep(.code-suivi) {
  font-family: 'Courier New', monospace;
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 600;
  color: #1f2937;
}

/* Responsive */
@media (max-width: 640px) {
  .toast-container {
    width: calc(100% - 2rem);
    right: 1rem;
    left: 1rem;
  }
}
</style>
