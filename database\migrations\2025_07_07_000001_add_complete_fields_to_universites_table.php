<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Ajouter les champs manquants à la table universites pour le système complet
     * APPROCHE CHIRURGICALE : Ajouter uniquement les nouveaux champs requis
     */
    public function up(): void
    {
        Schema::table('universites', function (Blueprint $table) {
            // APPROCHE SÉCURISÉE : Ajouter les nouveaux champs sans renommer pour éviter les conflits
            // Garder nom_universite existant et ajouter nom_complet comme alias
            $table->string('nom_complet')->nullable()->after('nom_universite');
            $table->string('sigle')->nullable()->unique()->after('nom_complet');
            $table->text('description')->nullable()->after('sigle');
            $table->string('localisation')->nullable()->after('description');
            $table->boolean('active')->default(true)->after('localisation');
            $table->foreignId('responsable_id')->nullable()->after('active')
                  ->constrained('agents')->onDelete('set null');
        });

        // Copier les données existantes de nom_universite vers nom_complet
        DB::statement('UPDATE universites SET nom_complet = nom_universite WHERE nom_complet IS NULL');

        // Rendre nom_complet obligatoire après la copie
        Schema::table('universites', function (Blueprint $table) {
            $table->string('nom_complet')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('universites', function (Blueprint $table) {
            // Supprimer les nouveaux champs
            $table->dropForeign(['responsable_id']);
            $table->dropColumn(['nom_complet', 'sigle', 'description', 'localisation', 'active', 'responsable_id']);
        });
    }
};
