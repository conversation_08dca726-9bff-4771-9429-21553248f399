<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="show"
        class="fixed inset-0 z-50 overflow-y-auto"
        @click="handleBackdropClick"
      >
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm"></div>
        
        <!-- Modal Container -->
        <div class="flex min-h-full items-center justify-center p-4">
          <Transition
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 scale-95 translate-y-4"
            enter-to-class="opacity-100 scale-100 translate-y-0"
            leave-active-class="transition-all duration-200 ease-in"
            leave-from-class="opacity-100 scale-100 translate-y-0"
            leave-to-class="opacity-0 scale-95 translate-y-4"
          >
            <div
              v-if="show"
              class="relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl border border-gray-100"
              @click.stop
            >
              <!-- Header avec icône -->
              <div :class="headerClasses">
                <div class="flex items-center gap-4">
                  <div :class="iconContainerClasses">
                    <component :is="iconComponent" :class="iconClasses" />
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-white">
                      {{ title }}
                    </h3>
                    <p v-if="subtitle" class="text-sm text-white/80 mt-1">
                      {{ subtitle }}
                    </p>
                  </div>
                </div>
              </div>

              <!-- Contenu -->
              <div class="p-6">
                <div class="text-gray-700 leading-relaxed">
                  <p class="text-base">{{ message }}</p>
                  
                  <!-- Détails supplémentaires -->
                  <div v-if="details" class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <p class="text-sm text-gray-600 font-medium mb-2">Détails :</p>
                    <p class="text-sm text-gray-700">{{ details }}</p>
                  </div>

                  <!-- Avertissement pour les actions irréversibles -->
                  <div v-if="isDestructive" class="mt-4 p-4 bg-red-50 rounded-lg border border-red-200">
                    <div class="flex items-center gap-2">
                      <ExclamationTriangleIcon class="w-5 h-5 text-red-600 flex-shrink-0" />
                      <p class="text-sm text-red-800 font-medium">
                        Cette action est irréversible et ne peut pas être annulée.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Actions -->
              <div class="px-6 pb-6 flex gap-3 justify-end">
                <button
                  @click="handleCancel"
                  :disabled="loading"
                  class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors disabled:opacity-50"
                >
                  {{ cancelText }}
                </button>
                
                <button
                  @click="handleConfirm"
                  :disabled="loading"
                  :class="confirmButtonClasses"
                >
                  <svg
                    v-if="loading"
                    class="animate-spin -ml-1 mr-2 h-4 w-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ loading ? 'Traitement...' : confirmText }}
                </button>
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { computed, ref } from 'vue'
import {
  ExclamationTriangleIcon,
  TrashIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/vue/24/outline'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'info', // 'info', 'warning', 'danger', 'success'
    validator: (value) => ['info', 'warning', 'danger', 'success'].includes(value)
  },
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    required: true
  },
  details: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: 'Confirmer'
  },
  cancelText: {
    type: String,
    default: 'Annuler'
  },
  isDestructive: {
    type: Boolean,
    default: false
  },
  closeOnBackdrop: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['confirm', 'cancel', 'close'])

const loading = ref(false)

// Classes CSS dynamiques selon le type
const headerClasses = computed(() => {
  const base = 'px-6 py-4 bg-gradient-to-r'
  switch (props.type) {
    case 'danger':
      return `${base} from-red-600 to-red-700`
    case 'warning':
      return `${base} from-amber-600 to-amber-700`
    case 'success':
      return `${base} from-green-600 to-green-700`
    case 'info':
    default:
      return `${base} from-blue-600 to-blue-700`
  }
})

const iconContainerClasses = computed(() => {
  const base = 'p-2 rounded-xl bg-white/20 backdrop-blur-sm'
  return base
})

const iconClasses = computed(() => {
  return 'w-6 h-6 text-white'
})

const iconComponent = computed(() => {
  switch (props.type) {
    case 'danger':
      return TrashIcon
    case 'warning':
      return ExclamationTriangleIcon
    case 'success':
      return CheckCircleIcon
    case 'info':
    default:
      return InformationCircleIcon
  }
})

const confirmButtonClasses = computed(() => {
  const base = 'px-4 py-2 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 flex items-center'
  switch (props.type) {
    case 'danger':
      return `${base} text-white bg-red-600 hover:bg-red-700 focus:ring-red-500`
    case 'warning':
      return `${base} text-white bg-amber-600 hover:bg-amber-700 focus:ring-amber-500`
    case 'success':
      return `${base} text-white bg-green-600 hover:bg-green-700 focus:ring-green-500`
    case 'info':
    default:
      return `${base} text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500`
  }
})

const handleConfirm = async () => {
  loading.value = true
  try {
    await emit('confirm')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  emit('close')
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop && !loading.value) {
    handleCancel()
  }
}
</script>
