<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle demande de stage</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .info-box {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .details-table th {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .footer {
            background-color: #f3f4f6;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
        }
        .urgent {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="{{ asset('images/logo-ministere.png') }}" alt="Ministère de l'Économie et des Finances" class="logo">
            <h1>Nouvelle demande de stage</h1>
            <p>Système de gestion des stages</p>
        </div>

        <div class="content">
            <div class="urgent">
                <h3>🔔 Action requise</h3>
                <p>Une nouvelle demande de stage vient d'être soumise et nécessite votre traitement.</p>
            </div>

            <h2>Détails de la demande</h2>
            
            <table class="details-table">
                <tr>
                    <th>Code de suivi</th>
                    <td><strong>{{ $demande->code_suivi }}</strong></td>
                </tr>
                <tr>
                    <th>Stagiaire</th>
                    <td>{{ $stagiaire->prenom }} {{ $stagiaire->nom }}</td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td>{{ $stagiaire->email }}</td>
                </tr>
                <tr>
                    <th>Type de stage</th>
                    <td>{{ $demande->type }}</td>
                </tr>
                <tr>
                    <th>Nature</th>
                    <td>{{ $demande->nature }}</td>
                </tr>
                <tr>
                    <th>Structure souhaitée</th>
                    <td>{{ $demande->structureSouhaitee->libelle ?? 'Non spécifiée' }}</td>
                </tr>
                <tr>
                    <th>Période souhaitée</th>
                    <td>
                        Du {{ \Carbon\Carbon::parse($demande->date_debut_souhaitee)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                        au {{ \Carbon\Carbon::parse($demande->date_fin_souhaitee)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                    </td>
                </tr>
                <tr>
                    <th>Date de soumission</th>
                    <td>{{ \Carbon\Carbon::parse($demande->created_at)->locale('fr')->isoFormat('DD MMMM YYYY à HH:mm') }}</td>
                </tr>
            </table>

            @if($demande->nature === 'Groupe')
                <div class="info-box">
                    <h4>📋 Demande de groupe</h4>
                    <p>Cette demande concerne un groupe de stagiaires. Veuillez examiner tous les membres du groupe lors du traitement.</p>
                </div>
            @endif

            <div class="info-box">
                <h4>📝 Prochaines étapes</h4>
                <ul>
                    <li>Examiner la demande et les documents fournis</li>
                    <li>Vérifier la disponibilité de la structure souhaitée</li>
                    <li>Assigner la demande à une structure appropriée</li>
                    <li>Suivre la réponse de la structure</li>
                </ul>
            </div>

            <div style="text-align: center;">
                <a href="{{ route('agent.dpaf.demandes.show', $demande->id) }}" class="btn">
                    Traiter la demande
                </a>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Ministère de l'Économie et des Finances du Bénin</strong><br>
                Direction de la Planification et de l'Administration Financière (DPAF)<br>
                Système de gestion des stages
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                Cet email a été envoyé automatiquement. Merci de ne pas répondre à cette adresse.
            </p>
        </div>
    </div>
</body>
</html>
