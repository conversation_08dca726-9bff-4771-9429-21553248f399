<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer la table si elle existe déjà (pour éviter les conflits)
        Schema::dropIfExists('formats_evaluation_universites');
        
        Schema::create('formats_evaluation_universites', function (Blueprint $table) {
            $table->id();
            
            // Référence à l'université
            $table->unsignedBigInteger('universite_id');
            
            // Configuration du format d'évaluation
            $table->integer('nombre_criteres')->comment('Nombre de critères: 2, 5 ou 10');
            $table->integer('points_par_critere')->comment('Points par critère: 10, 4 ou 2');
            $table->json('criteres')->comment('Intitulés des critères personnalisés');
            
            // Métadonnées
            $table->unsignedBigInteger('cree_par_agent_id')->comment('Agent qui a créé ce format');
            $table->boolean('actif')->default(true)->comment('Format actif ou archivé');
            
            // Timestamps
            $table->timestamps();
            
            // Index pour optimiser les performances
            $table->index(['universite_id', 'actif'], 'idx_universite_actif');
            $table->index('cree_par_agent_id', 'idx_createur');
            $table->index('created_at', 'idx_date_creation');
            
            // Contraintes de clés étrangères
            $table->foreign('universite_id')
                  ->references('id')
                  ->on('universites')
                  ->onDelete('cascade')
                  ->name('fk_format_universite');
                  
            $table->foreign('cree_par_agent_id')
                  ->references('id')
                  ->on('agents')
                  ->onDelete('cascade')
                  ->name('fk_format_createur');
        });
        
        // Ajouter des contraintes de validation au niveau base de données
        DB::statement('ALTER TABLE formats_evaluation_universites ADD CONSTRAINT chk_nombre_criteres CHECK (nombre_criteres IN (2, 5, 10))');
        DB::statement('ALTER TABLE formats_evaluation_universites ADD CONSTRAINT chk_points_par_critere CHECK (points_par_critere IN (2, 4, 10))');
        DB::statement('ALTER TABLE formats_evaluation_universites ADD CONSTRAINT chk_total_points CHECK ((nombre_criteres * points_par_critere) = 20)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('formats_evaluation_universites');
    }
};
