<template>
  <AdminLayout>
    <div class="py-6">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header professionnel -->
        <div class="mb-8">
          <div class="flex items-center gap-3 mb-4">
            <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-12 h-12 flex items-center justify-center shadow-lg">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-800">
                Gestion des Stagiaires
              </h1>
              <p class="text-sm text-gray-600 mt-1">Liste complète des stagiaires ({{ stagiaires.length }} stagiaires)</p>
            </div>
          </div>
        </div>
        <!-- Carte liste stagiaires professionnelle -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center gap-3">
              <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-10 h-10 flex items-center justify-center shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Liste des Stagiaires</h3>
                <p class="text-sm text-gray-600 mt-1">Tous les stagiaires du système</p>
              </div>
            </div>
          </div>
          <div v-if="stagiaires.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun stagiaire</h3>
            <p class="mt-1 text-sm text-gray-500">Aucun stagiaire n'a été ajouté au système pour le moment.</p>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stagiaire</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Formation</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Université</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-100">
                <tr v-for="stagiaire in stagiaires" :key="stagiaire.id_stagiaire" class="hover:bg-gray-50 transition">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center gap-3">
                      <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {{ (stagiaire.user?.prenom?.charAt(0) || '') + (stagiaire.user?.nom?.charAt(0) || '') }}
                      </div>
                      <div>
                        <div class="text-sm font-semibold text-slate-900">{{ stagiaire.user?.prenom }} {{ stagiaire.user?.nom }}</div>
                        <div class="text-sm text-slate-600">{{ stagiaire.niveau_etude || 'Non défini' }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    <div>
                      <div class="font-medium">{{ stagiaire.user?.email || '-' }}</div>
                      <div class="text-gray-500">{{ stagiaire.user?.telephone || '-' }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    <div>
                      <div class="font-medium">{{ stagiaire.filiere || 'Non défini' }}</div>
                      <div class="text-gray-500">{{ stagiaire.niveau_etude || 'Non défini' }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {{ stagiaire.universite || 'Non défini' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-center">
                    <button @click="viewStagiaire(stagiaire)"
                      class="inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium text-sm">
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                      </svg>
                      Voir détails
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Modale pour voir les détails du stagiaire -->
        <div v-if="isModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <!-- En-tête de la modale -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 class="text-xl font-bold text-gray-800">
                Détails du stagiaire
              </h3>
              <button @click="closeModal" class="text-gray-500 hover:text-gray-700 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <!-- Contenu de la modale -->
            <div class="px-6 py-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p class="text-sm font-medium text-gray-500">Nom</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.user?.nom || '-' }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Prénom</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.user?.prenom || '-' }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Email</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.user?.email || '-' }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Téléphone</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.user?.telephone || '-' }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Date de naissance</p>
                  <p class="text-lg font-medium text-gray-900">
                    {{ formatDate(currentStagiaire?.user?.date_de_naissance) || '-' }}
                  </p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Sexe</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.user?.sexe || '-' }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Niveau d'étude</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.niveau_etude || '-' }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Université</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.universite || '-' }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">Filière</p>
                  <p class="text-lg font-medium text-gray-900">{{ currentStagiaire?.filiere || '-' }}</p>
                </div>
              </div>
            </div>

            <!-- Bouton pour fermer la modale -->
            <div class="flex justify-end p-4 border-t mt-6">
              <button type="button" @click="closeModal()"
                class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M18 6 6 18M6 6l12 12" />
                </svg>
                Fermer
              </button>
            </div>
          </div>
        </div>

        <!-- Composant Toast pour les notifications -->
        <AdminToast ref="toast" />
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { usePage } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';

const props = defineProps({
  stagiaires: Array
});

const page = usePage();
const toast = ref(null);
const isModalOpen = ref(false);
const currentStagiaire = ref(null);
// Dans le script du composant
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR'); // Format: JJ/MM/AAAA
};

// Surveiller les messages flash et les afficher automatiquement
onMounted(() => {
  // Vérifier si des messages flash existent au chargement
  setTimeout(() => {
    const { flash } = page.props;
    if (flash) {
      if (flash.success && toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Succès',
          message: flash.success
        });
      }

      if (flash.error && toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur',
          message: flash.error
        });
      }
    }
  }, 100);
});

const viewStagiaire = (stagiaire) => {
  currentStagiaire.value = stagiaire;
  isModalOpen.value = true;
};

const closeModal = () => {
  isModalOpen.value = false;
  currentStagiaire.value = null;
};
</script>