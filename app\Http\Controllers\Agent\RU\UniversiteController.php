<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\Universite;

class UniversiteController extends Controller
{
    /**
     * Affiche les détails de l'université dont l'agent RU est responsable
     */
    public function show()
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::with('responsable.user')->find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        return Inertia::render('Agent/RU/Universite/Show', [
            'pageTitle' => 'Mon Université',
            'universite' => $universite,
            'agent' => $agent->load('user'),
        ]);
    }

    /**
     * Met à jour les informations de l'université (seul le RU responsable peut modifier)
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('agent.ru.dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;

        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')->with('error', 'Aucune université assignée.');
        }

        // Validation des données (suppression du champ contact)
        $validated = $request->validate([
            'nom_complet' => 'required|string|max:255|unique:universites,nom_complet,' . $universite->id,
            'sigle' => 'required|string|max:50|unique:universites,sigle,' . $universite->id,
            'description' => 'nullable|string|max:1000',
            'localisation' => 'required|string|max:255',
        ]);

        // Mettre à jour l'université
        $universite->update($validated);

        // Log de l'action
        \Log::info('Université modifiée par RU', [
            'universite_id' => $universite->id,
            'agent_ru_id' => $agent->id,
            'modifications' => $validated
        ]);

        return redirect()->route('agent.ru.universite.show')->with('success', 'Université mise à jour avec succès.');
    }
}
