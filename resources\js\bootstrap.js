import axios from 'axios';
window.axios = axios;

// Configuration automatique du token CSRF
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Récupération automatique du token CSRF depuis la meta tag
const token = document.head.querySelector('meta[name="csrf-token"]');

if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Intercepteur pour gérer les erreurs 419 (Page Expired)
window.axios.interceptors.response.use(
    response => response,
    error => {
        if (error.response && error.response.status === 419) {
            // Token CSRF expiré, recharger la page
            console.warn('Token CSRF expiré, rechargement de la page...');
            window.location.reload();
        }
        return Promise.reject(error);
    }
);
