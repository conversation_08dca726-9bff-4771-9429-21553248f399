<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attestation d'Effectivité de Stage</title>
    <style>
        /* SPÉCIFICATIONS TECHNIQUES EXACTES SELON MODÈLE OFFICIEL */
        @page {
            size: A4;
            margin: 2cm 1.5cm;
        }

        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 14pt;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
            background: white;
        }

        .no-print {
            display: none;
        }

        @media screen {
            .no-print {
                display: block;
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 1000;
                background: #4F46E5;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            }

            body {
                padding: 20px;
                background: #f5f5f5;
            }

            .attestation-container {
                background: white;
                box-shadow: 0 0 15px rgba(0,0,0,0.1);
                max-width: 21cm;
                margin: 0 auto;
                padding: 2cm 1.5cm;
            }
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .attestation-container {
                padding: 0;
                margin: 0;
                box-shadow: none;
                background: white;
            }

            body {
                background: white;
                padding: 0;
            }
        }

        /* EN-TÊTE CONFORME AU MODÈLE OFFICIEL - LAYOUT VERTICAL */
        .header-officiel {
            display: block;
            margin-bottom: 30px;
            width: 100%;
            position: relative;
        }

        .logo-section {
            text-align: left;
            margin: 0 0 15px 0;
            padding: 0;
        }

        .logo {
            width: 300px;
            height: auto;
            margin: 0;
            padding: 0;
        }

        .titres-ministere {
            text-align: center;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 14pt;
            line-height: 1.2;
            margin: 0 0 20px 0;
            max-width: 400px;
        }

        .titres-ministere div {
            margin: 2px 0;
        }

        .contact-info {
            position: absolute;
            top: 0;
            right: 0;
            text-align: left;
            font-size: 10pt;
            line-height: 1.3;
            width: 250px;
            white-space: nowrap;
        }



        /* LOCALISATION ET RÉFÉRENCE */
        .date-lieu {
            text-align: right;
            margin: 10px 0 15px 0;
            font-size: 14pt;
        }

        .numero-reference {
            margin: 15px 0 25px 0;
            font-size: 14pt;
        }

        /* TITRE (CENTRÉ, GRAS, SOULIGNÉ) */
        .titre-attestation {
            text-align: center;
            font-size: 14pt;
            font-weight: bold;
            text-decoration: underline;
            margin: 30px 0 35px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* BANDE TRICOLORE DRAPEAU BÉNIN - INTÉGRÉE AU FORMAT A4 */
        .drapeau-benin {
            text-align: center;
            margin: 60px auto 20px auto;
            padding: 0;
            width: 100%;
            max-width: 100%;
            page-break-inside: avoid;
            clear: both;
        }

        .drapeau-benin img {
            width: 270px;
            height: auto;
            display: block;
            margin: 0 auto;
            max-width: 100%;
        }

        /* CONTENU SELON ALGORITHME OFFICIEL */
        .contenu {
            text-align: justify;
            font-size: 14pt;
            line-height: 1.6;
            margin: 25px 0;
        }

        .contenu p {
            margin: 18px 0;
            text-indent: 0;
        }

        .bold {
            font-weight: bold;
        }

        /* FOI DE QUOI */
        .foi-de-quoi {
            margin: 25px 0 40px 0;
            text-align: justify;
            font-size: 14pt;
            line-height: 1.6;
        }

        /* SECTION SIGNATURE */
        .signature-section {
            margin-top: 50px;
            text-align: right;
            font-size: 14pt;
        }

        .signature-title {
            font-weight: bold;
            margin-bottom: 70px;
            line-height: 1.4;
        }

        .signature-name {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- BOUTON IMPRESSION (VISIBLE UNIQUEMENT À L'ÉCRAN) -->
    <button onclick="window.print()" class="no-print">
        🖨️ Imprimer / Télécharger PDF
    </button>

    <div class="attestation-container">
        <!-- EN-TÊTE CONFORME AU MODÈLE OFFICIEL EXACT -->
        <div class="header-officiel">
            <div class="logo-section">
                <img src="{{ asset('images/logoministere.png') }}" alt="Logo Ministère" class="logo">
            </div>
            <div class="titres-ministere">
                <div>SECRÉTARIAT GÉNÉRAL</div>
                <div>DU MINISTÈRE</div>
                <div>************</div>
                <div>{{ mb_strtoupper($libelle_structure, 'UTF-8') }}</div>
            </div>
            <div class="contact-info">
                368, Avenue Jean-Paul II<br>
                01 BP 302 COTONOU<br>
                Tél. : 21 30 10 20 – Fax : 21 30 18 51<br>
                www.finances.bj
            </div>
        </div>

        <!-- LOCALISATION ET RÉFÉRENCE -->
        <div class="date-lieu">
            <strong>Cotonou</strong>, le {{ \Carbon\Carbon::now()->locale('fr')->isoFormat('dddd DD MMMM YYYY') }}
        </div>

        <div class="numero-reference">
            N° {{ $numero_reference }}
        </div>

        <!-- TITRE (CENTRÉ, GRAS, SOULIGNÉ) -->
        <div class="titre-attestation">
            Attestation d'Effectivité de Stage
        </div>

        <!-- ALGORITHME DE GÉNÉRATION DU CONTENU SELON MODÈLE OFFICIEL -->
        <div class="contenu">
            {{-- PARAGRAPHE 1 : ATTESTATION PRINCIPALE --}}
            <p>
                Je soussigné <span class="bold">{{ mb_strtoupper($nom_rs, 'UTF-8') }}</span>,
                {{ $fonction_responsable }} de la {{ $libelle_structure }}
                @if($sigle_structure)
                    ({{ $sigle_structure }})
                @endif
                du Ministère de l'Économie et des Finances (MEF), atteste que
                <span class="bold">{{ $civilite }} {{ mb_strtoupper($stagiaire->user->nom, 'UTF-8') }} {{ $stagiaire->user->prenom }}</span>,
                a été stagiaire @if($type_stage === 'académique')académique @else professionnel @endif
                à la {{ $libelle_structure }}
                @if($sigle_structure)
                    ({{ $sigle_structure }})
                @endif
                du Ministère de l'Économie et des Finances.
            </p>

            {{-- PARAGRAPHE 2 : INFORMATIONS ACADÉMIQUES/PROFESSIONNELLES --}}
            @if($type_stage === 'académique')
                <p>
                    {{ $accords['pronom_sujet'] }} est {{ $accords['etudiant'] }} en {{ $niveau_etude }} en {{ $filiere }}
                    à {{ $universite }}@if($sigle_universite) ({{ $sigle_universite }})@endif.
                </p>
            @else
                <p>
                    {{ $accords['pronom_sujet'] }} est {{ $accords['titulaire'] }} d'un diplôme en {{ $filiere }},
                    et a effectué ce stage dans le cadre de son développement professionnel.
                </p>
            @endif

            {{-- PARAGRAPHE 3 : DÉTAILS DU STAGE SELON TYPE --}}
            @if($type_stage === 'académique')
                <p>
                    {{ $accords['participe_mis'] }} à la disposition de
                    @if($sigle_structure)
                        la {{ $sigle_structure }}
                    @else
                        la {{ $libelle_structure }}
                    @endif
                    pour un stage académique, pratique et bénévole de {{ $duree_stage }},
                    {{ $accords['pronom_objet'] }} a effectivement effectué son stage du
                    <span class="bold">{{ $date_debut_formatee }}</span>
                    au <span class="bold">{{ $date_fin_formatee }}</span>.
                </p>
            @else
                <p>
                    {{ $accords['participe_mis'] }} à la disposition de
                    @if($sigle_structure)
                        la {{ $sigle_structure }}
                    @else
                        la {{ $libelle_structure }}
                    @endif
                    pour un stage professionnel et encadré,
                    {{ $accords['pronom_objet'] }} a effectivement effectué son stage du
                    <span class="bold">{{ $date_debut_formatee }}</span>
                    au <span class="bold">{{ $date_fin_formatee }}</span>,
                    soit une durée de {{ $duree_stage }}.
                </p>
            @endif
        </div>

        <!-- FOI DE QUOI (CONFORME MODÈLE OFFICIEL) -->
        <div class="foi-de-quoi">
            <p>En foi de quoi, la présente attestation lui est délivrée pour servir et valoir ce que de droit.</p>
        </div>

        <!-- SECTION SIGNATURE (CONFORME MODÈLE OFFICIEL) -->
        <div class="signature-section">
            <div class="signature-title">
                Le {{ $fonction_responsable }}<br>
                (Signature et cachet)
            </div>
            <div class="signature-name">
                {{ mb_strtoupper($nom_rs, 'UTF-8') }}
            </div>
        </div>

        <!-- BANDE TRICOLORE DRAPEAU BÉNIN -->
        <div class="drapeau-benin">
            <img src="{{ asset('images/bande_drapeau_benin_.png') }}" alt="Drapeau du Bénin">
        </div>
    </div>
</body>
</html>
