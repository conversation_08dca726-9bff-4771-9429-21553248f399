<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Ajouter les colonnes stage_id et propose_par à theme_stages après création de la table stages
     */
    public function up(): void
    {
        Schema::table('theme_stages', function (Blueprint $table) {
            $table->foreignId('stage_id')->nullable()->after('user_id')->constrained('stages')->onDelete('cascade');
            $table->string('propose_par')->default('stagiaire')->after('stage_id'); // 'stagiaire' ou 'ms'
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('theme_stages', function (Blueprint $table) {
            $table->dropForeign(['stage_id']);
            $table->dropColumn(['stage_id', 'propose_par']);
        });
    }
};
