<?php

echo "=== Test de connexion à la base de données ===\n\n";

// Test de connexion MySQL directe
try {
    $host = '127.0.0.1';
    $port = 3306;
    $database = 'gestion_stages';
    $username = 'root';
    $password = '';

    echo "🔍 Test de connexion MySQL...\n";
    echo "Host: $host:$port\n";
    echo "Database: $database\n";
    echo "Username: $username\n\n";

    $pdo = new PDO("mysql:host=$host;port=$port", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "✅ Connexion MySQL réussie !\n\n";

    // Vérifier si la base de données existe
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    $dbExists = $stmt->rowCount() > 0;

    if ($dbExists) {
        echo "✅ Base de données '$database' existe !\n\n";

        // Se connecter à la base de données
        $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Vérifier les tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        echo "📋 Tables trouvées (" . count($tables) . ") :\n";
        foreach ($tables as $table) {
            echo "   - $table\n";
        }

        // Vérifier les utilisateurs admin
        if (in_array('users', $tables)) {
            echo "\n🔍 Recherche des utilisateurs admin...\n";
            $stmt = $pdo->prepare("SELECT id, email, role, prenom, nom, telephone FROM users WHERE role = 'admin'");
            $stmt->execute();
            $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo "Nombre d'administrateurs trouvés : " . count($admins) . "\n\n";

            if (count($admins) > 0) {
                foreach ($admins as $admin) {
                    echo "👤 Admin ID {$admin['id']} :\n";
                    echo "   - Email: {$admin['email']}\n";
                    echo "   - Nom complet: {$admin['prenom']} {$admin['nom']}\n";
                    echo "   - Rôle: {$admin['role']}\n";
                    echo "   - Téléphone: {$admin['telephone']}\n";
                    echo "\n";
                }

                echo "✅ Les utilisateurs admin sont bien créés !\n";
                echo "📋 Vous pouvez vous connecter avec :\n";
                echo "- <EMAIL> / password123\n";
                echo "- <EMAIL> / password123\n";
                echo "\n🌐 URL de connexion : http://127.0.0.1:8000/login\n";
            } else {
                echo "❌ Aucun utilisateur admin trouvé !\n";
                echo "💡 Exécutez : php artisan db:seed --class=AdminSeeder\n";
            }
        } else {
            echo "\n❌ Table 'users' non trouvée !\n";
            echo "💡 Exécutez : php artisan migrate\n";
        }

    } else {
        echo "❌ Base de données '$database' n'existe pas !\n";
        echo "💡 Création de la base de données...\n";

        $pdo->exec("CREATE DATABASE `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Base de données '$database' créée avec succès !\n";
        echo "💡 Exécutez maintenant : php artisan migrate\n";
    }

} catch (PDOException $e) {
    echo "❌ Erreur de connexion MySQL : " . $e->getMessage() . "\n";
    echo "\n💡 Vérifications à faire :\n";
    echo "1. XAMPP est-il démarré ?\n";
    echo "2. Le service MySQL est-il actif ?\n";
    echo "3. Les paramètres de connexion sont-ils corrects ?\n";
} catch (Exception $e) {
    echo "❌ Erreur générale : " . $e->getMessage() . "\n";
}

echo "\n=== Fin du test ===\n";
