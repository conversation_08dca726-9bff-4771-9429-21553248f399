<template>
    <div class="flex h-screen admin-bg">
        <!-- SIDEBAR STATIQUE -->
        <aside
            :class="[
                'sidebar-glass text-white transition-all duration-300 ease-in-out flex flex-col shadow-lg z-30',
                'relative flex-shrink-0',
                sidebarExpanded ? 'w-64' : 'w-20'
            ]"
        >
            <!-- Header du Sidebar -->
            <div class="p-4 border-b border-blue-500/30">
                <div class="flex items-center justify-between">
                    <Link :href="route('admin.dashboard')" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center shadow-sm">
                            <span class="text-gray-600 font-bold text-lg">GS</span>
                        </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" key="logo-text" class="transition-all duration-300">
                            <h1 class="text-lg font-bold text-white">Administration</h1>
                            <p class="text-xs text-blue-200">Ministère des Finances</p>
                        </div>
                        </transition>
                    </Link>
                    <button
                        @click="toggleSidebar"
                        class="p-2 rounded-lg hover:bg-blue-500/30 transition-colors duration-200"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 p-4 space-y-2">
                <Link
                    v-for="item in menuItems"
                    :key="item.route"
                    :href="route(item.route)"

                    :class="[
                        'flex items-center rounded-xl text-sm font-medium transition-all duration-200 group relative',
                        sidebarExpanded ? 'px-4 py-3' : 'justify-center py-3 mx-2',
                        isActive(item.active)
                            ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/10'
                            : 'text-blue-100 hover:bg-white/10 hover:text-white hover:shadow-md'
                    ]"
                >
                    <!-- Indicateur actif -->
                    <div
                        v-if="isActive(item.active)"
                        class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full"
                    ></div>

                    <svg class="w-5 h-5 flex-shrink-0 transition-all duration-200"
                         :class="isActive(item.active) ? 'text-white' : 'text-blue-200 group-hover:text-white'"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                    </svg>
                    <transition name='fade' mode='out-in'>
                    <span
                        v-if="sidebarExpanded"
                        class="ml-3 transition-all duration-300 font-medium"
                          :key="`label-${item.route}`"
                    >
                        {{ item.name }}
                    </span>
                    </transition>

                    <!-- Badge pour les éléments actifs -->
                    <div
                        v-if="isActive(item.active) && sidebarExpanded"
                        class="ml-auto w-2 h-2 bg-white rounded-full opacity-80"
                    ></div>
                </Link>
            </nav>

            <!-- User Profile Section avec menu personnalisé -->
            <div class="p-4 border-t border-blue-500/30 relative">
                <div class="relative">
                    <!-- Bouton du profil utilisateur -->
                    <button
                        @click="toggleUserMenu"
                        :class="[
                            'w-full flex items-center rounded-lg text-sm font-medium text-blue-100 bg-blue-500/20 transition-colors hover:bg-blue-500/30',
                            sidebarExpanded ? 'px-3 py-3' : 'justify-center py-3'
                        ]"
                    >
                    <div class="relative flex-shrink-0">
                        <img
                            v-if="user && user.avatar"
                            :src="'/storage/' + user.avatar"
                            alt="Photo de profil"
                            class="w-8 h-8 rounded-full object-cover border-2 border-blue-300"
                        />
                            <div v-else class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold border-2 border-blue-300">
                            {{ user?.nom?.charAt(0) || 'A' }}
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-blue-600 rounded-full"></div>
                    </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" class="ml-3 text-left flex-1" key="user-info">
                        <div class="font-medium text-white text-sm">{{ user?.nom || 'Administrateur' }}</div>
                        <div class="text-xs text-blue-200 truncate">Administrateur</div>
                    </div>
                        </transition>
                        <transition name='fade' mode='out-in'>
                          <svg v-if="sidebarExpanded" class="w-4 h-4 text-blue-200 ml-2 transition-transform" :class="{ 'rotate-180': showUserMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                          </svg>
                        </transition>
                    </button>

                    <!-- Menu contextuel personnalisé -->
                    <transition name="menu-fade">
                        <div
                            v-if="showUserMenu"
                            class="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                        >
                            <!-- Info utilisateur -->
                            <div class="px-4 py-3 border-b border-gray-100">
                                <div class="font-medium text-base text-gray-800">{{ user?.nom || 'Administrateur' }}</div>
                                <div class="font-medium text-sm text-gray-500">{{ user?.email || 'Email non disponible' }}</div>
                            </div>

                            <!-- Options du menu -->
                            <div class="py-1">
                                <Link
                                    :href="route('profile.edit')"
                                    @click="closeUserMenu"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Mon Profil
                                </Link>

                                <Link
                                    :href="route('logout')"
                                    method="post"
                                    as="button"
                                    @click="closeUserMenu"
                                    class="w-full text-left flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                >
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                    Déconnexion
                                </Link>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
        </aside>

        <!-- MAIN CONTENT -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Header modernisé et professionnel -->
            <header class="bg-white shadow-sm border-b border-gray-200 z-20">
                <div class="flex items-center justify-between px-4 md:px-8 py-4 md:py-6">
                    <div class="flex items-center space-x-4 md:space-x-6">
                        <!-- Logo avec container moderne -->
                        <div class="flex-shrink-0 hidden md:block">
                            <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-xl border border-blue-200/50 shadow-sm">
                        <img
                            src="/images/logoministere.png"
                            alt="Logo du Ministère"
                            class="h-10 md:h-12 w-auto object-contain"
                        />
                            </div>
                        </div>

                        <!-- Titre et description -->
                        <div class="hidden sm:block">
                            <h1 class="text-xl md:text-2xl font-bold text-gray-800 mb-1">Administration</h1>
                            <p class="text-gray-600 text-xs md:text-sm">Gestion des Stages - Ministère des Finances</p>
                        </div>

                        <!-- Version mobile du titre -->
                        <div class="sm:hidden">
                            <h1 class="text-lg font-bold text-gray-800">Admin</h1>
                        </div>
                    </div>

                    <!-- Actions et notifications -->
                    <div class="flex items-center space-x-2 md:space-x-4">
                        <!-- Cloche de notifications -->
                        <NotificationBell
                            :notifications="notifications || []"
                            :notifications-route="route('admin.notifications.index')"
                        />

                        <!-- Indicateur de rôle -->
                        <div class="hidden lg:flex items-center">
                            <div
                                class="flex items-center space-x-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-full text-sm font-medium border border-blue-200">
                                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                <span>Espace Admin</span>
                            </div>
                        </div>
                        <div
                            class="lg:hidden bg-gradient-to-r from-blue-600 to-blue-700 text-white px-2 md:px-4 py-2 rounded-full text-xs md:text-sm font-medium shadow-md">
                            <div class="flex items-center space-x-1 md:space-x-2">
                                <svg class="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                <span class="hidden sm:inline">Espace Admin</span>
                                <span class="sm:hidden">Admin</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- En-tête de la page -->
            <header v-if="$slots.header"
                class="page-header bg-gradient-to-r from-blue-50/50 to-indigo-50/30 border-b border-blue-100/50">
                <div class="px-4 md:px-6 py-3 md:py-4 mx-auto max-w-6xl">
                    <slot name="header" />
                </div>
            </header>

            <!-- Contenu principal -->
            <main class="flex-1 overflow-auto bg-gradient-to-br from-gray-50/30 to-blue-50/20">
                <div class="px-4 md:px-6 py-3 md:py-4">
                    <slot />
                </div>
            </main>
        </div>

        <!-- Messages de succès globaux -->
        <GlobalSuccessMessages />

        <!-- Cloche de notifications réelle -->
        <RealNotificationBell
            user-role="admin"
            theme="blue"
            :notifications="$page.props.notifications || []"
        />

    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link, usePage } from '@inertiajs/vue3'
import RealNotificationBell from '@/Components/RealNotificationBell.vue'
import NotificationBell from '@/Components/NotificationBell.vue'
import GlobalSuccessMessages from '@/Components/GlobalSuccessMessages.vue'
const sidebarExpanded = ref(true)
const showUserMenu = ref(false)

const props = defineProps({
  notifications: {
    type: Array,
    default: () => []
  }
})

const page = usePage()
const user = computed(() => page.props.auth?.user)

const toggleSidebar = () => {
    sidebarExpanded.value = !sidebarExpanded.value
}



const toggleUserMenu = () => {
    showUserMenu.value = !showUserMenu.value
}

const closeUserMenu = () => {
    showUserMenu.value = false
}

const menuItems = [
    {
        name: 'Tableau de bord',
        route: 'admin.dashboard',
        icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z',
        active: ['admin.dashboard']
    },
    {
        name: 'Structures DG',
        route: 'admin.structures.index',
        icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
        active: ['admin.structures.*']
    },
    {
        name: 'Utilisateurs',
        route: 'admin.users.index',
        icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
        active: ['admin.users.*']
    },
    {
        name: 'Agents RS',
        route: 'admin.agents.index',
        icon: 'M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2 M23 21v-2a4 4 0 00-3-3.87 M16 3.13a4 4 0 010 7.75 M9 7a4 4 0 11-8 0 4 4 0 018 0z',
        active: ['admin.agents.*']
    },
    {
        name: 'Agents MS',
        route: 'admin.agents-ms.index',
        icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
        active: ['admin.agents-ms.*'],
        readonly: true
    },
    {
        name: 'Stagiaires',
        route: 'admin.stagiaires.index',
        icon: 'M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z',
        active: ['admin.stagiaires.*']
    },
    {
        name: 'Universités',
        route: 'admin.universites.index',
        icon: 'M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5',
        active: ['admin.universites.*']
    },
    {
        name: 'Responsables RU',
        route: 'admin.responsables-ru.index',
        icon: 'M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z',
        active: ['admin.responsables-ru.*']
    },
    {
        name: 'Sous-structures',
        route: 'admin.sous-structures.index',
        icon: 'M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z',
        active: ['admin.sous-structures.*'],
        readonly: true
    },
    {
        name: 'Organigrammes',
        route: 'admin.organigrammes.index',
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
        active: ['admin.organigrammes.*'],
        readonly: true
    }
]

const isActive = (routeNames) => {
    return routeNames.some(routeName => route().current(routeName))
}
</script>

<style scoped>
/* Arrière-plan principal - voir définition complète en bas du fichier */

/* Glassmorphism pour la sidebar */
.sidebar-glass {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.95) 0%,
        rgba(37, 99, 235, 0.98) 50%,
        rgba(29, 78, 216, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 25px 45px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Transitions fluides */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

/* Menu utilisateur */
.menu-fade-enter-active, .menu-fade-leave-active {
    transition: all 0.2s ease;
}

.menu-fade-enter-from, .menu-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* Hover effects */
.group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
</style>

<style scoped>
/* Glassmorphism pour la sidebar */
.sidebar-glass {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.95) 0%,
        rgba(37, 99, 235, 0.98) 50%,
        rgba(29, 78, 216, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 25px 45px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Logo avec effet gradient */
.logo-gradient {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 16px;
    color: #2563eb;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
}

.logo-gradient::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Menu items avec indicateur actif */
.menu-link {
    position: relative;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-link:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transform: translateX(4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.menu-link.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.active-indicator {
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
}

/* Header glassmorphism */
.header-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

/* Main content */
.main-content {
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.8) 0%,
        rgba(239, 246, 255, 0.6) 50%,
        rgba(224, 242, 254, 0.4) 100%
    );
}

/* Footer glassmorphism */
.footer-glass {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
}

/* Sidebar toggle */
.sidebar-toggle {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Transitions */
.fade-enter-active, .fade-leave-active {
    transition: all 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.menu-fade-enter-active, .menu-fade-leave-active {
  transition: all 0.2s ease;
}
.menu-fade-enter-from, .menu-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.admin-bg {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Animation pour le panel de notifications */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}
</style>
