<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\DemandeStage;
use App\Models\Structure;

class DPAFReponseStructureMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $demande;
    public $structure;
    public $stagiaire;
    public $decision;
    public $justification;

    /**
     * Create a new message instance.
     */
    public function __construct(DemandeStage $demande, Structure $structure, string $decision, string $justification = null)
    {
        $this->demande = $demande;
        $this->structure = $structure;
        $this->stagiaire = $demande->stagiaire->user;
        $this->decision = $decision;
        $this->justification = $justification;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->decision === 'acceptee' 
            ? 'Demande acceptée par la structure - ' . $this->demande->code_suivi
            : 'Demande refusée par la structure - ' . $this->demande->code_suivi;

        return new Envelope(
            subject: $subject,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.dpaf-reponse-structure',
            with: [
                'demande' => $this->demande,
                'structure' => $this->structure,
                'stagiaire' => $this->stagiaire,
                'decision' => $this->decision,
                'justification' => $this->justification,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
