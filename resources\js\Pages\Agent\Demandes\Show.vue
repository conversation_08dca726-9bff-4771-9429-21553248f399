<script setup>
import { Head, <PERSON> } from '@inertiajs/vue3';
import { router } from '@inertiajs/vue3';
import AgentDPAF from '@/Layouts/AgentDPAF.vue';
import { ref } from 'vue';
import AdminToast from '@/Components/AdminToast.vue';
import Modal from '@/Components/Modal.vue';
import DocumentsViewer from '@/Components/DocumentsViewer.vue';
import ProfessionalConfirmModal from '@/Components/ProfessionalConfirmModal.vue';
import ProfessionalAlertModal from '@/Components/ProfessionalAlertModal.vue';
import { useProfessionalModals } from '@/Composables/useProfessionalModals.js';

const props = defineProps({
    demande: Object,
    structures: Array,
    membres: Array
});

const { confirmModal, alertModal, confirmDemandeAction, alertMotifRefusRequired } = useProfessionalModals();
const toast = ref(null);

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('fr-FR');
};

// Fonction améliorée pour les couleurs de statut - plus vives
const getStatusColor = (statut) => {
    if (!statut) return 'text-slate-800 bg-gradient-to-r from-slate-100 to-gray-100 border-2 border-slate-300 shadow-lg shadow-slate-200/50';
    // Normalisation : trim, minuscule, suppression des accents
    const normalize = (str) => str.normalize('NFD').replace(/\p{Diacritic}/gu, '').trim().toLowerCase();
    const cleanStatut = normalize(statut);
    console.log('Statut reçu pour coloration:', statut);
    switch (cleanStatut) {
        case 'en attente':
            return 'text-amber-800 bg-gradient-to-r from-amber-100 to-orange-100 border-2 border-amber-300 shadow-lg shadow-amber-200/50';
        case 'en cours':
            return 'text-blue-800 bg-gradient-to-r from-blue-100 to-sky-100 border-2 border-blue-300 shadow-lg shadow-blue-200/50';
        case 'approuvee':
        case 'acceptée':
        case 'acceptee':
            return 'text-emerald-800 bg-gradient-to-r from-emerald-100 to-green-100 border-2 border-emerald-300 shadow-lg shadow-emerald-200/50';
        case 'refusee':
        case 'refusée':
            return 'text-red-800 bg-gradient-to-r from-red-100 to-rose-100 border-2 border-red-300 shadow-lg shadow-red-200/50';
        case 'a reaffecter':
        case 'a réaffecter':
            return 'text-orange-800 bg-gradient-to-r from-orange-100 to-amber-100 border-2 border-amber-300 shadow-lg shadow-orange-200/50';
        default:
            return 'text-slate-800 bg-gradient-to-r from-slate-100 to-gray-100 border-2 border-slate-300 shadow-lg shadow-slate-200/50';
    }
};

const showAffectationModal = ref(false);
const selectedStructure = ref(props.demande.structure_id || '');

const openAffectationModal = () => {
    showAffectationModal.value = true;
};

const closeAffectationModal = () => {
    showAffectationModal.value = false;
};

const submitAffectation = () => {
    if (!selectedStructure.value) return;

    router.post(route('agent.demandes.affecter', props.demande.id), {
        structure_id: selectedStructure.value
    }, {
        onSuccess: () => {
            closeAffectationModal();
            if (toast.value) {
                toast.value.addToast({
                    type: 'success',
                    title: 'Affectation réussie',
                    message: `La demande '${props.demande.code_suivi}' a été affectée à la structure '${props.structures.find(s => s.id === selectedStructure.value)?.libelle}' avec succès.`
                });
            }
        }
    });
};

const showRefusDefModal = ref(false);
const motifRefusDef = ref('');

const openRefusDefModal = () => {
    motifRefusDef.value = '';
    showRefusDefModal.value = true;
};
const closeRefusDefModal = () => {
    showRefusDefModal.value = false;
};
const submitRefusDef = async () => {
    if (!motifRefusDef.value.trim()) {
        await alertMotifRefusRequired();
        return;
    }
    router.post(route('agent.demandes.refuseDefinitivement', props.demande.id), {
        motif_refus: motifRefusDef.value
    }, {
        onSuccess: () => {
            closeRefusDefModal();
            if (toast.value) {
                toast.value.addToast({
                    type: 'success',
                    title: 'Succès',
                    message: 'La demande a été refusée définitivement.'
                });
            }
        }
    });
};

// AMÉLIORATION DPAF : Nouvelle modale de rejet pour demandes en attente
const showRejetModal = ref(false);
const motifRejet = ref('');

const openRejetModal = () => {
    motifRejet.value = '';
    showRejetModal.value = true;
};

const closeRejetModal = () => {
    showRejetModal.value = false;
};

const submitRejet = () => {
    if (!motifRejet.value.trim()) {
        if (toast.value) {
            toast.value.addToast({
                type: 'error',
                title: 'Erreur',
                message: 'Veuillez indiquer un motif de rejet.'
            });
        }
        return;
    }

    router.post(route('agent.demandes.refuseDefinitivement', props.demande.id), {
        motif_refus: motifRejet.value
    }, {
        onSuccess: () => {
            closeRejetModal();
            if (toast.value) {
                toast.value.addToast({
                    type: 'success',
                    title: 'Demande rejetée',
                    message: `La demande '${props.demande.code_suivi}' a été rejetée avec succès.`
                });
            }
        },
        onError: (errors) => {
            if (toast.value) {
                toast.value.addToast({
                    type: 'error',
                    title: 'Erreur',
                    message: 'Une erreur est survenue lors du rejet de la demande.'
                });
            }
        }
    });
};

const submit = async (action) => {
    const confirmed = await confirmDemandeAction(action, props.demande.code_suivi);
    if (!confirmed) {
        return;
    }

    router.post(route(`agent.demandes.${action}`, props.demande.id));
};

function getInitials(nom, prenom) {
    const n = nom ? nom.charAt(0) : '';
    const p = prenom ? prenom.charAt(0) : '';
    return (n + p).toUpperCase() || '?';
}
</script>

<template>
    <Head title="Détails de la demande" />
    <AgentDPAF>
        <template #header>
            <h2 class="text-xl font-semibold text-slate-800">Détails de la demande</h2>
        </template>

        <!-- Arrière-plan grisé -->
        <div class="min-h-screen bg-gradient-to-br from-gray-100 via-slate-100 to-gray-200">
            <div class="py-12">
                <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                    <div class="overflow-hidden bg-white rounded-3xl shadow-xl border-2 border-slate-200/50">
                        <div class="p-8">
                            <!-- Header amélioré avec plus de couleurs -->
                            <div class="mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                                <div class="flex items-center gap-4">
                                    <div class="p-4 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-2xl shadow-xl shadow-blue-500/30">
                                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h1 class="text-3xl font-black bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">Détails de la demande #{{ demande.code_suivi }}</h1>
                                        <p class="text-slate-600 font-mono text-base mt-1 bg-slate-200/70 px-3 py-1 rounded-lg inline-block">{{ demande.code_suivi }}</p>
                                    </div>
                                </div>
                                <Link :href="route('agent.demandes')" 
                                    class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-slate-600 to-slate-700 text-white rounded-xl hover:from-slate-700 hover:to-slate-800 transition-all duration-300 shadow-lg hover:shadow-xl font-medium text-base transform hover:scale-105">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                                    </svg>
                                    Retour
                                </Link>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                                <!-- Informations sur la demande - plus coloré -->
                                <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50 p-8 rounded-3xl shadow-xl border-2 border-blue-200/50">
                                    <div class="flex items-center gap-3 mb-6">
                                        <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <h2 class="text-xl font-bold text-blue-900">Informations de la demande</h2>
                                    </div>
                                    
                                    <div class="space-y-5">
                                        <div class="bg-white/80 p-4 rounded-2xl border border-blue-200/50 shadow-sm">
                                            <span class="text-sm text-blue-700 font-semibold block mb-2">Statut</span>
                                            <span class="px-4 py-2 text-sm font-bold rounded-2xl inline-block" :class="getStatusColor(demande.statut)">
                                                {{ demande.statut }}
                                            </span>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-blue-200/50 shadow-sm">
                                            <span class="text-sm text-blue-700 font-semibold block mb-2">Structure</span>
                                            <div class="flex flex-col gap-3">
                                                <div class="font-bold text-slate-800 text-lg">
                                                    {{ props.demande.structure ? props.demande.structure.libelle : 'Non spécifiée' }}
                                                </div>
                                                <button 
                                                    v-if="demande.statut === 'En attente'"
                                                    @click="openAffectationModal"
                                                    class="px-5 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl font-bold flex items-center gap-2 transform hover:scale-105"
                                                >
                                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
                                                    </svg>
                                                    Affecter une structure
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-blue-200/50 shadow-sm">
                                            <span class="text-sm text-blue-700 font-semibold block mb-1">Type de stage</span>
                                            <span class="font-bold text-slate-800">{{ demande.type }}</span>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-blue-200/50 shadow-sm">
                                            <span class="text-sm text-blue-700 font-semibold block mb-1">Nature</span>
                                            <span class="font-bold text-slate-800">{{ demande.nature }}</span>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-blue-200/50 shadow-sm">
                                            <span class="text-sm text-blue-700 font-semibold block mb-1">Période</span>
                                            <span class="font-bold text-slate-800">Du {{ formatDate(demande.date_debut) }} au {{ formatDate(demande.date_fin) }}</span>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-blue-200/50 shadow-sm">
                                            <span class="text-sm text-blue-700 font-semibold block mb-1">Date de soumission</span>
                                            <span class="font-bold text-slate-800">{{ formatDate(demande.created_at) }}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Informations du stagiaire - plus coloré -->
                                <div class="bg-gradient-to-br from-purple-50 via-pink-50 to-purple-50 p-8 rounded-3xl shadow-xl border-2 border-purple-200/50">
                                    <div class="flex items-center gap-3 mb-6">
                                        <div class="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg">
                                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                        <h2 class="text-xl font-bold text-purple-900">Informations du stagiaire</h2>
                                    </div>
                                    
                                    <div class="space-y-4">
                                        <div class="bg-white/80 p-4 rounded-2xl border border-purple-200/50 shadow-sm">
                                            <span class="text-sm text-purple-700 font-semibold block mb-1">Nom complet</span>
                                            <div class="mb-6 flex items-center">
                                                <!-- Condition pour afficher l'image si visage_path existe, sinon afficher les initiales -->
                                                <div class="h-16 w-16 rounded-full flex items-center justify-center text-xl font-bold shadow-md mr-4 overflow-hidden bg-blue-600 text-white">
                                                    <img v-if="demande.visage_path"
                                                         :src="'/storage/' + demande.visage_path"
                                                         alt="Photo du stagiaire"
                                                         class="object-cover w-full h-full">
                                                    <div v-else class="w-full h-full flex items-center justify-center">
                                                        {{ getInitials(demande.stagiaire?.user?.nom, demande.stagiaire?.user?.prenom) }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 class="text-lg font-semibold text-slate-800">{{ demande.stagiaire?.user?.nom }} {{ demande.stagiaire?.user?.prenom }}</h4>
                                                    <p class="text-sm text-slate-600">{{ demande.stagiaire?.user?.email }}</p>
                                                </div>
                                        </div>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-purple-200/50 shadow-sm">
                                            <span class="text-sm text-purple-700 font-semibold block mb-1">Téléphone</span>
                                            <span class="font-bold text-slate-800">{{ demande.stagiaire?.user?.telephone }}</span>
                                        </div>
                                        
                                        <div v-if="demande.type === 'Académique'" class="bg-white/80 p-4 rounded-2xl border border-purple-200/50 shadow-sm">
                                            <span class="text-sm text-purple-700 font-semibold block mb-1">Université</span>
                                            <span class="font-bold text-slate-800">{{ demande.stagiaire?.universite }}</span>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-purple-200/50 shadow-sm">
                                            <span class="text-sm text-purple-700 font-semibold block mb-1">Filière</span>
                                            <span class="font-bold text-slate-800">{{ demande.stagiaire?.filiere }}</span>
                                        </div>
                                        
                                        <div class="bg-white/80 p-4 rounded-2xl border border-purple-200/50 shadow-sm">
                                            <span class="text-sm text-purple-700 font-semibold block mb-1">Niveau d'étude</span>
                                            <span class="font-bold text-slate-800">{{ demande.stagiaire?.niveau_etude }}</span>
                                        </div>
                                    </div>

                                    <!-- Membres du groupe - coloré -->
                                    <div v-if="demande.nature === 'Groupe' && membres && membres.length > 0" class="mt-8">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg">
                                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                </svg>
                                            </div>
                                            <h3 class="text-lg font-bold text-emerald-800">Autres membres du groupe</h3>
                                        </div>
                                        <ul class="space-y-4">
                                            <li v-for="membre in membres" :key="membre.id"
                                                class="p-5 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-2xl border-2 border-emerald-200/50 shadow-lg">
                                                <div class="flex flex-col md:flex-row md:items-start gap-4">
                                                    <div class="flex-1">
                                                        <div class="font-bold text-emerald-800 text-lg mb-2">{{ membre.user?.nom }} {{ membre.user?.prenom }}</div>
                                                        <div class="text-sm text-emerald-700 font-medium">Email : {{ membre.user?.email }}</div>
                                                        <div class="text-sm text-emerald-700 font-medium">Téléphone : {{ membre.user?.telephone }}</div>
                                                    </div>

                                                    <!-- Documents gérés par le composant unifié ci-dessous -->
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Documents attachés - Interface unifiée -->
                            <div class="mt-8">
                                <DocumentsViewer :demande="demande" :show-download-buttons="true" />
                            </div>

                            <!-- AMÉLIORATION DPAF : Actions pour demandes en attente -->
                            <div v-if="demande.statut === 'En attente'" class="mt-8 bg-gradient-to-br from-slate-50 to-blue-50 p-8 rounded-3xl border-2 border-blue-200/50 shadow-xl">
                                <div class="flex items-center gap-3 mb-6">
                                    <div class="p-3 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold text-slate-800">Actions sur la demande</h3>
                                        <p class="text-slate-600">Choisissez l'action à effectuer sur cette demande</p>
                                    </div>
                                </div>

                                <div class="flex flex-wrap gap-4">
                                    <!-- Bouton Affecter la demande -->
                                    <button
                                        @click="openAffectationModal"
                                        class="flex-1 min-w-[250px] px-8 py-4 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-xl hover:from-emerald-700 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl font-bold flex items-center justify-center gap-3 transform hover:scale-105"
                                    >
                                        <svg class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                                        </svg>
                                        <span>Affecter la demande</span>
                                    </button>

                                    <!-- Bouton Rejeter la demande -->
                                    <button
                                        @click="openRejetModal"
                                        class="flex-1 min-w-[250px] px-8 py-4 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-xl hover:from-red-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl font-bold flex items-center justify-center gap-3 transform hover:scale-105"
                                    >
                                        <svg class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Rejeter la demande</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Actions pour demandes à réaffecter (existant) -->
                            <div v-if="demande.statut === 'A réaffecter'" class="flex gap-4 mt-8">
                                <button @click="openAffectationModal" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold flex items-center gap-2 shadow-lg hover:scale-105">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/></svg>
                                    Réaffecter à une structure
                                    </button>
                                <button @click="openRefusDefModal" class="px-6 py-3 bg-gradient-to-r from-red-600 to-rose-600 text-white rounded-xl font-bold flex items-center gap-2 shadow-lg hover:scale-105">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                                    Refuser définitivement
                                    </button>
                                </div>

                            <!-- Statut affiché pour demandes déjà traitées -->
                            <div v-if="!['En attente', 'A réaffecter'].includes(demande.statut)" class="mt-8 bg-gradient-to-br from-gray-50 to-slate-50 p-6 rounded-2xl border border-gray-200">
                                <div class="flex items-center gap-3">
                                    <div class="p-2 rounded-full" :class="{
                                        'bg-emerald-100 text-emerald-600': demande.statut === 'Acceptée',
                                        'bg-red-100 text-red-600': demande.statut === 'Refusée',
                                        'bg-blue-100 text-blue-600': demande.statut === 'En cours'
                                    }">
                                        <svg v-if="demande.statut === 'Acceptée'" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                        <svg v-else-if="demande.statut === 'Refusée'" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                        <svg v-else class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-bold text-slate-800">Statut : {{ demande.statut }}</h3>
                                        <p class="text-slate-600 text-sm">Cette demande a déjà été traitée</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal d'affectation - amélioré -->
        <div v-if="showAffectationModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="bg-white rounded-3xl shadow-2xl max-w-md w-full mx-4 border-2 border-slate-200/50">
                <div class="px-6 py-5 border-b-2 border-slate-100 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-3xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-bold text-blue-900">
                                Affecter une structure
                            </h3>
                        </div>
                        <button @click="closeAffectationModal" class="text-gray-400 hover:text-gray-600 p-2 rounded-xl hover:bg-gray-100 transition-all duration-200">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="px-6 py-6">
                    <div class="mb-4">
                        <label class="block text-sm font-bold text-slate-700 mb-3">Structure</label>
                        <select 
                            v-model="selectedStructure"
                            class="w-full px-4 py-3 text-base border-2 border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-xl font-medium bg-white shadow-sm"
                        >
                            <option value="">Sélectionner une structure</option>
                            <option v-for="structure in structures" :key="structure.id" :value="structure.id">
                                {{ structure.libelle }}
                            </option>
                        </select>
                    </div>
                </div>

                <div class="px-6 py-5 bg-gradient-to-r from-slate-50 to-gray-50 flex justify-end gap-3 rounded-b-3xl">
                    <button 
                        @click="closeAffectationModal"
                        class="px-5 py-3 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 rounded-xl hover:from-gray-300 hover:to-gray-400 transition-all duration-300 font-bold transform hover:scale-105"
                    >
                        Annuler
                    </button>
                    <button 
                        @click="submitAffectation"
                        :disabled="!selectedStructure"
                        :class="[
                            'px-5 py-3 rounded-xl transition-all duration-300 flex items-center gap-2 font-bold transform',
                            selectedStructure 
                                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl hover:scale-105' 
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        ]"
                    >
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                        Affecter
                    </button>
                </div>
            </div>
        </div>

        <!-- Modal refus définitif -->
        <Modal :show="showRefusDefModal" @close="closeRefusDefModal">
            <div class="p-8 bg-white/95 rounded-3xl shadow-2xl border-2 border-slate-200/50 max-w-md mx-auto">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                    <div class="p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl shadow-lg">
                        <svg class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>
                    </div>
                    Refuser définitivement la demande
                </h2>
                <div class="mb-6">
                    <label class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">Motif du refus définitif</label>
                    <textarea v-model="motifRefusDef" class="w-full rounded-2xl border-2 border-gray-200 shadow-sm focus:border-red-500 focus:ring-2 focus:ring-red-200 focus:ring-opacity-50 text-gray-900 font-medium p-4" rows="4" placeholder="Saisir le motif du refus définitif..."></textarea>
                </div>
                <div class="flex justify-end gap-3">
                    <button @click="closeRefusDefModal" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">Annuler</button>
                    <button @click="submitRefusDef" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">Confirmer le refus</button>
                </div>
            </div>
        </Modal>

        <!-- AMÉLIORATION DPAF : Modal de rejet pour demandes en attente -->
        <div v-if="showRejetModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="bg-white rounded-3xl shadow-2xl max-w-lg w-full mx-4 border-2 border-red-200/50">
                <div class="px-8 py-6 border-b-2 border-red-100 bg-gradient-to-r from-red-50 to-pink-50 rounded-t-3xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <div class="p-3 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-red-900">
                                    Rejeter la demande
                                </h3>
                                <p class="text-red-700 text-sm">
                                    Code : {{ demande.code_suivi }}
                                </p>
                            </div>
                        </div>
                        <button @click="closeRejetModal" class="text-gray-400 hover:text-gray-600 p-2 rounded-xl hover:bg-gray-100 transition-all duration-200">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="px-8 py-6">
                    <div class="mb-6">
                        <div class="bg-amber-50 border border-amber-200 rounded-xl p-4 mb-6">
                            <div class="flex items-start gap-3">
                                <svg class="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div>
                                    <h4 class="font-semibold text-amber-800 mb-1">Information importante</h4>
                                    <p class="text-amber-700 text-sm">
                                        Cette action rejettera définitivement la demande. Le stagiaire recevra un email avec le motif du rejet.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <label class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">
                            Motif du rejet <span class="text-red-500">*</span>
                        </label>
                        <textarea
                            v-model="motifRejet"
                            class="w-full rounded-xl border-2 border-gray-200 shadow-sm focus:border-red-500 focus:ring-2 focus:ring-red-200 focus:ring-opacity-50 text-gray-900 font-medium p-4 min-h-[120px]"
                            rows="5"
                            placeholder="Veuillez expliquer clairement les raisons du rejet de cette demande de stage..."
                            required
                        ></textarea>
                        <p class="text-xs text-gray-500 mt-2">
                            Ce motif sera communiqué au stagiaire par email.
                        </p>
                    </div>
                </div>

                <div class="px-8 py-6 bg-gradient-to-r from-gray-50 to-slate-50 flex justify-end gap-4 rounded-b-3xl">
                    <button
                        @click="closeRejetModal"
                        class="px-6 py-3 bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 rounded-xl hover:from-gray-300 hover:to-gray-400 transition-all duration-300 font-bold transform hover:scale-105"
                    >
                        Annuler
                    </button>
                    <button
                        @click="submitRejet"
                        :disabled="!motifRejet.trim()"
                        class="px-6 py-3 bg-gradient-to-r from-red-600 to-pink-600 text-white rounded-xl hover:from-red-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl font-bold flex items-center gap-2 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                        Confirmer le rejet
                    </button>
                </div>
            </div>
        </div>
    </AgentDPAF>

    <!-- Composant Toast pour les notifications -->
    <AdminToast ref="toast" />

    <!-- Modals professionnels -->
    <ProfessionalConfirmModal
        :show="confirmModal.show"
        :type="confirmModal.type"
        :title="confirmModal.title"
        :subtitle="confirmModal.subtitle"
        :message="confirmModal.message"
        :details="confirmModal.details"
        :confirm-text="confirmModal.confirmText"
        :cancel-text="confirmModal.cancelText"
        :is-destructive="confirmModal.isDestructive"
        @confirm="confirmModal.onConfirm"
        @cancel="confirmModal.onCancel"
        @close="confirmModal.onCancel"
    />

    <ProfessionalAlertModal
        :show="alertModal.show"
        :type="alertModal.type"
        :title="alertModal.title"
        :subtitle="alertModal.subtitle"
        :message="alertModal.message"
        :details="alertModal.details"
        :suggestions="alertModal.suggestions"
        :primary-action-text="alertModal.primaryActionText"
        :secondary-action-text="alertModal.secondaryActionText"
        :show-secondary-action="alertModal.showSecondaryAction"
        @primary-action="alertModal.onPrimaryAction"
        @secondary-action="alertModal.onSecondaryAction"
        @close="alertModal.onPrimaryAction"
    />
</template>