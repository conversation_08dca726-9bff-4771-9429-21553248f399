<template>
    <component :is="layoutComponent">
        <Head title="Mon Profil" />

        <div class="py-8 min-h-screen bg-gradient-to-br from-blue-50 via-white to-gray-50">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-8">
                <!-- En-tête de la page -->
                <div class="bg-white rounded-2xl shadow-lg border border-blue-100 p-8">
                    <div class="flex items-center space-x-6">
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-4 rounded-2xl shadow-lg">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Mon <PERSON>il</h1>
                            <p class="text-gray-600 mt-1">G<PERSON><PERSON> vos informations personnelles et vos préférences</p>
                        </div>
                    </div>
                </div>

                <!-- Section Avatar -->
                <div class="bg-white rounded-2xl shadow-lg border border-blue-100 p-8">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Photo de profil</h2>
                            <p class="text-gray-600 text-sm mt-1">Personnalisez votre avatar professionnel</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-8">
                        <!-- Avatar actuel -->
                        <div class="relative">
                            <img
                                v-if="$page.props.auth.user.avatar"
                                :src="'/storage/' + $page.props.auth.user.avatar"
                                alt="Photo de profil"
                                class="w-24 h-24 rounded-full object-cover border-4 border-blue-200 shadow-lg ring-2 ring-blue-100"
                            />
                            <div
                                v-else
                                class="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white text-2xl font-bold border-4 border-blue-200 shadow-lg ring-2 ring-blue-100"
                            >
                                {{ $page.props.auth.user.nom?.charAt(0) || 'U' }}
                            </div>

                            <!-- Badge de statut -->
                            <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 border-2 border-white rounded-full shadow-sm"></div>
                        </div>

                        <!-- Actions avatar -->
                        <div class="flex-1 space-y-4">
                            <div class="flex flex-wrap gap-3">
                                <label class="cursor-pointer">
                                    <input
                                        type="file"
                                        @change="handleAvatarUpload"
                                        accept="image/*"
                                        class="hidden"
                                    />
                                    <span class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                        </svg>
                                        Changer la photo
                                    </span>
                                </label>

                                <button
                                    v-if="$page.props.auth.user.avatar"
                                    @click="removeAvatar"
                                    class="inline-flex items-center px-6 py-3 bg-red-50 text-red-600 rounded-xl hover:bg-red-100 transition-all duration-200 font-medium border border-red-200 hover:border-red-300"
                                >
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    Supprimer
                                </button>
                            </div>
                            <p class="text-sm text-gray-500">
                                Formats acceptés : JPG, PNG, GIF. Taille maximale : 2MB
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Informations personnelles -->
                <div class="bg-white rounded-2xl shadow-lg border border-blue-100 p-8">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Informations personnelles</h2>
                            <p class="text-gray-600 text-sm mt-1">Mettez à jour vos informations de base</p>
                        </div>
                    </div>

                    <form @submit.prevent="updateProfile" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Nom -->
                            <div>
                                <label for="nom" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nom *
                                </label>
                                <input
                                    id="nom"
                                    v-model="form.nom"
                                    type="text"
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                    :class="{ 'border-red-500 bg-red-50': form.errors.nom }"
                                />
                                <p v-if="form.errors.nom" class="mt-1 text-sm text-red-600">{{ form.errors.nom }}</p>
                            </div>

                            <!-- Prénom -->
                            <div>
                                <label for="prenom" class="block text-sm font-medium text-gray-700 mb-2">
                                    Prénom *
                                </label>
                                <input
                                    id="prenom"
                                    v-model="form.prenom"
                                    type="text"
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                    :class="{ 'border-red-500 bg-red-50': form.errors.prenom }"
                                />
                                <p v-if="form.errors.prenom" class="mt-1 text-sm text-red-600">{{ form.errors.prenom }}</p>
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    Email *
                                </label>
                                <input
                                    id="email"
                                    v-model="form.email"
                                    type="email"
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                    :class="{ 'border-red-500 bg-red-50': form.errors.email }"
                                />
                                <p v-if="form.errors.email" class="mt-1 text-sm text-red-600">{{ form.errors.email }}</p>
                            </div>

                            <!-- Téléphone -->
                            <div>
                                <label for="telephone" class="block text-sm font-medium text-gray-700 mb-2">
                                    Téléphone
                                </label>
                                <input
                                    id="telephone"
                                    v-model="form.telephone"
                                    type="tel"
                                    placeholder="+229 XX XX XX XX"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                    :class="{ 'border-red-500 bg-red-50': form.errors.telephone }"
                                />
                                <p v-if="form.errors.telephone" class="mt-1 text-sm text-red-600">{{ form.errors.telephone }}</p>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                            <div class="flex items-center space-x-4">
                                <button
                                    type="submit"
                                    :disabled="form.processing"
                                    class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:transform-none"
                                >
                                    <svg v-if="form.processing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    {{ form.processing ? 'Enregistrement...' : 'Enregistrer les modifications' }}
                                </button>
                            </div>

                            <!-- Message de succès -->
                            <transition name="fade">
                                <div v-if="$page.props.status === 'profile-updated'" class="flex items-center text-green-600 bg-green-50 px-4 py-2 rounded-lg border border-green-200">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span class="text-sm font-medium">Profil mis à jour avec succès</span>
                                </div>
                            </transition>
                        </div>
                    </form>
                </div>

                <!-- Section Sécurité -->
                <div class="bg-white rounded-2xl shadow-lg border border-blue-100 p-8">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">Sécurité</h2>
                            <p class="text-gray-600 text-sm mt-1">Gérez votre mot de passe et la sécurité de votre compte</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <button
                            @click="showChangePasswordModal = true"
                            class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl hover:from-gray-700 hover:to-gray-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                            </svg>
                            Changer le mot de passe
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal de changement de mot de passe -->
        <ChangePasswordModal
            :show="showChangePasswordModal"
            @close="showChangePasswordModal = false"
        />
    </component>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3'
import { computed, ref } from 'vue'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import RSLayout from '@/Layouts/RSLayout.vue'
import MSLayout from '@/Layouts/MSLayout.vue'
import Stagiaire from '@/Layouts/Stagiaire.vue'
import ChangePasswordModal from '@/Components/ChangePasswordModal.vue'

const props = defineProps({
    mustVerifyEmail: Boolean,
    status: String,
    user: Object,
    layout: String,
})

// État pour le modal de changement de mot de passe
const showChangePasswordModal = ref(false)

// Déterminer le layout à utiliser
const layoutComponent = computed(() => {
    const layouts = {
        'AdminLayout': AdminLayout,
        'RSLayout': RSLayout,
        'MSLayout': MSLayout,
        'Stagiaire': Stagiaire,
    }
    return layouts[props.layout] || AdminLayout
})

// Formulaire pour les informations personnelles
const form = useForm({
    nom: props.user?.nom || '',
    prenom: props.user?.prenom || '',
    email: props.user?.email || '',
    telephone: props.user?.telephone || '',
})

const updateProfile = () => {
    form.patch(route('profile.update'), {
        preserveScroll: true,
    })
}

const handleAvatarUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
        const avatarForm = useForm({
            avatar: file
        })

        avatarForm.post(route('profile.update'), {
            preserveScroll: true,
            onSuccess: () => {
                // Reset file input
                event.target.value = ''
            }
        })
    }
}

const removeAvatar = () => {
    if (confirm('Êtes-vous sûr de vouloir supprimer votre photo de profil ?')) {
        const removeForm = useForm({})
        removeForm.delete(route('profile.avatar.remove'), {
            preserveScroll: true,
        })
    }
}
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}
</style>
