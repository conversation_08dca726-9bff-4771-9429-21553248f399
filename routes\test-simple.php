<?php

use Illuminate\Support\Facades\Route;

// Test simple pour vérifier que le système fonctionne
Route::get('/test-simple', function () {
    try {
        // Test 1: Vérifier que les modèles existent
        $tests = [];
        
        $tests['models'] = [
            'FormatEvaluationUniversite' => class_exists('App\Models\FormatEvaluationUniversite'),
            'Evaluation' => class_exists('App\Models\Evaluation'),
            'Universite' => class_exists('App\Models\Universite'),
            'Stage' => class_exists('App\Models\Stage'),
            'Agent' => class_exists('App\Models\Agent'),
        ];
        
        // Test 2: Vérifier les tables
        $tests['tables'] = [
            'formats_evaluation_universites' => \Schema::hasTable('formats_evaluation_universites'),
            'evaluations' => \Schema::hasTable('evaluations'),
            'universites' => \Schema::hasTable('universites'),
            'stages' => \Schema::hasTable('stages'),
        ];
        
        // Test 3: Vérifier les colonnes
        $tests['columns'] = [
            'evaluations.criteres_personnalises' => \Schema::hasColumn('evaluations', 'criteres_personnalises'),
            'evaluations.format_evaluation_universite_id' => \Schema::hasColumn('evaluations', 'format_evaluation_universite_id'),
            'evaluations.validee_par_ru' => \Schema::hasColumn('evaluations', 'validee_par_ru'),
        ];
        
        // Test 4: Compter les données existantes
        $tests['data'] = [
            'universites_count' => \App\Models\Universite::count(),
            'stages_count' => \App\Models\Stage::count(),
            'evaluations_count' => \App\Models\Evaluation::count(),
            'formats_count' => \App\Models\FormatEvaluationUniversite::count(),
            'agents_ru_count' => \App\Models\Agent::where('role_agent', 'RU')->count(),
        ];
        
        // Test 5: Vérifier les contrôleurs
        $tests['controllers'] = [
            'RU_EvaluationController' => class_exists('App\Http\Controllers\Agent\RU\EvaluationController'),
            'MS_StageController' => class_exists('App\Http\Controllers\Agent\MS\StageController'),
        ];
        
        return response()->json([
            'status' => 'success',
            'message' => 'Tests du système d\'évaluation',
            'tests' => $tests,
            'timestamp' => now()->toDateTimeString()
        ], 200, [], JSON_PRETTY_PRINT);
        
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Erreur lors des tests',
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 500, [], JSON_PRETTY_PRINT);
    }
});

// Test de création d'un format simple
Route::get('/test-create-format-simple', function () {
    try {
        // Trouver une université
        $universite = \App\Models\Universite::first();
        
        if (!$universite) {
            return response()->json([
                'status' => 'error',
                'message' => 'Aucune université trouvée'
            ]);
        }
        
        // Créer un format de test
        $format = \App\Models\FormatEvaluationUniversite::create([
            'universite_id' => $universite->id,
            'nombre_criteres' => 5,
            'points_par_critere' => 4,
            'criteres' => [
                'Compétences techniques',
                'Autonomie',
                'Respect des délais',
                'Qualité du travail',
                'Intégration équipe'
            ],
            'cree_par_agent_id' => 1,
            'actif' => true
        ]);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Format créé avec succès',
            'format' => $format,
            'universite' => $universite
        ], 200, [], JSON_PRETTY_PRINT);
        
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Erreur lors de la création du format',
            'error' => $e->getMessage()
        ], 500, [], JSON_PRETTY_PRINT);
    }
});

// Test de création d'une évaluation académique
Route::get('/test-create-evaluation-simple', function () {
    try {
        // Trouver un stage académique
        $stage = \App\Models\Stage::where('type', 'academique')->first();
        
        if (!$stage) {
            return response()->json([
                'status' => 'error',
                'message' => 'Aucun stage académique trouvé'
            ]);
        }
        
        // Trouver ou créer un format
        $format = \App\Models\FormatEvaluationUniversite::first();
        
        if (!$format && $stage->stagiaire && $stage->stagiaire->universite_id) {
            $format = \App\Models\FormatEvaluationUniversite::create([
                'universite_id' => $stage->stagiaire->universite_id,
                'nombre_criteres' => 5,
                'points_par_critere' => 4,
                'criteres' => [
                    'Compétences techniques',
                    'Autonomie', 
                    'Respect des délais',
                    'Qualité du travail',
                    'Intégration équipe'
                ],
                'cree_par_agent_id' => 1,
                'actif' => true
            ]);
        }
        
        // Créer une évaluation
        $evaluation = \App\Models\Evaluation::create([
            'stage_id' => $stage->id,
            'agent_id' => 1,
            'criteres_personnalises' => [3.5, 4.0, 3.0, 3.5, 4.0],
            'format_evaluation_universite_id' => $format ? $format->id : null,
            'note_totale' => 18.0,
            'commentaire_general' => 'Excellent stagiaire, très motivé.',
            'date_evaluation' => now(),
            'validee_par_ru' => false
        ]);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Évaluation créée avec succès',
            'evaluation' => $evaluation,
            'stage' => $stage,
            'format' => $format
        ], 200, [], JSON_PRETTY_PRINT);
        
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => 'Erreur lors de la création de l\'évaluation',
            'error' => $e->getMessage()
        ], 500, [], JSON_PRETTY_PRINT);
    }
});
