<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réponse de la structure</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header.success {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        }
        .header.danger {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .info-box {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .info-box.success {
            background-color: #ecfdf5;
            border-left-color: #10b981;
        }
        .info-box.danger {
            background-color: #fef2f2;
            border-left-color: #ef4444;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .details-table th {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .footer {
            background-color: #f3f4f6;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header {{ $decision === 'acceptee' ? 'success' : 'danger' }}">
            <img src="{{ asset('images/logo-ministere.png') }}" alt="Ministère de l'Économie et des Finances" class="logo">
            <h1>
                @if($decision === 'acceptee')
                    ✅ Demande acceptée
                @else
                    ❌ Demande refusée
                @endif
            </h1>
            <p>Réponse de {{ $structure->libelle }}</p>
        </div>

        <div class="content">
            <div class="info-box {{ $decision === 'acceptee' ? 'success' : 'danger' }}">
                <h3>
                    @if($decision === 'acceptee')
                        🎉 Bonne nouvelle !
                    @else
                        📋 Action requise
                    @endif
                </h3>
                <p>
                    @if($decision === 'acceptee')
                        La structure {{ $structure->libelle }} a accepté d'accueillir le stagiaire. Vous pouvez maintenant procéder à l'affectation du maître de stage.
                    @else
                        La structure {{ $structure->libelle }} a refusé la demande. Une réaffectation vers une autre structure est nécessaire.
                    @endif
                </p>
            </div>

            <h2>Détails de la demande</h2>
            
            <table class="details-table">
                <tr>
                    <th>Code de suivi</th>
                    <td><strong>{{ $demande->code_suivi }}</strong></td>
                </tr>
                <tr>
                    <th>Stagiaire</th>
                    <td>{{ $stagiaire->prenom }} {{ $stagiaire->nom }}</td>
                </tr>
                <tr>
                    <th>Structure</th>
                    <td>{{ $structure->libelle }}</td>
                </tr>
                <tr>
                    <th>Décision</th>
                    <td>
                        @if($decision === 'acceptee')
                            <span style="color: #059669; font-weight: bold;">✅ ACCEPTÉE</span>
                        @else
                            <span style="color: #dc2626; font-weight: bold;">❌ REFUSÉE</span>
                        @endif
                    </td>
                </tr>
                <tr>
                    <th>Date de réponse</th>
                    <td>{{ \Carbon\Carbon::now()->locale('fr')->isoFormat('DD MMMM YYYY à HH:mm') }}</td>
                </tr>
                @if($justification)
                <tr>
                    <th>Justification</th>
                    <td>{{ $justification }}</td>
                </tr>
                @endif
            </table>

            @if($decision === 'acceptee')
                <div class="info-box success">
                    <h4>📋 Prochaines étapes</h4>
                    <ul>
                        <li>Affecter un maître de stage dans la structure</li>
                        <li>Définir les dates exactes du stage</li>
                        <li>Notifier le stagiaire de son affectation</li>
                        <li>Préparer les documents administratifs</li>
                    </ul>
                </div>
            @else
                <div class="info-box danger">
                    <h4>📋 Actions requises</h4>
                    <ul>
                        <li>Analyser les motifs du refus</li>
                        <li>Identifier une structure alternative</li>
                        <li>Réassigner la demande</li>
                        <li>Informer le stagiaire si nécessaire</li>
                    </ul>
                </div>
            @endif

            <div style="text-align: center;">
                <a href="{{ route('agent.dpaf.demandes.show', $demande->id) }}" class="btn">
                    @if($decision === 'acceptee')
                        Procéder à l'affectation
                    @else
                        Réassigner la demande
                    @endif
                </a>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Ministère de l'Économie et des Finances du Bénin</strong><br>
                Direction de la Planification et de l'Administration Financière (DPAF)<br>
                Système de gestion des stages
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                Cet email a été envoyé automatiquement. Merci de ne pas répondre à cette adresse.
            </p>
        </div>
    </div>
</body>
</html>
