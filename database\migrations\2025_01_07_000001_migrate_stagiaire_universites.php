<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Stagiaire;
use App\Models\Universite;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migration pour convertir les noms d'université en texte vers les relations avec la table universites
     */
    public function up(): void
    {
        Log::info('🎯 DÉBUT - Migration des universités des stagiaires');
        
        // Récupérer tous les stagiaires qui ont une université en texte mais pas de relation
        $stagiaires = Stagiaire::whereNotNull('universite')
            ->whereNull('universite_id')
            ->get();
            
        Log::info("📊 Nombre de stagiaires à migrer: {$stagiaires->count()}");
        
        $migratedCount = 0;
        $notFoundCount = 0;
        $universitesMissing = [];
        
        foreach ($stagiaires as $stagiaire) {
            $nomUniversite = trim($stagiaire->universite);
            
            if (empty($nomUniversite)) {
                continue;
            }
            
            // Chercher l'université correspondante
            $universite = Universite::where('nom_complet', $nomUniversite)
                ->orWhere('nom_universite', $nomUniversite)
                ->orWhere(function($query) use ($nomUniversite) {
                    // Recherche approximative pour gérer les variations de noms
                    $query->where('nom_complet', 'LIKE', "%{$nomUniversite}%")
                          ->orWhere('nom_universite', 'LIKE', "%{$nomUniversite}%");
                })
                ->first();
                
            if ($universite) {
                // Mettre à jour le stagiaire avec la relation
                $stagiaire->update([
                    'universite_id' => $universite->id,
                    'universite' => $universite->nom_complet // Normaliser le nom
                ]);
                
                $migratedCount++;
                Log::info("✅ Stagiaire {$stagiaire->id_stagiaire}: '{$nomUniversite}' → Université ID {$universite->id}");
            } else {
                // Université non trouvée - la créer automatiquement
                $nouvelleUniversite = Universite::create([
                    'nom_complet' => $nomUniversite,
                    'nom_universite' => $nomUniversite, // Compatibilité
                    'sigle' => $this->generateSigle($nomUniversite),
                    'localisation' => 'Non renseignée',
                    'active' => true,
                    'responsable_id' => null,
                    'description' => 'Université créée automatiquement lors de la migration des données existantes'
                ]);
                
                // Mettre à jour le stagiaire
                $stagiaire->update([
                    'universite_id' => $nouvelleUniversite->id,
                    'universite' => $nouvelleUniversite->nom_complet
                ]);
                
                $migratedCount++;
                Log::info("🆕 Stagiaire {$stagiaire->id_stagiaire}: Nouvelle université créée '{$nomUniversite}' → ID {$nouvelleUniversite->id}");
            }
        }
        
        Log::info("✅ TERMINÉ - Migration des universités:");
        Log::info("   - Stagiaires migrés: {$migratedCount}");
        Log::info("   - Universités non trouvées: {$notFoundCount}");
        
        if (!empty($universitesMissing)) {
            Log::warning("⚠️  Universités manquantes:", $universitesMissing);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Log::info('🔄 ROLLBACK - Suppression des relations université des stagiaires');
        
        // Remettre à null les relations université_id
        Stagiaire::whereNotNull('universite_id')->update([
            'universite_id' => null
        ]);
        
        Log::info('✅ Rollback terminé - Relations université supprimées');
    }
    
    /**
     * Générer un sigle à partir du nom de l'université
     */
    private function generateSigle(string $nomUniversite): string
    {
        // Extraire les premières lettres des mots principaux
        $mots = explode(' ', $nomUniversite);
        $sigle = '';
        
        foreach ($mots as $mot) {
            $mot = trim($mot);
            if (strlen($mot) > 2 && !in_array(strtolower($mot), ['de', 'du', 'des', 'le', 'la', 'les', 'et', 'ou'])) {
                $sigle .= strtoupper(substr($mot, 0, 1));
            }
        }
        
        // Si le sigle est trop court, prendre les 3 premiers caractères du nom
        if (strlen($sigle) < 2) {
            $sigle = strtoupper(substr(str_replace(' ', '', $nomUniversite), 0, 3));
        }
        
        // Limiter à 10 caractères maximum
        return substr($sigle, 0, 10);
    }
};
