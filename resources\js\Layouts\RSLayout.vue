<template>
    <div class="flex h-screen rs-bg">
        <!-- SIDEBAR STATIQUE -->
        <aside
            :class="[
                'sidebar-glass text-white transition-all duration-300 ease-in-out flex flex-col shadow-lg z-30',
                'relative flex-shrink-0',
                sidebarExpanded ? 'w-64' : 'w-20'
            ]"
        >
            <!-- Header du Sidebar -->
            <div class="p-4 border-b border-blue-500/30">
                <div class="flex items-center justify-between">
                    <Link :href="route('agent.rs.dashboard')" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center shadow-sm">
                            <span class="text-gray-600 font-bold text-lg">GS</span>
                        </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" key="logo-text" class="transition-all duration-300">
                            <h1 class="text-lg font-bold text-white">Agent RS</h1>
                            <p class="text-xs text-blue-200">Ministère des Finances</p>
                        </div>
                        </transition>
                    </Link>
                    <button
                        @click="toggleSidebar"
                        class="p-2 rounded-lg hover:bg-blue-500/30 transition-colors duration-200"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 p-4 space-y-2">
                <Link
                    v-for="item in menuItems"
                    :key="item.route"
                    :href="route(item.route)"
                    :class="[
                        'flex items-center rounded-xl text-sm font-medium transition-all duration-200 group relative',
                        sidebarExpanded ? 'px-4 py-3' : 'justify-center py-3 mx-2',
                        isActive(item.active)
                            ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/10'
                            : 'text-blue-100 hover:bg-white/10 hover:text-white hover:shadow-md'
                    ]"
                >
                    <!-- Indicateur actif -->
                    <div
                        v-if="isActive(item.active)"
                        class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full"
                    ></div>

                    <svg class="w-5 h-5 flex-shrink-0 transition-all duration-200"
                         :class="isActive(item.active) ? 'text-white' : 'text-blue-200 group-hover:text-white'"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                    </svg>
                    <transition name='fade' mode='out-in'>
                    <span
                        v-if="sidebarExpanded"
                        class="ml-3 transition-all duration-300 font-medium"
                          :key="`label-${item.route}`"
                    >
                        {{ item.name }}
                    </span>
                    </transition>

                    <!-- Badge pour les éléments actifs -->
                    <div
                        v-if="isActive(item.active) && sidebarExpanded"
                        class="ml-auto w-2 h-2 bg-white rounded-full opacity-80"
                    ></div>
                </Link>

                <!-- Lien DPAF spécial -->
                <Link
                    v-if="isDpafResponsable"
                    :href="route('agent.dashboard')"
                    :class="[
                        'flex items-center rounded-xl text-sm font-medium transition-all duration-200 group relative',
                        sidebarExpanded ? 'px-4 py-3' : 'justify-center py-3 mx-2',
                        isActive('agent.dashboard*')
                            ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/10'
                            : 'text-blue-100 hover:bg-white/10 hover:text-white hover:shadow-md'
                    ]"
                >
                    <!-- Indicateur actif -->
                    <div
                        v-if="isActive('agent.dashboard*')"
                        class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full"
                    ></div>

                    <svg class="w-5 h-5 flex-shrink-0 transition-all duration-200"
                         :class="isActive('agent.dashboard*') ? 'text-white' : 'text-blue-200 group-hover:text-white'"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <transition name='fade' mode='out-in'>
                    <span
                        v-if="sidebarExpanded"
                        class="ml-3 transition-all duration-300 font-medium"
                        key="dpaf-label"
                    >
                        Espace DPAF
                    </span>
                    </transition>

                    <!-- Badge pour les éléments actifs -->
                    <div
                        v-if="isActive('agent.dashboard*') && sidebarExpanded"
                        class="ml-auto w-2 h-2 bg-white rounded-full opacity-80"
                    ></div>
                </Link>
            </nav>

            <!-- APPROCHE CHIRURGICALE : Cloche de notifications intégrée dans la sidebar -->
            <div class="px-4 pb-2 border-t border-blue-500/30">
                <div class="flex items-center justify-center pt-3">
                    <div class="relative">
                        <button @click="showNotifications = !showNotifications" class="relative focus:outline-none group">
                            <div class="p-3 rounded-full bg-white/10 hover:bg-white/20 border border-white/20 transition-all duration-200 group-hover:shadow-lg">
                                <svg class="w-6 h-6 text-white transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                </svg>
                            </div>
                            <span v-if="($page.props.notifications || []).length" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-2 py-1 font-medium shadow-md min-w-[1.25rem] text-center">
                                {{ ($page.props.notifications || []).length > 99 ? '99+' : ($page.props.notifications || []).length }}
                            </span>
                        </button>

                        <!-- Panel de notifications -->
                        <div v-if="showNotifications" class="absolute bottom-full right-0 mb-2 w-96 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden animate-fade-in z-50">
                            <div class="p-4 border-b bg-blue-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                        </svg>
                                        <h3 class="text-lg font-semibold text-blue-900">Notifications</h3>
                                    </div>
                                    <div class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                                        {{ ($page.props.notifications || []).length }}
                                    </div>
                                </div>
                            </div>

                            <div v-if="($page.props.notifications || []).length === 0" class="p-8 text-center">
                                <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                                </svg>
                                <p class="text-gray-500 text-sm">Aucune notification</p>
                            </div>

                            <ul v-else class="divide-y divide-gray-100 max-h-96 overflow-y-auto">
                                <li v-for="notif in ($page.props.notifications || [])" :key="notif.id" class="p-4 hover:bg-gray-50 transition-colors cursor-pointer" @click="markAsRead(notif.id)">
                                    <div class="flex items-start gap-3">
                                        <div class="pt-1 flex-shrink-0">
                                            <div class="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100">
                                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="text-sm font-medium text-gray-900 mb-1 leading-relaxed" v-html="notif.data.message"></div>
                                            <div class="text-xs text-gray-500">{{ formatDate(notif.created_at) }}</div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Profile Section avec menu personnalisé -->
            <div class="p-4 relative">
                <div class="relative">
                    <!-- Bouton du profil utilisateur -->
                    <button
                        @click="toggleUserMenu"
                        :class="[
                            'w-full flex items-center rounded-lg text-sm font-medium text-blue-100 bg-blue-500/20 transition-colors hover:bg-blue-500/30',
                            sidebarExpanded ? 'px-3 py-3' : 'justify-center py-3'
                        ]"
                    >
                    <div class="relative flex-shrink-0">
                        <img
                            v-if="user && user.avatar"
                            :src="'/storage/' + user.avatar"
                            alt="Photo de profil"
                            class="w-8 h-8 rounded-full object-cover border-2 border-blue-300"
                        />
                            <div v-else class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold border-2 border-blue-300">
                            {{ user?.nom?.charAt(0) || 'R' }}
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-blue-600 rounded-full"></div>
                    </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" class="ml-3 text-left flex-1" key="user-info">
                        <div class="font-medium text-white text-sm">{{ user?.nom || 'Agent RS' }}</div>
                        <div class="text-xs text-blue-200 truncate">Agent RS</div>
                    </div>
                        </transition>
                        <transition name='fade' mode='out-in'>
                          <svg v-if="sidebarExpanded" class="w-4 h-4 text-blue-200 ml-2 transition-transform" :class="{ 'rotate-180': showUserMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                          </svg>
                        </transition>
                    </button>

                    <!-- Menu contextuel personnalisé -->
                    <transition name="menu-fade">
                        <div
                            v-if="showUserMenu"
                            class="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                        >
                            <!-- Info utilisateur -->
                            <div class="px-4 py-3 border-b border-gray-100">
                                <div class="font-medium text-base text-gray-800">{{ user?.nom || 'Agent RS' }}</div>
                                <div class="font-medium text-sm text-gray-500">{{ user?.email || 'Email non disponible' }}</div>
                            </div>

                            <!-- Options du menu -->
                            <div class="py-1">
                                <Link
                                    :href="route('profile.edit')"
                                    @click="closeUserMenu"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Mon Profil
                                </Link>
                                <Link
                                    :href="route('logout')"
                                    method="post"
                                    as="button"
                                    @click="closeUserMenu"
                                    class="w-full text-left flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                >
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                    Déconnexion
                                </Link>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
        </aside>

        <!-- Overlay mobile -->
        <div
            v-if="sidebarExpanded"
            @click="toggleSidebar"
            class="sidebar-overlay lg:hidden"
            :class="{ 'show': sidebarExpanded }"
        ></div>

        <!-- MAIN CONTENT AREA -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- Header glassmorphism -->
            <header class="header-glass shadow-sm border-b border-gray-200 z-20">
                <div class="flex items-center justify-between px-4 md:px-6 py-3 md:py-4 mx-auto max-w-6xl">
                    <div class="flex items-center space-x-4">
                        <!-- Bouton toggle mobile -->
                        <button
                            @click="toggleSidebar"
                            class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                        >
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>

                        <!-- Logo Ministère -->
                        <div class="flex-shrink-0">
                            <div class="bg-gradient-to-br from-green-50 to-green-100 p-2 rounded-lg border border-green-200/50 shadow-sm">
                                <img
                                    src="/images/logoministere.png"
                                    alt="Logo du Ministère"
                                    class="h-8 w-auto object-contain"
                                />
                            </div>
                        </div>
                        <div class="hidden md:block w-px h-8 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                        <!-- Titre et description -->
                        <div class="hidden sm:block">
                            <h1 class="text-xl md:text-2xl font-bold text-gray-800 mb-1">Responsable Structure</h1>
                            <p class="text-gray-600 text-xs md:text-sm">Gestion des Stages - Ministère des Finances</p>
                        </div>

                        <!-- Version mobile du titre -->
                        <div class="sm:hidden">
                            <h1 class="text-lg font-bold text-gray-800">RS</h1>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <!-- Cloche de notifications -->
                        <NotificationBell
                            :notifications="$page.props.notifications || []"
                            :notifications-route="route('agent.rs.notifications.index')"
                        />

                        <div class="hidden lg:flex items-center space-x-3">
                            <div class="flex items-center space-x-2 bg-green-50 text-green-700 px-3 py-2 rounded-full text-sm font-medium border border-green-200">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span>Espace RS</span>
                            </div>
                        </div>
                        <div class="lg:hidden bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                <span>Espace RS</span>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- AMÉLIORATION URGENTE : Cloche de notifications RS -->
            <RealNotificationBell
                user-role="agent"
                theme="blue"
                :notifications="props.notifications"
                positioning="fixed"
            />

            <!-- En-tête de la page -->
            <header v-if="$slots.header"
                class="page-header bg-gradient-to-r from-green-50/50 to-emerald-50/30 border-b border-green-100/50">
                <div class="px-4 md:px-6 py-3 md:py-4 mx-auto max-w-6xl">
                    <slot name="header" />
                </div>
            </header>

            <!-- Contenu principal -->
            <main class="flex-1 overflow-auto bg-gradient-to-br from-gray-50/30 to-green-50/20">
                <div class="px-3 sm:px-4 md:px-6 py-3 md:py-4 max-w-full">
                    <slot />
                </div>
            </main>

            <!-- Footer glassmorphism -->
            <footer class="footer-glass border-t border-gray-200/50 backdrop-blur-sm">
                <div class="max-w-6xl mx-auto px-3 sm:px-4 md:px-6 py-3 md:py-4">
                    <div class="flex flex-col sm:flex-row items-center justify-between space-y-2 sm:space-y-0">
                        <div class="text-center md:text-left">
                            <p class="text-sm font-medium text-gray-700">
                                © {{ new Date().getFullYear() }} Gestion des Stages
                            </p>
                            <p class="text-xs text-gray-500">
                                Espace Responsable de Structure
                            </p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="hidden md:flex items-center space-x-2 text-xs text-gray-500">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span>Système opérationnel</span>
                            </div>
                            <div class="text-xs text-gray-400 font-mono bg-gray-100 px-2 py-1 rounded">
                                Build 2024.11.28
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>


    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Link, usePage, router } from '@inertiajs/vue3'
import RealNotificationBell from '@/Components/RealNotificationBell.vue'
import NotificationBell from '@/Components/NotificationBell.vue'

// AMÉLIORATION URGENTE : Props pour les notifications
const props = defineProps({
    notifications: {
        type: Array,
        default: () => []
    }
})

const sidebarExpanded = ref(window.innerWidth >= 1024)
const showUserMenu = ref(false)
const showNotifications = ref(false) // APPROCHE CHIRURGICALE : État pour les notifications
const user = usePage().props.auth?.user

const page = usePage()
const isDpafResponsable = computed(() => {
    return page.props.structure?.sigle === 'DPAF';
})

const menuItems = [
    {
        name: 'Tableau de bord',
        route: 'agent.rs.dashboard',
        active: 'agent.rs.dashboard',
        icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6'
    },
    {
        name: 'Demandes',
        route: 'agent.rs.demandes',
        active: 'agent.rs.demandes*',
        icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2'
    },
    {
        name: 'Stages',
        route: 'agent.rs.stages',
        active: 'agent.rs.stages*',
        icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
    },
    {
        name: 'Agents',
        route: 'agent.rs.agents.index',
        active: 'agent.rs.agents*',
        icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z'
    },
    {
        name: 'Organigramme',
        route: 'agent.rs.organigramme.index',
        active: 'agent.rs.organigramme*',
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    }
]

const toggleSidebar = () => {
    sidebarExpanded.value = !sidebarExpanded.value
}

const toggleUserMenu = () => {
    showUserMenu.value = !showUserMenu.value
}

const closeUserMenu = () => {
    showUserMenu.value = false
}

// APPROCHE CHIRURGICALE : Fonctions pour gérer les notifications
const formatDate = (date) => {
    return new Date(date).toLocaleString('fr-FR')
}

const markAsRead = (notificationId) => {
    router.post(route('agent.notifications.markAsRead', notificationId), {}, {
        preserveScroll: true,
    })
}

const isActive = (routeName) => {
    return route().current(routeName)
}

// Gestion responsive
const handleResize = () => {
    if (window.innerWidth >= 1024) {
        sidebarExpanded.value = true
    } else {
        sidebarExpanded.value = false
    }
}

onMounted(() => {
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* Glassmorphism pour la sidebar */
.sidebar-glass {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.95) 0%, 
        rgba(37, 99, 235, 0.98) 50%, 
        rgba(29, 78, 216, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 25px 45px rgba(0, 0, 0, 0.1), 
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Logo avec effet gradient */
.logo-gradient {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 16px;
    color: #2563eb;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
}

.logo-gradient::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.logo-gradient:hover::before {
    opacity: 1;
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Menu items avec indicateur actif */
.menu-link {
    position: relative;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-link:hover {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transform: translateX(4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.menu-link.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.active-indicator {
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(to bottom, #ffffff, #f1f5f9);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
}

/* Tooltips pour sidebar réduite */
.tooltip {
    position: absolute;
    left: 60px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 1000;
}

.menu-link:hover .tooltip {
    opacity: 1;
    left: 65px;
}

/* Séparateur élégant */
.separator {
    margin: 16px;
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* Section utilisateur repensée */
.user-section {
    padding: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.user-name {
    color: white;
    font-weight: 600;
    font-size: 14px;
}

/* Header glassmorphism */
.header-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

/* Bouton notifications */
.notification-button {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
}

.notification-badge {
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* Page header */
.page-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(99, 102, 241, 0.03));
    backdrop-filter: blur(10px);
}

/* Main content */
.main-content {
    background: linear-gradient(135deg, 
        rgba(248, 250, 252, 0.8) 0%, 
        rgba(239, 246, 255, 0.6) 50%, 
        rgba(224, 242, 254, 0.4) 100%
    );
}

/* Footer glassmorphism */
.footer-glass {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
}

/* Sidebar toggle */
.sidebar-toggle {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Transitions */
.fade-enter-active, .fade-leave-active {
    transition: all 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar-glass {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 50;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .sidebar-glass.expanded {
        transform: translateX(0);
    }

    /* Overlay pour mobile */
    .sidebar-overlay {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 40;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease-in-out;
    }

    .sidebar-overlay.show {
        opacity: 1;
        pointer-events: auto;
    }
}

@media (max-width: 768px) {
    .header-glass {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .main-content {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .footer-glass {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Amélioration des cartes sur mobile */
    .bg-white.rounded-xl {
        margin-left: 0;
        margin-right: 0;
    }
}

@media (max-width: 640px) {
    .sidebar-glass {
        width: 280px;
    }

    .header-glass .px-4 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .main-content .px-4 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

/* APPROCHE CHIRURGICALE : Animation pour les notifications */
.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
</style>