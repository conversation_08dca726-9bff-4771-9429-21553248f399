<template>
  <div class="relative">
    <label v-if="label" :class="['block text-sm font-medium mb-2', required ? 'required' : '']">
      {{ label }}
    </label>
    
    <!-- Dropdown avec recherche -->
    <div class="relative">
      <button
        type="button"
        @click="toggleDropdown"
        :class="[
          'w-full px-4 py-3 text-left border rounded-lg focus:ring-2 focus:border-transparent transition-all duration-200',
          'bg-white hover:bg-gray-50',
          error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-emerald-500 focus:border-emerald-500',
          disabled ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer'
        ]"
        :disabled="disabled"
      >
        <div class="flex items-center justify-between">
          <span :class="selectedUniversite ? 'text-gray-900' : 'text-gray-500'">
            {{ selectedUniversite ? selectedUniversite.nom_complet : placeholder }}
          </span>
          <svg 
            :class="['w-5 h-5 transition-transform duration-200', isOpen ? 'rotate-180' : '']"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      <!-- Dropdown Menu -->
      <div 
        v-if="isOpen" 
        class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-hidden"
      >
        <!-- Champ de recherche -->
        <div class="p-3 border-b border-gray-200">
          <div class="relative">
            <input
              ref="searchInput"
              v-model="searchQuery"
              type="text"
              placeholder="Rechercher une université..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
              @keydown.escape="closeDropdown"
              @keydown.enter.prevent="selectFirstFiltered"
            />
            <svg class="absolute right-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        <!-- Liste des universités -->
        <div class="max-h-48 overflow-y-auto">
          <div v-if="filteredUniversites.length === 0" class="px-4 py-3 text-sm text-gray-500 text-center">
            Aucune université trouvée
          </div>
          <button
            v-for="universite in filteredUniversites"
            :key="universite.id"
            type="button"
            @click="selectUniversite(universite)"
            class="w-full px-4 py-3 text-left hover:bg-emerald-50 focus:bg-emerald-50 focus:outline-none transition-colors duration-150"
          >
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900">{{ universite.nom_complet }}</div>
                <div class="text-sm text-gray-500">{{ universite.sigle }} • {{ universite.localisation }}</div>
              </div>
              <div v-if="selectedUniversite && selectedUniversite.id === universite.id" class="text-emerald-600">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Message d'erreur -->
    <p v-if="error" class="mt-2 text-sm text-red-600">{{ error }}</p>
    
    <!-- Information supplémentaire -->
    <p v-if="selectedUniversite && selectedUniversite.description" class="mt-2 text-xs text-gray-500">
      {{ selectedUniversite.description }}
    </p>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number, Object],
    default: null
  },
  universites: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: 'Université'
  },
  placeholder: {
    type: String,
    default: 'Sélectionner une université'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const isOpen = ref(false)
const searchQuery = ref('')
const searchInput = ref(null)

// Université sélectionnée
const selectedUniversite = computed(() => {
  if (!props.modelValue) return null
  
  // Si modelValue est un objet, le retourner directement
  if (typeof props.modelValue === 'object') {
    return props.modelValue
  }
  
  // Si modelValue est un ID ou nom, chercher dans la liste
  return props.universites.find(u => 
    u.id === props.modelValue || 
    u.nom_complet === props.modelValue ||
    u.nom_universite === props.modelValue
  )
})

// Universités filtrées par la recherche
const filteredUniversites = computed(() => {
  if (!searchQuery.value) {
    return props.universites.filter(u => u.active === true)
  }

  const query = searchQuery.value.toLowerCase()
  return props.universites.filter(u =>
    u.active === true && (
      u.nom_complet?.toLowerCase().includes(query) ||
      u.sigle?.toLowerCase().includes(query) ||
      u.localisation?.toLowerCase().includes(query)
    )
  )
})

const toggleDropdown = () => {
  if (props.disabled) return
  
  isOpen.value = !isOpen.value
  
  if (isOpen.value) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
}

const closeDropdown = () => {
  isOpen.value = false
  searchQuery.value = ''
}

const selectUniversite = (universite) => {
  emit('update:modelValue', universite.nom_complet)
  emit('change', universite)
  closeDropdown()
}

const selectFirstFiltered = () => {
  if (filteredUniversites.value.length > 0) {
    selectUniversite(filteredUniversites.value[0])
  }
}

// Fermer le dropdown en cliquant à l'extérieur
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Réinitialiser la recherche quand le dropdown se ferme
watch(isOpen, (newValue) => {
  if (!newValue) {
    searchQuery.value = ''
  }
})
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
