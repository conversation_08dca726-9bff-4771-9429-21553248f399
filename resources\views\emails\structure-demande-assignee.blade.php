<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demande de stage assignée</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .info-box {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .details-table th {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 10px 5px;
        }
        .btn-success {
            background-color: #10b981;
        }
        .btn-danger {
            background-color: #ef4444;
        }
        .footer {
            background-color: #f3f4f6;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
        }
        .urgent {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="{{ asset('images/logo-ministere.png') }}" alt="Ministère de l'Économie et des Finances" class="logo">
            <h1>Demande de stage assignée</h1>
            <p>{{ $structure->libelle }}</p>
        </div>

        <div class="content">
            <div class="urgent">
                <h3>📋 Demande à examiner</h3>
                <p>Une demande de stage a été assignée à votre structure. Votre réponse est attendue dans les meilleurs délais.</p>
            </div>

            <h2>Profil du stagiaire</h2>
            
            <table class="details-table">
                <tr>
                    <th>Code de suivi</th>
                    <td><strong>{{ $demande->code_suivi }}</strong></td>
                </tr>
                <tr>
                    <th>Nom complet</th>
                    <td>{{ $stagiaire->prenom }} {{ $stagiaire->nom }}</td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td>{{ $stagiaire->email }}</td>
                </tr>
                <tr>
                    <th>Téléphone</th>
                    <td>{{ $stagiaire->telephone ?? 'Non renseigné' }}</td>
                </tr>
                <tr>
                    <th>Type de stage</th>
                    <td>{{ $demande->type }}</td>
                </tr>
                <tr>
                    <th>Nature</th>
                    <td>{{ $demande->nature }}</td>
                </tr>
                <tr>
                    <th>Période souhaitée</th>
                    <td>
                        Du {{ \Carbon\Carbon::parse($demande->date_debut_souhaitee)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                        au {{ \Carbon\Carbon::parse($demande->date_fin_souhaitee)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                        <br><small>({{ \Carbon\Carbon::parse($demande->date_debut_souhaitee)->diffInDays(\Carbon\Carbon::parse($demande->date_fin_souhaitee)) }} jours)</small>
                    </td>
                </tr>
                @if($demande->type === 'Académique')
                <tr>
                    <th>Université</th>
                    <td>{{ $demande->stagiaire->universite ?? 'Non renseignée' }}</td>
                </tr>
                <tr>
                    <th>Niveau d'études</th>
                    <td>{{ $demande->stagiaire->niveau_etudes ?? 'Non renseigné' }}</td>
                </tr>
                @endif
            </table>

            @if($demande->nature === 'Groupe')
                <div class="info-box">
                    <h4>👥 Demande de groupe</h4>
                    <p>Cette demande concerne un groupe de {{ $demande->membresGroupe->count() + 1 }} stagiaires. Votre décision s'appliquera à l'ensemble du groupe.</p>
                </div>
            @endif

            <div class="info-box">
                <h4>📋 Actions requises</h4>
                <ul>
                    <li>Examiner le profil du stagiaire et ses motivations</li>
                    <li>Vérifier la disponibilité de votre structure pour la période demandée</li>
                    <li>Évaluer la pertinence du stage par rapport aux activités de votre structure</li>
                    <li>Donner votre réponse (acceptation ou refus motivé)</li>
                </ul>
            </div>

            <div style="text-align: center;">
                <a href="{{ route('agent.rs.demandes.show', $demande->id) }}" class="btn btn-success">
                    Accepter la demande
                </a>
                <a href="{{ route('agent.rs.demandes.show', $demande->id) }}" class="btn btn-danger">
                    Refuser la demande
                </a>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <a href="{{ route('agent.rs.demandes.show', $demande->id) }}" class="btn">
                    Examiner la demande complète
                </a>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Ministère de l'Économie et des Finances du Bénin</strong><br>
                Direction de la Planification et de l'Administration Financière (DPAF)<br>
                Système de gestion des stages
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                Cet email a été envoyé automatiquement. Merci de ne pas répondre à cette adresse.
            </p>
        </div>
    </div>
</body>
</html>
