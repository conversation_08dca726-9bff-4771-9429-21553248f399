<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('formats_evaluation_universites', function (Blueprint $table) {
            // Index composé pour les requêtes fréquentes
            $table->index(['actif', 'created_at'], 'idx_actif_date');
            
            // Index pour les requêtes par nombre de critères
            $table->index('nombre_criteres', 'idx_nombre_criteres');
            
            // Index pour les requêtes par points par critère
            $table->index('points_par_critere', 'idx_points_par_critere');
            
            // Index composé pour optimiser les recherches par université et date
            $table->index(['universite_id', 'created_at'], 'idx_universite_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('formats_evaluation_universites', function (Blueprint $table) {
            $table->dropIndex('idx_actif_date');
            $table->dropIndex('idx_nombre_criteres');
            $table->dropIndex('idx_points_par_critere');
            $table->dropIndex('idx_universite_date');
        });
    }
};
