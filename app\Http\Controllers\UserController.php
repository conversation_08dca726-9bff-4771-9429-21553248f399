<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Agent;
use App\Models\Structure;
use App\Models\Stagiaire;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class UserController extends Controller
{
    private function generateUniqueMatricule()
    {
        do {
            // Génère un matricule au format AG-XXXXX (où X sont des chiffres)
            $matricule = 'AG-' . str_pad(rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while (Agent::where('matricule', $matricule)->exists());

        return $matricule;
    }

    public function index(Request $request)
    {
        // Vérifiez si l'utilisateur est admin
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Récupérer les paramètres de filtrage
        $roleFilter = $request->get('role');
        $structureFilter = $request->get('structure');
        $createdByFilter = $request->get('created_by');

        // Construction de la requête de base avec toutes les relations nécessaires
        $query = User::with([
            'agent.structure',
            'agent.creator',
            'agent.structuresResponsable',
            'agent.universiteResponsable',
            'agent.universitesGerees',
            'stagiaire'
        ])->select('id', 'nom', 'prenom', 'email', 'role', 'created_at', 'created_by');

        // Appliquer les filtres
        if ($roleFilter && $roleFilter !== 'all') {
            if ($roleFilter === 'agent_rs') {
                $query->whereHas('agent', function($q) {
                    $q->where('role_agent', 'RS');
                });
            } elseif ($roleFilter === 'agent_ms') {
                $query->whereHas('agent', function($q) {
                    $q->where('role_agent', 'MS');
                });
            } elseif ($roleFilter === 'agent_ru') {
                $query->whereHas('agent', function($q) {
                    $q->where('role_agent', 'RU');
                });
            } else {
                $query->where('role', $roleFilter);
            }
        }

        // Filtrage par structure
        if ($structureFilter && $structureFilter !== 'all') {
            $query->whereHas('agent', function($q) use ($structureFilter) {
                $q->where('structure_id', $structureFilter)
                  ->orWhereHas('structuresResponsable', function($sq) use ($structureFilter) {
                      $sq->where('id', $structureFilter);
                  });
            });
        }

        // Filtrage par créateur (pour voir qui a créé quoi)
        if ($createdByFilter && $createdByFilter !== 'all') {
            if ($createdByFilter === 'current_admin') {
                $query->where('created_by', Auth::id());
            } else {
                $query->where('created_by', $createdByFilter);
            }
        }

        $users = $query->orderBy('created_at', 'desc')->get();

        // Enrichir les données utilisateur
        $users = $users->map(function ($user) {
            $user->is_agent_rs_ms = $user->agent && in_array($user->agent->role_agent, ['RS', 'MS']);
            $user->agent_role = $user->agent ? $user->agent->role_agent : null;
            $user->structure_info = $user->agent && $user->agent->structure ? $user->agent->structure : null;
            $user->structures_responsable = $user->agent ? $user->agent->structuresResponsable : collect();
            $user->creator_info = $user->agent && $user->agent->creator ? $user->agent->creator : null;

            // Déterminer si l'admin actuel peut modifier cet utilisateur
            $user->can_modify = $this->canModifyUser($user);

            return $user;
        });

        // Récupérer les données pour les filtres
        $structures = Structure::with('parent')->orderBy('libelle')->get();
        // Structures disponibles pour Agent RS (sans responsable assigné)
        $structuresDisponibles = Structure::whereNull('parent_id')
            ->whereNull('responsable_id')
            ->orderBy('libelle')
            ->get();
        $universites = \App\Models\Universite::where('active', true)->orderBy('nom_complet')->get();
        $admins = User::where('role', 'admin')->select('id', 'nom', 'prenom', 'email')->get();

        return Inertia::render('Users/Index', [
            'users' => $users,
            'structures' => $structures,
            'structuresDisponibles' => $structuresDisponibles,
            'universites' => $universites,
            'admins' => $admins,
            'filters' => [
                'role' => $roleFilter,
                'structure' => $structureFilter,
                'created_by' => $createdByFilter,
            ],
            'agent_roles' => [
                'DPAF' => Agent::ROLE_DPAF,
                'MS' => Agent::ROLE_MS,
                'RS' => Agent::ROLE_RS,
                'RU' => Agent::ROLE_RU,
            ]
        ]);
    }

    /**
     * Détermine si l'admin actuel peut modifier un utilisateur donné
     */
    private function canModifyUser($user)
    {
        $currentAdmin = Auth::user();

        // Un admin ne peut pas se modifier lui-même via cette interface
        if ($user->id === $currentAdmin->id) {
            return false;
        }

        // Les stagiaires ne peuvent pas être modifiés via cette interface
        if ($user->role === 'stagiaire') {
            return false;
        }

        // Les agents RS/MS sont gérés via leurs interfaces dédiées
        if ($user->is_agent_rs_ms) {
            return false;
        }

        // Un admin créé ne peut pas modifier son créateur
        if ($user->created_by && $user->created_by === $currentAdmin->created_by && $user->created_at < $currentAdmin->created_at) {
            return false;
        }

        // Un admin ne peut pas modifier un autre admin qui ne l'a pas créé
        if ($user->role === 'admin' && $user->created_by !== $currentAdmin->id) {
            return false;
        }

        return true;
    }

    public function store(Request $request)
    {
        // Vérifiez si l'utilisateur est admin
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'role' => 'required|string|in:admin,agent',
            'password' => 'required|string|min:8|confirmed',
            'user_type' => 'required|string|in:admin,agent_rs,agent_ru', // Nouveau champ pour différencier
            'structure_responsable_id' => 'nullable|exists:structures,id', // Structure pour Agent RS
            'universite_responsable_id' => 'nullable|exists:universites,id', // Université pour Agent RU
            // Champs additionnels pour Agent RU
            'telephone' => 'required_if:user_type,agent_ru|string|max:20',
            'date_de_naissance' => 'required_if:user_type,agent_ru|date',
            'sexe' => 'required_if:user_type,agent_ru|in:Homme,Femme',
            'matricule' => 'required_if:user_type,agent_ru|string|unique:agents',
            'fonction' => 'required_if:user_type,agent_ru|string',
            'date_embauche' => 'nullable|date',
        ]);

        // Déterminer le rôle final basé sur user_type
        $finalRole = $validated['user_type'] === 'admin' ? 'admin' : 'agent';

        // Créer l'utilisateur avec les champs additionnels pour Agent RU
        $userData = [
            'nom' => $validated['nom'],
            'prenom' => $validated['prenom'],
            'email' => $validated['email'],
            'role' => $finalRole,
            'password' => Hash::make($validated['password']),
            'created_by' => Auth::id(), // Tracer qui a créé cet utilisateur
        ];

        // Ajouter les champs spécifiques pour Agent RU
        if ($validated['user_type'] === 'agent_ru') {
            $userData['telephone'] = $validated['telephone'];
            $userData['date_de_naissance'] = $validated['date_de_naissance'];
            $userData['sexe'] = $validated['sexe'];
        }

        $user = User::create($userData);

        // Si c'est un agent, créer l'entrée agent correspondante
        if ($validated['user_type'] === 'agent_rs') {
            $agent = Agent::create([
                'user_id' => $user->id,
                'matricule' => $this->generateUniqueMatricule(),
                'fonction' => 'Responsable de Structure',
                'role_agent' => 'RS',
                'date_embauche' => now(),
                'created_by' => Auth::id(),
            ]);

            // Assigner la structure si fournie
            if (!empty($validated['structure_responsable_id'])) {
                $structure = Structure::find($validated['structure_responsable_id']);
                if ($structure) {
                    // Vérifier si la structure n'a pas déjà un responsable
                    if ($structure->responsable_id) {
                        // Supprimer l'utilisateur et l'agent créés
                        $agent->delete();
                        $user->delete();
                        return redirect()->back()->with('error', 'Cette structure a déjà un responsable assigné.');
                    }

                    $structure->update(['responsable_id' => $agent->id]);
                }
            }
        } elseif ($validated['user_type'] === 'agent_ru') {
            $agent = Agent::create([
                'user_id' => $user->id,
                'matricule' => $validated['matricule'],
                'fonction' => $validated['fonction'],
                'role_agent' => 'RU',
                'date_embauche' => $validated['date_embauche'] ?? now(),
                'created_by' => Auth::id(),
            ]);

            // Assigner l'université si fournie
            if (!empty($validated['universite_responsable_id'])) {
                $universite = \App\Models\Universite::find($validated['universite_responsable_id']);
                if ($universite) {
                    // Vérifier si l'université n'a pas déjà un responsable
                    if ($universite->responsable_id) {
                        // Supprimer l'utilisateur et l'agent créés
                        $agent->delete();
                        $user->delete();
                        return redirect()->back()->with('error', 'Cette université a déjà un responsable assigné.');
                    }

                    $universite->update(['responsable_id' => $agent->id]);
                    // Mettre à jour l'agent avec l'université assignée
                    $agent->update(['universite_responsable_id' => $universite->id]);
                }
            }
        }

        $message = match($validated['user_type']) {
            'admin' => 'Administrateur ajouté avec succès.',
            'agent_rs' => 'Agent RS ajouté avec succès.',
            'agent_ru' => 'Agent RU ajouté avec succès.',
            default => 'Utilisateur ajouté avec succès.'
        };

        return redirect()->route('admin.users.index')->with('success', $message);
    }

    public function update(Request $request, User $user)
    {
        $currentAdmin = Auth::user();

        // Vérifier les permissions de modification
        if (!$this->canModifyUser($user)) {
            return redirect()->back()->with('error', 'Vous n\'avez pas l\'autorisation de modifier cet utilisateur.');
        }

        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            'action_type' => 'required|string|in:update_info,convert_to_agent_rs',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        if ($validated['action_type'] === 'convert_to_agent_rs') {
            // Convertir un admin en agent RS
            if ($user->role !== 'admin') {
                return redirect()->back()->with('error', 'Seuls les administrateurs peuvent être convertis en agents RS.');
            }

            // Mettre à jour le rôle
            $user->update(['role' => 'agent']);

            // Créer l'entrée agent
            Agent::create([
                'user_id' => $user->id,
                'matricule' => $this->generateUniqueMatricule(),
                'fonction' => 'Responsable de Structure',
                'role_agent' => 'RS',
                'date_embauche' => now(),
                'created_by' => $currentAdmin->id,
            ]);

            return redirect()->route('admin.users.index')->with('success', 'Administrateur converti en Agent RS avec succès.');
        } else {
            // Mise à jour normale des informations
            $updateData = [
                'nom' => $validated['nom'],
                'prenom' => $validated['prenom'],
                'email' => $validated['email'],
            ];

            if (!empty($validated['password'])) {
                $updateData['password'] = Hash::make($validated['password']);
            }

            $user->update($updateData);

            return redirect()->route('admin.users.index')->with('success', 'Informations utilisateur mises à jour avec succès.');
        }
    }

    public function destroy(User $user)
    {
        // Vérifier les permissions de suppression
        if (!$this->canModifyUser($user)) {
            return redirect()->back()->with('error', 'Vous n\'avez pas l\'autorisation de supprimer cet utilisateur.');
        }

        // Vérifier si l'utilisateur n'est pas l'utilisateur connecté
        if (Auth::id() === $user->id) {
            return redirect()->route('admin.users.index')->with('error', 'Vous ne pouvez pas supprimer votre propre compte.');
        }

        // Supprimer les relations associées si nécessaire
        if ($user->agent) {
            $user->agent->delete();
        }

        if ($user->stagiaire) {
            $user->stagiaire->delete();
        }

        $userName = $user->nom . ' ' . $user->prenom;
        $user->delete();

        return redirect()->route('admin.users.index')->with('success', "Utilisateur {$userName} supprimé avec succès.");
    }
}