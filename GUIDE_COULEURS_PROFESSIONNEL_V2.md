# 🎨 GUIDE DE COULEURS PROFESSIONNEL - GESTION DE STAGES
## Basé sur l'analyse de la page de référence `/admin/sous-structures`

---

## 📋 **PALETTE DE COULEURS PRINCIPALE**

### 🟣 **COULEURS PRIMAIRES - PURPLE/VIOLET**
```css
/* Gradients principaux pour headers et éléments importants */
bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600

/* Purple pour icônes et accents */
bg-purple-100        /* Arrière-plan icônes */
text-purple-600      /* Texte/icônes */
text-purple-900      /* Hover states */
```

### 🔵 **COULEURS SECONDAIRES - BLEU PROFESSIONNEL**
```css
/* Bleu pour badges et éléments secondaires */
bg-blue-100          /* Arrière-plan badges */
text-blue-600        /* Texte badges */
text-blue-800        /* Texte foncé badges */
```

### 🟢 **COULEURS D'ÉTAT - VERT**
```css
/* Vert pour statuts positifs */
bg-green-100         /* Arrière-plan */
text-green-600       /* Texte/icônes */
```

### 🟠 **COULEURS D'ALERTE - ORANGE**
```css
/* Orange pour alertes et avertissements */
bg-orange-100        /* Arrière-plan */
text-orange-600      /* Texte/icônes */
```

---

## 🏗️ **STRUCTURE ET ARRIÈRE-PLANS**

### ⚪ **ARRIÈRE-PLANS PRINCIPAUX**
```css
/* JAMAIS de blanc pur - toujours des nuances grises */
bg-white             /* Cartes et conteneurs principaux */
bg-gray-50           /* Arrière-plan général et headers de tableaux */
bg-gray-100          /* Bordures et séparateurs subtils */
```

### 🔲 **BORDURES ET SÉPARATEURS**
```css
border-gray-100      /* Bordures principales des cartes */
border-gray-200      /* Bordures plus marquées */
divide-gray-100      /* Séparateurs de lignes de tableau */
divide-gray-200      /* Séparateurs plus marqués */
```

### 🌫️ **OMBRES PROFESSIONNELLES**
```css
shadow-sm            /* Ombre subtile pour cartes */
shadow-lg            /* Ombre plus marquée pour éléments flottants */
```

---

## 📝 **TYPOGRAPHIE ET TEXTES**

### 📖 **HIÉRARCHIE DES TEXTES**
```css
/* Titres principaux */
text-gray-800        /* H1, titres de page */
text-gray-900        /* Titres de sections, données importantes */

/* Textes secondaires */
text-gray-600        /* Descriptions, sous-titres */
text-gray-500        /* Textes d'aide, métadonnées */
text-gray-400        /* Textes désactivés, placeholders */
```

### 🔤 **TAILLES ET POIDS**
```css
/* Titres */
text-2xl font-bold   /* Titres de page */
text-lg font-semibold /* Titres de sections */

/* Textes courants */
text-sm font-medium  /* Labels, textes importants */
text-sm              /* Texte standard */
text-xs font-medium  /* Headers de tableau */
```

---

## 🎯 **ÉLÉMENTS INTERACTIFS**

### 🔘 **BOUTONS ET LIENS**
```css
/* Boutons primaires */
bg-purple-600 hover:bg-purple-700 text-white

/* Liens */
text-purple-600 hover:text-purple-900

/* États hover pour lignes de tableau */
hover:bg-gray-50 transition
```

### 🏷️ **BADGES ET ÉTIQUETTES**
```css
/* Badge standard */
bg-gray-100 text-gray-800

/* Badge bleu (structures parent) */
bg-blue-100 text-blue-800

/* Styles communs */
inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
```

---

## 📊 **COMPOSANTS SPÉCIFIQUES**

### 📈 **CARTES DE STATISTIQUES**
```css
/* Structure de base */
bg-white rounded-xl shadow-sm border border-gray-100 p-6

/* Icônes colorées par type */
bg-purple-100 text-purple-600  /* Principal */
bg-green-100 text-green-600    /* Positif */
bg-orange-100 text-orange-600  /* Attention */
bg-blue-100 text-blue-600      /* Information */
```

### 📋 **TABLEAUX PROFESSIONNELS**
```css
/* Container */
bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden

/* Header */
bg-gray-50 border-b border-gray-200

/* Tableau */
min-w-full divide-y divide-gray-200
bg-gray-50 (thead)
bg-white divide-y divide-gray-100 (tbody)

/* Cellules */
px-6 py-4 whitespace-nowrap
px-6 py-3 (headers)
```

---

## ⚠️ **RÈGLES STRICTES À RESPECTER**

### ❌ **INTERDICTIONS ABSOLUES**
- **JAMAIS** de `bg-white` pur sans nuance
- **JAMAIS** de `text-black` ou couleurs noires pures
- **JAMAIS** de couleurs vives ou flashy
- **JAMAIS** de gradients colorés sauf purple/violet approuvé

### ✅ **BONNES PRATIQUES**
- Toujours utiliser les nuances de gris pour les arrière-plans
- Maintenir la cohérence des purple/violet pour les éléments principaux
- Utiliser les couleurs d'état (vert, orange, bleu) avec parcimonie
- Respecter la hiérarchie typographique établie
- Appliquer les transitions hover pour l'interactivité

---

## 🎨 **PALETTE COMPLÈTE DE RÉFÉRENCE**

```css
/* COULEURS PRINCIPALES */
Purple: from-purple-600 via-purple-700 to-violet-600
Purple Light: bg-purple-100, text-purple-600
Purple Dark: text-purple-900

/* COULEURS SECONDAIRES */
Blue: bg-blue-100, text-blue-600, text-blue-800
Green: bg-green-100, text-green-600
Orange: bg-orange-100, text-orange-600

/* ARRIÈRE-PLANS */
Primary: bg-white
Secondary: bg-gray-50
Tertiary: bg-gray-100

/* TEXTES */
Primary: text-gray-900
Secondary: text-gray-800
Tertiary: text-gray-600
Quaternary: text-gray-500
Disabled: text-gray-400

/* BORDURES */
Light: border-gray-100
Medium: border-gray-200
Dividers: divide-gray-100, divide-gray-200

/* OMBRES */
Subtle: shadow-sm
Elevated: shadow-lg
```

---

## 🚀 **APPLICATION DANS LE PROJET**

Ce guide doit être appliqué uniformément à :
- ✅ Toutes les pages d'administration
- ✅ Toutes les interfaces RS et MS
- ✅ Tous les composants et modales
- ✅ Toutes les pages d'authentification
- ✅ Tous les tableaux et listes

**Référence de qualité :** `/admin/sous-structures` - Cette page représente le standard de design professionnel à reproduire partout.
