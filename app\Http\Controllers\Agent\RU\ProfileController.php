<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        return Inertia::render('Agent/RU/Profile/Edit', [
            'pageTitle' => 'Mon Profil',
            'mustVerifyEmail' => $request->user() instanceof MustVerifyEmail,
            'status' => session('status'),
            'agent' => $user->agent->load('user'),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $user->fill($request->validated());

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();

        return Redirect::route('agent.ru.profile.edit')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
