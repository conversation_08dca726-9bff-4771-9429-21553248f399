<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormatEvaluationUniversite extends Model
{
    use HasFactory;

    protected $table = 'formats_evaluation_universites';

    protected $fillable = [
        'universite_id',
        'nombre_criteres',
        'points_par_critere',
        'criteres', // JSON des intitulés des critères
        'cree_par_agent_id',
        'actif',
    ];

    protected $casts = [
        'criteres' => 'array',
        'actif' => 'boolean',
    ];

    /**
     * Relation avec l'université
     */
    public function universite()
    {
        return $this->belongsTo(Universite::class);
    }

    /**
     * Relation avec l'agent qui a créé ce format
     */
    public function creeParAgent()
    {
        return $this->belongsTo(Agent::class, 'cree_par_agent_id');
    }

    /**
     * Obtenir le format actif pour une université donnée
     */
    public static function getFormatActifPourUniversite($universiteId)
    {
        return self::where('universite_id', $universiteId)
            ->where('actif', true)
            ->latest()
            ->first();
    }

    /**
     * Obtenir le format actif pour une université par nom avec cache
     */
    public static function getFormatActifPourUniversiteParNom($nomUniversite)
    {
        $cacheKey = 'format_evaluation_' . md5($nomUniversite);

        return cache()->remember($cacheKey, 3600, function () use ($nomUniversite) {
            return self::with(['universite', 'creeParAgent.user'])
                ->whereHas('universite', function ($query) use ($nomUniversite) {
                    $query->where('nom_complet', $nomUniversite);
                })
                ->where('actif', true)
                ->latest()
                ->first();
        });
    }

    /**
     * Invalider le cache pour une université
     */
    public static function invaliderCacheUniversite($nomUniversite)
    {
        $cacheKey = 'format_evaluation_' . md5($nomUniversite);
        cache()->forget($cacheKey);
    }

    /**
     * Désactiver tous les anciens formats pour cette université
     */
    public function desactiverAnciens()
    {
        self::where('universite_id', $this->universite_id)
            ->where('id', '!=', $this->id)
            ->update(['actif' => false]);
    }

    /**
     * Obtenir le total de points possible
     */
    public function getTotalPointsAttribute()
    {
        return $this->nombre_criteres * $this->points_par_critere;
    }

    /**
     * Valider que le format est cohérent
     */
    public function estValide()
    {
        // Vérifier que le nombre de critères correspond aux intitulés
        if (!is_array($this->criteres) || count($this->criteres) !== $this->nombre_criteres) {
            return false;
        }

        // Vérifier que tous les critères ont un intitulé non vide
        foreach ($this->criteres as $critere) {
            if (empty(trim($critere))) {
                return false;
            }
        }

        return true;
    }

    /**
     * Scope pour les formats actifs
     */
    public function scopeActifs($query)
    {
        return $query->where('actif', true);
    }

    /**
     * Scope pour une université spécifique
     */
    public function scopePourUniversite($query, $universiteId)
    {
        return $query->where('universite_id', $universiteId);
    }

    /**
     * Scope pour une université par nom
     */
    public function scopePourUniversiteParNom($query, $nomUniversite)
    {
        return $query->whereHas('universite', function ($q) use ($nomUniversite) {
            $q->where('nom_complet', $nomUniversite);
        });
    }

    /**
     * Obtenir les statistiques d'utilisation des formats
     */
    public static function getStatistiques()
    {
        return [
            'total_formats' => self::count(),
            'formats_actifs' => self::actifs()->count(),
            'universites_avec_format' => self::actifs()->distinct('universite_id')->count(),
            'repartition_criteres' => self::actifs()
                ->selectRaw('nombre_criteres, COUNT(*) as count')
                ->groupBy('nombre_criteres')
                ->pluck('count', 'nombre_criteres')
                ->toArray()
        ];
    }
}
