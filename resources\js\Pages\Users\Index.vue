<!-- resources/js/Pages/Users/<USER>
<template>
  <Head title="Gestion des utilisateurs" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 space-y-6">
        <!-- Header professionnel -->
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
          <div class="flex items-center gap-4">
            <div class="p-3 bg-blue-600 rounded-xl shadow-lg">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </div>
            <div>
              <h1 class="text-3xl font-bold text-slate-800">Gestion des utilisateurs</h1>
              <p class="text-slate-600 mt-1">Administration des comptes et permissions</p>
            </div>
            <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-2 rounded-full">
              {{ filteredUsers.length }} utilisateur{{ filteredUsers.length > 1 ? 's' : '' }}
            </span>
          </div>
          <button @click="openUserTypeModal()" class="inline-flex items-center gap-3 px-6 py-3 bg-blue-600 text-white rounded-xl shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-semibold transition-all duration-200 transform hover:scale-105">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Ajouter un utilisateur
          </button>
        </div>

        <!-- Système de filtrage professionnel -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6 mb-6">
          <div class="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
            <div class="flex items-center gap-2 text-slate-700 font-semibold">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"/>
              </svg>
              Filtres :
            </div>
            <div class="flex flex-wrap gap-4 flex-1">
              <!-- Filtre par rôle -->
              <div class="min-w-[200px]">
                <label class="block text-sm font-medium text-slate-700 mb-1">Rôle</label>
                <select v-model="filters.role" @change="applyFilters" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-slate-700">
                  <option value="all">Tous les rôles</option>
                  <option value="admin">Administrateurs</option>
                  <option value="agent_rs">Agents RS</option>
                  <option value="agent_ms">Agents MS</option>
                  <option value="agent_ru">Responsables RU</option>
                  <option value="stagiaire">Stagiaires</option>
                </select>
              </div>

              <!-- Filtre par structure -->
              <div class="min-w-[250px]">
                <label class="block text-sm font-medium text-slate-700 mb-1">Structure</label>
                <select v-model="filters.structure" @change="applyFilters" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-slate-700">
                  <option value="all">Toutes les structures</option>
                  <option v-for="structure in structures" :key="structure.id" :value="structure.id">
                    {{ structure.sigle }} - {{ structure.libelle }}
                  </option>
                </select>
              </div>

              <!-- Filtre par créateur -->
              <div class="min-w-[200px]">
                <label class="block text-sm font-medium text-slate-700 mb-1">Créé par</label>
                <select v-model="filters.created_by" @change="applyFilters" class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-slate-700">
                  <option value="all">Tous les créateurs</option>
                  <option value="current_admin">Mes créations</option>
                  <option v-for="admin in admins" :key="admin.id" :value="admin.id">
                    {{ admin.nom }} {{ admin.prenom }}
                  </option>
                </select>
              </div>

              <!-- Bouton de réinitialisation -->
              <div class="flex items-end">
                <button @click="resetFilters" class="px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors duration-200 flex items-center gap-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  Réinitialiser
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- Tableau professionnel avec scroll horizontal -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                </svg>
                <h2 class="text-xl font-bold text-slate-800">Liste des utilisateurs</h2>
              </div>
              <div class="text-sm text-slate-600">
                {{ filteredUsers.length }} résultat{{ filteredUsers.length > 1 ? 's' : '' }}
              </div>
            </div>
          </div>

          <div v-if="filteredUsers.length === 0" class="p-16 text-center">
            <div class="max-w-md mx-auto">
              <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-6 text-slate-300">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
              <h3 class="text-xl font-semibold text-slate-700 mb-2">Aucun utilisateur trouvé</h3>
              <p class="text-slate-500 mb-6">Aucun utilisateur ne correspond aux critères de filtrage sélectionnés.</p>
              <button @click="resetFilters" class="px-6 py-3 bg-blue-600 text-white rounded-xl shadow hover:bg-blue-700 transition font-semibold">
                Réinitialiser les filtres
              </button>
            </div>
          </div>

          <div v-else class="overflow-x-auto">
            <div class="min-w-full">
              <table class="w-full">
                <thead class="bg-slate-100 border-b border-slate-200">
                  <tr>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 uppercase tracking-wider min-w-[200px]">
                      Utilisateur
                    </th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 uppercase tracking-wider min-w-[250px]">
                      Email
                    </th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 uppercase tracking-wider min-w-[150px]">
                      Rôle
                    </th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 uppercase tracking-wider min-w-[200px]">
                      Affecté à
                    </th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 uppercase tracking-wider min-w-[150px]">
                      Créé par
                    </th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 uppercase tracking-wider min-w-[120px]">
                      Date création
                    </th>
                    <th class="px-6 py-4 text-center text-sm font-bold text-slate-700 uppercase tracking-wider min-w-[150px]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-100">
                  <tr v-for="user in filteredUsers" :key="user.id" class="hover:bg-blue-50 transition-colors duration-200">
                    <!-- Utilisateur -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          {{ (user.prenom?.[0] || '') + (user.nom?.[0] || '') }}
                        </div>
                        <div>
                          <div class="text-sm font-semibold text-slate-900">
                            {{ user.prenom }} {{ user.nom }}
                          </div>
                        </div>
                      </div>
                    </td>

                    <!-- Email -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-slate-700">{{ user.email }}</div>
                    </td>

                    <!-- Rôle -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getRoleBadgeClass(user)">
                        {{ getRoleDisplayName(user) }}
                      </span>
                    </td>

                    <!-- Affecté à -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div v-if="getAffectationInfo(user)" class="text-sm font-medium text-slate-900">
                        {{ getAffectationInfo(user) }}
                      </div>
                      <span v-else class="text-slate-400 text-sm">—</span>
                    </td>

                    <!-- Créé par -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div v-if="user.creator_info" class="text-sm text-slate-700">
                        {{ user.creator_info.prenom }} {{ user.creator_info.nom }}
                      </div>
                      <span v-else class="text-slate-400 text-sm italic">Système</span>
                    </td>

                    <!-- Date création -->
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-slate-700">
                        {{ formatDate(user.created_at) }}
                      </div>
                    </td>

                    <!-- Actions -->
                    <td class="px-6 py-4 whitespace-nowrap text-center">
                      <div class="flex justify-center gap-2">
                        <template v-if="user.can_modify">
                          <button @click="openEditModal(user)" class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-colors duration-200" title="Modifier">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
                            </svg>
                          </button>
                          <button @click="openDeleteModal(user)" class="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg transition-colors duration-200" title="Supprimer">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                              <path d="M3 6h18"/>
                              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                            </svg>
                          </button>
                        </template>
                        <template v-else-if="user.is_agent_rs_ms">
                          <span class="text-slate-500 text-xs bg-slate-100 px-3 py-1 rounded-full">Géré via Agents</span>
                        </template>
                        <template v-else>
                          <span class="text-slate-400 text-xs">Aucune action</span>
                        </template>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!-- Modale de sélection du type d'utilisateur -->
        <div v-if="showUserTypeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
            <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
              <div class="flex justify-between items-center">
                <h3 class="text-xl font-bold text-slate-800">Choisir le type d'utilisateur</h3>
                <button @click="closeUserTypeModal" class="text-slate-500 hover:text-slate-700 transition-colors">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div class="p-6">
              <p class="text-slate-600 mb-6">Sélectionnez le type d'utilisateur que vous souhaitez créer :</p>

              <div class="space-y-4">
                <button @click="selectUserType('admin')" class="w-full p-4 border-2 border-slate-200 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 text-left group">
                  <div class="flex items-center gap-4">
                    <div class="p-3 bg-blue-100 rounded-xl group-hover:bg-blue-200 transition-colors">
                      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-slate-800">Administrateur</h4>
                      <p class="text-sm text-slate-600">Accès complet à l'administration du système</p>
                    </div>
                  </div>
                </button>

                <button @click="selectUserType('agent_rs')" class="w-full p-4 border-2 border-slate-200 rounded-xl hover:border-green-500 hover:bg-green-50 transition-all duration-200 text-left group">
                  <div class="flex items-center gap-4">
                    <div class="p-3 bg-green-100 rounded-xl group-hover:bg-green-200 transition-colors">
                      <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-slate-800">Agent RS (Responsable de Structure)</h4>
                      <p class="text-sm text-slate-600">Gestion d'une structure et de ses agents MS</p>
                    </div>
                  </div>
                </button>

                <button @click="selectUserType('agent_ru')" class="w-full p-4 border-2 border-slate-200 rounded-xl hover:border-emerald-500 hover:bg-emerald-50 transition-all duration-200 text-left group">
                  <div class="flex items-center gap-4">
                    <div class="p-3 bg-emerald-100 rounded-xl group-hover:bg-emerald-200 transition-colors">
                      <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-semibold text-slate-800">Agent RU (Responsable Université)</h4>
                      <p class="text-sm text-slate-600">Gestion d'une université et de ses étudiants</p>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modale pour l'ajout/modification d'utilisateur -->
        <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <!-- En-tête de la modale -->
            <div class="px-6 py-4 border-b border-slate-200 bg-slate-50 sticky top-0">
              <div class="flex justify-between items-center">
                <h3 class="text-xl font-bold text-slate-800">
                  {{ editingId ? 'Modifier un utilisateur' : getModalTitle() }}
                </h3>
                <button @click="closeModal" class="text-slate-500 hover:text-slate-700 transition-colors">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Formulaire -->
            <form @submit.prevent="submitForm" class="px-6 py-6">
              <div class="space-y-6">
                <!-- Informations de base -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Prénom *</label>
                    <input
                      v-model="form.prenom"
                      type="text"
                      placeholder="Prénom de l'utilisateur"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      required
                    />
                    <div v-if="form.errors.prenom" class="mt-1 text-sm text-red-600">{{ form.errors.prenom }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Nom *</label>
                    <input
                      v-model="form.nom"
                      type="text"
                      placeholder="Nom de l'utilisateur"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      required
                    />
                    <div v-if="form.errors.nom" class="mt-1 text-sm text-red-600">{{ form.errors.nom }}</div>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">Adresse email *</label>
                  <input
                    v-model="form.email"
                    type="email"
                    placeholder="<EMAIL>"
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    required
                  />
                  <div v-if="form.errors.email" class="mt-1 text-sm text-red-600">{{ form.errors.email }}</div>
                </div>

                <!-- Champs additionnels pour Agent RU -->
                <div v-if="selectedUserType === 'agent_ru' && !editingId" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Téléphone <span class="text-red-500">*</span></label>
                    <input
                      v-model="form.telephone"
                      type="tel"
                      placeholder="+229 XX XX XX XX"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    />
                    <div v-if="form.errors.telephone" class="mt-1 text-sm text-red-600">{{ form.errors.telephone }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Date de naissance <span class="text-red-500">*</span></label>
                    <input
                      v-model="form.date_de_naissance"
                      type="date"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    />
                    <div v-if="form.errors.date_de_naissance" class="mt-1 text-sm text-red-600">{{ form.errors.date_de_naissance }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Sexe <span class="text-red-500">*</span></label>
                    <select
                      v-model="form.sexe"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    >
                      <option value="">Sélectionner</option>
                      <option value="Homme">Homme</option>
                      <option value="Femme">Femme</option>
                    </select>
                    <div v-if="form.errors.sexe" class="mt-1 text-sm text-red-600">{{ form.errors.sexe }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Matricule <span class="text-red-500">*</span></label>
                    <input
                      v-model="form.matricule"
                      type="text"
                      placeholder="RU123456"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    />
                    <div v-if="form.errors.matricule" class="mt-1 text-sm text-red-600">{{ form.errors.matricule }}</div>
                    <p class="text-sm text-slate-500 mt-1">Format recommandé : RU + 6 caractères</p>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Fonction <span class="text-red-500">*</span></label>
                    <input
                      v-model="form.fonction"
                      type="text"
                      placeholder="Responsable Université"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    />
                    <div v-if="form.errors.fonction" class="mt-1 text-sm text-red-600">{{ form.errors.fonction }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Date d'embauche</label>
                    <input
                      v-model="form.date_embauche"
                      type="date"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    />
                    <div v-if="form.errors.date_embauche" class="mt-1 text-sm text-red-600">{{ form.errors.date_embauche }}</div>
                  </div>
                </div>

                <!-- Type d'utilisateur (pour création seulement) -->
                <div v-if="!editingId">
                  <label class="block text-sm font-semibold text-slate-700 mb-2">Type d'utilisateur</label>
                  <div class="p-4 bg-slate-50 rounded-lg border border-slate-200">
                    <div class="flex items-center gap-3">
                      <div :class="[
                        'p-2 rounded-lg',
                        selectedUserType === 'admin' ? 'bg-blue-100' : selectedUserType === 'agent_rs' ? 'bg-green-100' : 'bg-emerald-100'
                      ]">
                        <svg v-if="selectedUserType === 'admin'" class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                        <svg v-else-if="selectedUserType === 'agent_rs'" class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                        </svg>
                        <svg v-else class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="font-semibold text-slate-800">
                          {{ getUserTypeDisplayName() }}
                        </div>
                        <div class="text-sm text-slate-600">
                          {{ getUserTypeDescription() }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Actions pour modification -->
                <div v-if="editingId && editingUser?.role === 'admin'">
                  <label class="block text-sm font-semibold text-slate-700 mb-2">Action à effectuer</label>
                  <div class="space-y-3">
                    <label class="flex items-center gap-3 p-3 border border-slate-200 rounded-lg hover:bg-slate-50 cursor-pointer">
                      <input v-model="form.action_type" type="radio" value="update_info" class="text-blue-600 focus:ring-blue-500">
                      <div>
                        <div class="font-medium text-slate-800">Modifier les informations</div>
                        <div class="text-sm text-slate-600">Mettre à jour le nom, prénom et email</div>
                      </div>
                    </label>
                    <label class="flex items-center gap-3 p-3 border border-slate-200 rounded-lg hover:bg-slate-50 cursor-pointer">
                      <input v-model="form.action_type" type="radio" value="convert_to_agent_rs" class="text-blue-600 focus:ring-blue-500">
                      <div>
                        <div class="font-medium text-slate-800">Convertir en Agent RS</div>
                        <div class="text-sm text-slate-600">Transformer cet administrateur en responsable de structure</div>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- Mots de passe -->
                <div v-if="!editingId || (editingId && form.action_type === 'update_info')" class="space-y-4">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">
                      {{ editingId ? 'Nouveau mot de passe (optionnel)' : 'Mot de passe *' }}
                    </label>
                    <input
                      v-model="form.password"
                      type="password"
                      placeholder="Minimum 8 caractères"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      :required="!editingId"
                    />
                    <div v-if="form.errors.password" class="mt-1 text-sm text-red-600">{{ form.errors.password }}</div>
                  </div>

                  <div v-if="form.password">
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Confirmer le mot de passe *</label>
                    <input
                      v-model="form.password_confirmation"
                      type="password"
                      placeholder="Confirmer le mot de passe"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      :required="!!form.password"
                    />
                  </div>

                  <!-- Champ structure pour Agent RS -->
                  <div v-if="selectedUserType === 'agent_rs' && !editingId">
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Structure à superviser</label>
                    <select
                      v-model="form.structure_responsable_id"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                    >
                      <option value="">Aucune structure assignée</option>
                      <option v-for="structure in structuresDisponibles" :key="structure.id" :value="structure.id">
                        {{ structure.sigle }} - {{ structure.libelle }}
                      </option>
                    </select>
                    <p class="text-sm text-slate-500 mt-2 bg-green-50 p-3 rounded-md border-l-4 border-green-500">
                      <strong>Note :</strong> Seules les structures sans responsable assigné sont disponibles.
                    </p>
                    <div v-if="form.errors.structure_responsable_id" class="mt-1 text-sm text-red-600">{{ form.errors.structure_responsable_id }}</div>
                  </div>

                  <!-- Champ université pour Agent RU -->
                  <div v-if="selectedUserType === 'agent_ru' && !editingId">
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Université à superviser</label>
                    <select
                      v-model="form.universite_responsable_id"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    >
                      <option value="">Aucune université assignée</option>
                      <option v-for="universite in universites" :key="universite.id" :value="universite.id">
                        {{ universite.nom_complet }} ({{ universite.sigle }})
                      </option>
                    </select>
                    <p class="text-sm text-slate-500 mt-2 bg-emerald-50 p-3 rounded-md border-l-4 border-emerald-500">
                      <strong>Note :</strong> L'agent RU sera automatiquement assigné comme responsable de l'université sélectionnée.
                    </p>
                    <div v-if="form.errors.universite_responsable_id" class="mt-1 text-sm text-red-600">{{ form.errors.universite_responsable_id }}</div>
                  </div>
                </div>
              </div>

              <!-- Boutons de navigation -->
              <div class="flex justify-between pt-6 border-t border-slate-200 mt-6 bg-slate-50 -mx-6 px-6 py-4">
                <button
                  type="button"
                  @click="closeModal()"
                  class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 flex items-center gap-2 font-semibold"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6 6 18M6 6l12 12"/>
                  </svg>
                  Annuler
                </button>

                <button
                  type="submit"
                  class="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center gap-2 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                  :disabled="form.processing"
                >
                  <span v-if="form.processing">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Traitement...
                  </span>
                  <span v-else>
                    {{ getSubmitButtonText() }}
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M5 12h14"/>
                      <path d="m12 5 7 7-7 7"/>
                    </svg>
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
        
        <!-- Modal de confirmation de suppression -->
        <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
            <div class="px-6 py-4 bg-red-50 border-b border-red-100">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-red-100 rounded-full p-2 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-red-800">Supprimer l'utilisateur</h3>
              </div>
            </div>
            
            <div class="px-6 py-4">
              <p class="text-gray-700 mb-4">
                Voulez-vous vraiment supprimer l'utilisateur "{{ userToDelete?.nom || '' }}" ?<br>
                <span class="text-sm text-gray-500">Email : {{ userToDelete?.email || '' }}</span><br>
                <span class="text-sm text-gray-500">Rôle : {{ userToDelete?.role || '' }}</span><br>
                Cette action est irréversible.
              </p>
              
              <div class="flex justify-end space-x-3">
                <button 
                  @click="closeDeleteModal" 
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Annuler
                </button>
                <button 
                  @click="confirmDelete" 
                  class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Ajout du composant AdminToast -->
        <AdminToast ref="toast" />
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { Head, useForm, router, usePage } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';
import { useSuccessMessage } from '@/Composables/useSuccessMessage';

const props = defineProps({
  users: Array,
  structures: Array,
  structuresDisponibles: Array,
  universites: Array,
  admins: Array,
  filters: Object,
});

const page = usePage();
const toast = ref(null);
const showModal = ref(false);
const showUserTypeModal = ref(false);
const editingId = ref(null);
const editingUser = ref(null);
const selectedUserType = ref('admin');
const { showUserCreated, showUserUpdated, showUserDeleted } = useSuccessMessage();

// État des filtres
const filters = ref({
  role: props.filters?.role || 'all',
  structure: props.filters?.structure || 'all',
  created_by: props.filters?.created_by || 'all',
});

// Variables pour la confirmation de suppression
const showDeleteModal = ref(false);
const userToDelete = ref(null);

// Computed pour les utilisateurs filtrés
const filteredUsers = computed(() => {
  let filtered = props.users || [];

  // Filtrage par rôle
  if (filters.value.role !== 'all') {
    if (filters.value.role === 'agent_rs') {
      filtered = filtered.filter(user => user.agent_role === 'RS');
    } else if (filters.value.role === 'agent_ms') {
      filtered = filtered.filter(user => user.agent_role === 'MS');
    } else {
      filtered = filtered.filter(user => user.role === filters.value.role);
    }
  }

  // Filtrage par structure
  if (filters.value.structure !== 'all') {
    filtered = filtered.filter(user => {
      if (user.structure_info && user.structure_info.id == filters.value.structure) {
        return true;
      }
      if (user.structures_responsable && user.structures_responsable.length > 0) {
        return user.structures_responsable.some(s => s.id == filters.value.structure);
      }
      return false;
    });
  }

  // Filtrage par créateur
  if (filters.value.created_by !== 'all') {
    if (filters.value.created_by === 'current_admin') {
      // Filtrer par l'admin actuel (à implémenter selon votre logique)
      filtered = filtered.filter(user => user.created_by === page.props.auth.user.id);
    } else {
      filtered = filtered.filter(user => user.created_by == filters.value.created_by);
    }
  }

  return filtered;
});

// Surveiller les messages flash et les afficher automatiquement
onMounted(() => {
  // Vérifier si des messages flash existent au chargement
  setTimeout(() => {
    const { flash } = page.props;
    if (flash) {
      if (flash.success && toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Succès',
          message: flash.success
        });
      }
      
      if (flash.error && toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur',
          message: flash.error
        });
      }
    }
  }, 100);
});

// Formulaire pour les données utilisateur
const form = useForm({
  nom: '',
  prenom: '',
  email: '',
  role: '',
  user_type: '',
  action_type: 'update_info',
  password: '',
  password_confirmation: '',
  structure_responsable_id: null,
  universite_responsable_id: null,
  // Champs additionnels pour Agent RU
  telephone: '',
  date_de_naissance: '',
  sexe: '',
  matricule: '',
  fonction: '',
  date_embauche: '',
});

// Méthodes utilitaires
const getRoleBadgeClass = (user) => {
  const baseClasses = 'px-3 py-1 rounded-full text-xs font-semibold';

  if (user.role === 'admin') {
    return `${baseClasses} bg-blue-100 text-blue-800`;
  } else if (user.agent_role === 'RS') {
    return `${baseClasses} bg-green-100 text-green-800`;
  } else if (user.agent_role === 'MS') {
    return `${baseClasses} bg-purple-100 text-purple-800`;
  } else if (user.agent_role === 'RU') {
    return `${baseClasses} bg-emerald-100 text-emerald-800`;
  } else if (user.role === 'stagiaire') {
    return `${baseClasses} bg-yellow-100 text-yellow-800`;
  } else {
    return `${baseClasses} bg-slate-100 text-slate-800`;
  }
};

const getRoleDisplayName = (user) => {
  if (user.role === 'admin') {
    return 'Administrateur';
  } else if (user.agent_role === 'RS') {
    return 'Agent RS';
  } else if (user.agent_role === 'MS') {
    return 'Agent MS';
  } else if (user.agent_role === 'RU') {
    return 'Responsable RU';
  } else if (user.role === 'stagiaire') {
    return 'Stagiaire';
  } else {
    return user.role;
  }
};

const getAffectationInfo = (user) => {
  // Pour les Responsables RU : afficher l'université assignée
  if (user.agent_role === 'RU' && user.agent?.universites_gerees?.length > 0) {
    return user.agent.universites_gerees[0].nom_complet || user.agent.universites_gerees[0].nom_universite;
  }

  // Pour les Agents RS/MS : afficher la structure assignée
  if ((user.agent_role === 'RS' || user.agent_role === 'MS') && user.agent?.structure) {
    return user.agent.structure.sigle || user.agent.structure.libelle;
  }

  // Pour les Agents RS qui sont responsables de structures
  if (user.agent_role === 'RS' && user.agent?.structures_responsable?.length > 0) {
    return user.agent.structures_responsable[0].sigle || user.agent.structures_responsable[0].libelle;
  }

  // Pour les Stagiaires et Administrateurs : retourner null (affichera "—")
  return null;
};

const formatDate = (dateString) => {
  if (!dateString) return '—';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

const getUserTypeDisplayName = () => {
  switch (selectedUserType.value) {
    case 'admin':
      return 'Administrateur';
    case 'agent_rs':
      return 'Agent RS (Responsable de Structure)';
    case 'agent_ru':
      return 'Agent RU (Responsable Université)';
    default:
      return 'Utilisateur';
  }
};

const getUserTypeDescription = () => {
  switch (selectedUserType.value) {
    case 'admin':
      return 'Accès complet à l\'administration';
    case 'agent_rs':
      return 'Gestion d\'une structure et de ses agents MS';
    case 'agent_ru':
      return 'Gestion d\'une université et de ses étudiants';
    default:
      return '';
  }
};

const getModalTitle = () => {
  switch (selectedUserType.value) {
    case 'admin':
      return 'Ajouter un administrateur';
    case 'agent_rs':
      return 'Ajouter un agent RS';
    case 'agent_ru':
      return 'Ajouter un agent RU';
    default:
      return 'Ajouter un utilisateur';
  }
};

const getSubmitButtonText = () => {
  if (editingId.value) {
    if (form.action_type === 'convert_to_agent_rs') {
      return 'Convertir en Agent RS';
    } else {
      return 'Mettre à jour';
    }
  } else {
    switch (selectedUserType.value) {
      case 'admin':
        return 'Créer l\'administrateur';
      case 'agent_rs':
        return 'Créer l\'agent RS';
      case 'agent_ru':
        return 'Créer l\'agent RU';
      default:
        return 'Créer l\'utilisateur';
    }
  }
};

// Méthodes de filtrage
const applyFilters = () => {
  router.get(route('admin.users.index'), filters.value, {
    preserveState: true,
    preserveScroll: true,
  });
};

const resetFilters = () => {
  filters.value = {
    role: 'all',
    structure: 'all',
    created_by: 'all',
  };
  applyFilters();
};

// Méthodes de gestion des modales
const openUserTypeModal = () => {
  showUserTypeModal.value = true;
};

const closeUserTypeModal = () => {
  showUserTypeModal.value = false;
};

const selectUserType = (type) => {
  selectedUserType.value = type;
  closeUserTypeModal();

  // Ouvrir le modal unifié pour tous les types d'utilisateurs
  openModal();
};

function openModal(user = null) {
  if (user) {
    form.nom = user.nom || '';
    form.prenom = user.prenom || '';
    form.email = user.email || '';
    form.role = user.role || '';
    form.action_type = 'update_info';
    form.password = '';
    form.password_confirmation = '';
    editingId.value = user.id;
    editingUser.value = user;
  } else {
    form.reset();
    form.user_type = selectedUserType.value;
    form.role = selectedUserType.value === 'admin' ? 'admin' : 'agent';
    editingId.value = null;
    editingUser.value = null;
  }
  showModal.value = true;
}

const openEditModal = (user) => {
  openModal(user);
};

function closeModal() {
  showModal.value = false;
  form.reset();
  editingId.value = null;
  editingUser.value = null;
}

// Fonctions pour le modal de confirmation de suppression
function openDeleteModal(user) {
  userToDelete.value = user;
  showDeleteModal.value = true;
}

function closeDeleteModal() {
  showDeleteModal.value = false;
  userToDelete.value = null;
}

function confirmDelete() {
  if (!userToDelete.value) return;
  
  deleteUser(userToDelete.value.id);
  closeDeleteModal();
}

function submitForm() {
  if (editingId.value) {
    // Mise à jour d'un utilisateur existant
    form.put(route('admin.users.update', editingId.value), {
      onSuccess: () => {
        closeModal();
        if (toast.value) {
          const message = form.action_type === 'convert_to_agent_rs'
            ? `L'administrateur "${form.prenom} ${form.nom}" a été converti en Agent RS avec succès.`
            : `Les informations de "${form.prenom} ${form.nom}" ont été mises à jour avec succès.`;

          toast.value.addToast({
            type: 'success',
            title: form.action_type === 'convert_to_agent_rs' ? 'Conversion réussie' : 'Utilisateur modifié',
            message: message
          });
        }
      },
      onError: () => {
        if (toast.value) {
          toast.value.addToast({
            type: 'error',
            title: 'Erreur de validation',
            message: 'Veuillez vérifier les informations saisies'
          });
        }
      }
    });
  } else {
    // Création d'un nouvel utilisateur
    form.post(route('admin.users.store'), {
      onSuccess: () => {
        closeModal();
        if (toast.value) {
          const userType = getUserTypeDisplayName().toLowerCase();
          const structureName = selectedUserType.value === 'admin' ? 'système' : (form.structure_id ? 'structure sélectionnée' : 'structure assignée');
          toast.value.addToast({
            type: 'success',
            title: `${userType.charAt(0).toUpperCase() + userType.slice(1)} créé avec succès`,
            message: `L'${userType} "${form.prenom} ${form.nom}" a été ajouté avec succès.<br>• Email : ${form.email}<br>• Accès : ${structureName}<br>• Un email de bienvenue sera envoyé automatiquement<br>• L'utilisateur peut maintenant se connecter à la plateforme`,
            duration: 8000
          });
        }
      },
      onError: (errors) => {
        if (toast.value) {
          const errorMessages = Object.values(errors).flat();
          toast.value.addToast({
            type: 'error',
            title: 'Erreur lors de la création',
            message: `Veuillez corriger les erreurs suivantes :<br>• ${errorMessages.join('<br>• ')}<br><br>Vérifiez que tous les champs obligatoires sont remplis correctement.`,
            duration: 8000
          });
        }
      }
    });
  }
}

function deleteUser(id) {
  // Trouver l'utilisateur pour afficher son nom dans le message de confirmation
  const user = props.users.find(u => u.id === id);
  
  router.delete(route('admin.users.destroy', id), {
    onSuccess: () => {
      if (toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Utilisateur supprimé',
          message: `L'utilisateur "${user?.nom || ''}" (${user?.email || ''}) avec le rôle "${user?.role || ''}" a été supprimé avec succès.`
        });
      }
    },
    onError: () => {
      if (toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur de suppression',
          message: 'Impossible de supprimer cet utilisateur'
        });
      }
    }
  });
}
</script>