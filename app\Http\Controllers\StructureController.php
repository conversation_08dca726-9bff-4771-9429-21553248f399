<?php

namespace App\Http\Controllers;

use App\Models\Structure;
use Illuminate\Http\Request;
use Inertia\Inertia;

class StructureController extends Controller
{
    /**
     * Affiche la liste des structures.
     */
    public function index()
    {
        // RESTRICTION ADMIN : L'admin ne voit que les structures DG (niveau 0, sans parent)
        // Les sous-structures sont gérées par les agents RS
        $structures = Structure::with(['responsable.user'])
            ->whereNull('parent_id') // Seulement les structures de Direction Générale
            ->get();

        return Inertia::render('Structures/Index', [
            'structures' => $structures,
        ]);
    }

    /**
     * Affiche le formulaire de création d'une structure.
     */
    public function create()
    {
        return Inertia::render('Admin/Structures/Create');
    }

    /**
     * Enregistre une nouvelle structure.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'sigle' => 'required|string|max:255|unique:structures',
            'libelle' => 'required|string|max:255|unique:structures',
            'description' => 'nullable|string',
        ]);

        Structure::create($validated);

        return redirect()->route('admin.structures.index')->with('success', 'Structure créée avec succès.');
    }

    /**
     * Affiche le formulaire d'édition pour une structure.
     */
    public function edit(Structure $structure)
    {
        return Inertia::render('Admin/Structures/Edit', [
            'structure' => $structure,
        ]);
    }

    /**
     * Met à jour une structure existante.
     */
    public function update(Request $request, Structure $structure)
    {
        // RESTRICTION ADMIN : L'admin ne peut modifier que les structures DG (niveau 0, sans parent)
        if ($structure->parent_id !== null) {
            return redirect()->route('admin.structures.index')
                ->with('error', 'Vous ne pouvez modifier que les structures de Direction Générale. Les sous-structures sont gérées par les agents RS.');
        }

        $validated = $request->validate([
            'sigle' => 'required|string|max:255|unique:structures,sigle,' . $structure->id,
            'libelle' => 'required|string|max:255|unique:structures,libelle,' . $structure->id,
            'description' => 'nullable|string',
        ]);

        $structure->update($validated);

        return redirect()->route('admin.structures.index')->with('success', 'Structure mise à jour avec succès.');
    }

    /**
     * Supprime une structure.
     */
    public function destroy(Structure $structure)
    {
        // RESTRICTION ADMIN : L'admin ne peut supprimer que les structures DG (niveau 0, sans parent)
        if ($structure->parent_id !== null) {
            return redirect()->route('admin.structures.index')
                ->with('error', 'Vous ne pouvez supprimer que les structures de Direction Générale. Les sous-structures sont gérées par les agents RS.');
        }

        // Vérifier s'il y a des sous-structures
        if ($structure->children()->count() > 0) {
            return redirect()->route('admin.structures.index')
                ->with('error', 'Impossible de supprimer cette structure car elle contient des sous-structures.');
        }

        $structure->delete();

        return redirect()->route('admin.structures.index')->with('success', 'Structure supprimée avec succès.');
    }

    /**
     * Affiche les détails d'une structure.
     */
    public function show(Structure $structure)
    {
        // Charger toutes les relations nécessaires pour la vue détaillée
        $structure->load([
            'responsable.user',
            'children.responsable.user',
            'parent.responsable.user'
        ]);

        // Charger les compteurs pour les statistiques
        $structure->loadCount([
            'children',
            'stagiaires',
            'stages',
            'demandesAttestation as demandes_count'
        ]);

        // Pour les enfants, charger aussi leurs compteurs
        $structure->children->each(function ($child) {
            $child->loadCount([
                'children',
                'stagiaires',
                'stages'
            ]);
        });

        return Inertia::render('Structures/Show', [
            'structure' => $structure,
        ]);
    }

    /**
     * Retourne les structures de direction générale sans responsable (utilisé pour sélection dynamique).
     * RESTRICTION : Seules les structures principales (parent_id = null) peuvent être assignées aux agents RS.
     */
    public function available()
    {
        $structures = Structure::where(function($query) {
            $query->whereNull('responsable_id');

            // Si un agent_id est fourni, inclure aussi sa structure actuelle
            if (request()->has('agent_id')) {
                $query->orWhere('responsable_id', request()->agent_id);
            }
        })
        // RESTRICTION CRITIQUE : Seules les structures de direction générale (sans parent)
        ->whereNull('parent_id')
        ->get(['id', 'libelle', 'responsable_id', 'sigle']);

        return response()->json($structures);
    }
}