<?php

namespace Database\Factories;

use App\Models\Agent;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AgentFactory extends Factory
{
    protected $model = Agent::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'matricule' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{4}'),
            'fonction' => $this->faker->jobTitle(),
            'role_agent' => $this->faker->randomElement(['RS', 'MS', 'DPAF']),
            'date_embauche' => $this->faker->dateTimeBetween('-5 years', 'now'),
            'structure_id' => null,
            'created_by' => User::factory(),
        ];
    }

    public function rs(): static
    {
        return $this->state(fn (array $attributes) => [
            'role_agent' => 'RS',
        ]);
    }

    public function ms(): static
    {
        return $this->state(fn (array $attributes) => [
            'role_agent' => 'MS',
        ]);
    }

    public function dpaf(): static
    {
        return $this->state(fn (array $attributes) => [
            'role_agent' => 'DPAF',
        ]);
    }
}
