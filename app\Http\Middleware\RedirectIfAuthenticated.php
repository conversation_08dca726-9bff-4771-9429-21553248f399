<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                // Vérifier l'intégrité de la session après fermeture du navigateur
                $sessionId = session()->getId();
                $userId = Auth::guard($guard)->id();

                // Vérifier si la session existe toujours dans la base de données
                $sessionExists = \DB::table('sessions')
                    ->where('id', $sessionId)
                    ->where('user_id', $userId)
                    ->exists();

                if (!$sessionExists) {
                    // Session invalide, forcer la déconnexion et demander une nouvelle authentification
                    Auth::guard($guard)->logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();

                    return $next($request);
                }

                // Si l'utilisateur est authentifié avec une session valide, le rediriger vers son dashboard
                return redirect()->route('dashboard');
            }
        }

        return $next($request);
    }
}
