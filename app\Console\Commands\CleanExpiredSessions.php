<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanExpiredSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sessions:clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean expired sessions from database for security';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $expiredTime = time() - 1800; // 30 minutes ago

        $deletedCount = DB::table('sessions')
            ->where('last_activity', '<', $expiredTime)
            ->delete();

        $this->info("Supprimé {$deletedCount} sessions expirées.");

        return 0;
    }
}
