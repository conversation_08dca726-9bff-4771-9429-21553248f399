<template>
  <Head :title="pageTitle" />
  
  <ResponsableUniversiteLayout>
    <div class="space-y-6">
      <!-- En-tête -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h1>
          <p class="text-gray-600 mt-1">{{ universite.nom_complet }}</p>
        </div>
      </div>

      <!-- Statistiques -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total stages</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.total }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">En cours</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.en_cours }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Terminés</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.termines }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Évalués</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.evalues }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filtres et recherche -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Recherche</label>
            <input 
              v-model="searchForm.search"
              @input="applyFilters"
              type="text"
              placeholder="Nom du stagiaire..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Statut</label>
            <select 
              v-model="searchForm.statut"
              @change="applyFilters"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Tous</option>
              <option value="En cours">En cours</option>
              <option value="Terminé">Terminé</option>
              <option value="Suspendu">Suspendu</option>
            </select>
          </div>



          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Période</label>
            <select 
              v-model="searchForm.periode"
              @change="applyFilters"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Toutes</option>
              <option value="en_cours">En cours</option>
              <option value="termines">Terminés</option>
              <option value="futurs">À venir</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tri</label>
            <select 
              v-model="searchForm.sort"
              @change="applyFilters"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="date_debut">Date de début</option>
              <option value="nom">Nom du stagiaire</option>
              <option value="note">Note d'évaluation</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Liste des stages -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Liste des stages</h3>
        </div>

        <div v-if="stages.data.length === 0" class="p-8 text-center">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <p class="text-gray-500">Aucun stage trouvé</p>
        </div>

        <div v-else class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stagiaire</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Structure</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Note</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="stage in stages.data" :key="stage.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <span class="text-blue-600 font-semibold text-sm">
                        {{ stage.stagiaire.user.prenom.charAt(0) }}{{ stage.stagiaire.user.nom.charAt(0) }}
                      </span>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ stage.stagiaire.user.prenom }} {{ stage.stagiaire.user.nom }}
                      </div>
                      <div class="text-sm text-gray-500">{{ stage.stagiaire.user.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ stage.structure?.libelle || '—' }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{{ formatDate(stage.date_debut) }}</div>
                  <div class="text-gray-500">{{ formatDate(stage.date_fin) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(stage.statut)" class="inline-flex px-2 py-1 text-xs font-medium rounded-full">
                    {{ stage.statut }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div v-if="stage.evaluation" class="text-sm">
                    <span class="font-medium text-gray-900">{{ stage.evaluation.note_totale }}/20</span>
                    <div v-if="stage.type === 'Académique' && !stage.evaluation.validee_par_ru" 
                         class="text-xs text-orange-600">En attente validation</div>
                  </div>
                  <span v-else class="text-sm text-gray-500">Non évalué</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <Link :href="route('agent.ru.stages.show', stage.id)" 
                        class="text-blue-600 hover:text-blue-900">
                    Voir
                  </Link>
                  <button v-if="stage.evaluation" 
                          @click="printNotes(stage.id)"
                          class="text-purple-600 hover:text-purple-900">
                    Imprimer
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="stages.links.length > 3" class="px-6 py-4 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700">
              Affichage de {{ stages.from }} à {{ stages.to }} sur {{ stages.total }} résultats
            </div>
            <div class="flex space-x-1">
              <Link v-for="link in stages.links" 
                    :key="link.label"
                    :href="link.url"
                    v-html="link.label"
                    :class="[
                      'px-3 py-2 text-sm rounded-lg',
                      link.active 
                        ? 'bg-blue-600 text-white' 
                        : link.url 
                          ? 'text-gray-700 hover:bg-gray-100' 
                          : 'text-gray-400 cursor-not-allowed'
                    ]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </ResponsableUniversiteLayout>
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3'
import { reactive, watch } from 'vue'
import ResponsableUniversiteLayout from '@/Layouts/ResponsableUniversiteLayout.vue'

const props = defineProps({
  pageTitle: String,
  universite: Object,
  stages: Object,
  stats: Object,
  filters: Object,
})

// Formulaire de recherche
const searchForm = reactive({
  search: props.filters.search || '',
  statut: props.filters.statut || '',
  periode: props.filters.periode || '',
  sort: props.filters.sort || 'date_debut',
  direction: props.filters.direction || 'desc',
})

// Fonctions
const applyFilters = () => {
  router.get(route('agent.ru.stages.index'), searchForm, {
    preserveState: true,
    replace: true,
  })
}

const formatDate = (date) => {
  if (!date) return '—'
  return new Date(date).toLocaleDateString('fr-FR')
}

const getStatusClass = (statut) => {
  const classes = {
    'En cours': 'bg-green-100 text-green-800',
    'Terminé': 'bg-blue-100 text-blue-800',
    'Suspendu': 'bg-red-100 text-red-800',
  }
  return classes[statut] || 'bg-gray-100 text-gray-800'
}

const printNotes = (stageId) => {
  window.open(route('agent.ru.stages.print-notes', stageId), '_blank')
}
</script>
