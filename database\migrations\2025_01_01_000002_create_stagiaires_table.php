<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migration consolidée pour la table stagiaires avec toutes les colonnes nécessaires
     */
    public function up(): void
    {
        Schema::create('stagiaires', function (Blueprint $table) {
            $table->id('id_stagiaire');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('niveau_etude');
            $table->string('filiere')->nullable();
            $table->string('universite')->nullable(); // Colonne legacy à supprimer plus tard
            $table->foreignId('universite_id')->nullable()->constrained('universites')->onDelete('set null');
            $table->timestamps();

            $table->unique('user_id'); // Un utilisateur ne peut être lié qu'à un seul profil stagiaire
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stagiaires');
    }
};
