<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('evaluations', function (Blueprint $table) {
            // Champs pour les évaluations avec format personnalisé (académiques)
            $table->json('criteres_personnalises')->nullable()->after('communication');
            $table->foreignId('format_evaluation_universite_id')->nullable()
                  ->constrained('formats_evaluation_universites')
                  ->onDelete('set null')
                  ->after('criteres_personnalises');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('evaluations', function (Blueprint $table) {
            $table->dropForeign(['format_evaluation_universite_id']);
            $table->dropColumn(['criteres_personnalises', 'format_evaluation_universite_id']);
        });
    }
};
