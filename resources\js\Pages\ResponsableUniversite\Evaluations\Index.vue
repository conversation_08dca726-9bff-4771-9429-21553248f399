<template>
  <ResponsableUniversiteLayout>
    <div class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- En-tête -->
        <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl shadow-xl p-8 mb-8">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-white mb-2">
                📋 Validation des Évaluations Académiques
              </h1>
              <p class="text-emerald-100">
                Université : {{ universite.nom_complet }}
              </p>
            </div>
            <div class="text-right">
              <div class="bg-white/20 rounded-lg p-4">
                <div class="text-2xl font-bold text-white">{{ evaluationsEnAttente.length }}</div>
                <div class="text-emerald-100 text-sm">En attente</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Onglets -->
        <div class="mb-8">
          <nav class="flex space-x-8">
            <button
              @click="activeTab = 'attente'"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === 'attente'
                  ? 'border-emerald-500 text-emerald-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              En attente de validation ({{ evaluationsEnAttente.length }})
            </button>
            <button
              @click="activeTab = 'validees'"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === 'validees'
                  ? 'border-emerald-500 text-emerald-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              Déjà validées ({{ evaluationsValidees.length }})
            </button>
          </nav>
        </div>

        <!-- Évaluations en attente -->
        <div v-if="activeTab === 'attente'">
          <div v-if="evaluationsEnAttente.length === 0" class="text-center py-12">
            <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune évaluation en attente</h3>
            <p class="text-gray-500">Toutes les évaluations académiques ont été validées.</p>
          </div>

          <div v-else class="grid gap-6">
            <div
              v-for="evaluation in evaluationsEnAttente"
              :key="evaluation.id"
              class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
            >
              <div class="p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center gap-3 mb-3">
                      <div class="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                        <span class="text-emerald-600 font-semibold text-sm">
                          {{ evaluation.stage.stagiaire.user.prenom.charAt(0) }}{{ evaluation.stage.stagiaire.user.nom.charAt(0) }}
                        </span>
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold text-gray-900">
                          {{ evaluation.stage.stagiaire.user.prenom }} {{ evaluation.stage.stagiaire.user.nom }}
                        </h3>
                        <p class="text-sm text-gray-500">{{ evaluation.stage.structure.libelle }}</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">Note obtenue</div>
                        <div class="text-2xl font-bold text-emerald-600 mt-1">{{ evaluation.note_totale }}/20</div>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">Évalué le</div>
                        <div class="text-sm font-medium text-gray-900 mt-1">
                          {{ formatDate(evaluation.date_evaluation) }}
                        </div>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">Maître de stage</div>
                        <div class="text-sm font-medium text-gray-900 mt-1">
                          {{ evaluation.agent.user.prenom }} {{ evaluation.agent.user.nom }}
                        </div>
                      </div>
                    </div>

                    <div v-if="evaluation.commentaire_general" class="bg-blue-50 rounded-lg p-4 mb-4">
                      <h4 class="text-sm font-medium text-blue-900 mb-2">Commentaire du maître de stage :</h4>
                      <p class="text-sm text-blue-800">{{ evaluation.commentaire_general }}</p>
                    </div>
                  </div>
                </div>

                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div class="flex items-center gap-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                      ⏳ En attente de validation
                    </span>
                  </div>
                  <div class="flex gap-3">
                    <button
                      @click="ouvrirModalValidation(evaluation)"
                      class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
                    >
                      ✅ Valider l'évaluation
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Évaluations validées -->
        <div v-if="activeTab === 'validees'">
          <div v-if="evaluationsValidees.length === 0" class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune évaluation validée</h3>
            <p class="text-gray-500">L'historique des validations apparaîtra ici.</p>
          </div>

          <div v-else class="grid gap-6">
            <div
              v-for="evaluation in evaluationsValidees"
              :key="evaluation.id"
              class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
            >
              <div class="p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center gap-3 mb-3">
                      <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <span class="text-green-600 font-semibold text-sm">
                          {{ evaluation.stage.stagiaire.user.prenom.charAt(0) }}{{ evaluation.stage.stagiaire.user.nom.charAt(0) }}
                        </span>
                      </div>
                      <div>
                        <h3 class="text-lg font-semibold text-gray-900">
                          {{ evaluation.stage.stagiaire.user.prenom }} {{ evaluation.stage.stagiaire.user.nom }}
                        </h3>
                        <p class="text-sm text-gray-500">{{ evaluation.stage.structure.libelle }}</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                      <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">Note</div>
                        <div class="text-xl font-bold text-green-600 mt-1">{{ evaluation.note_totale }}/20</div>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">Évalué le</div>
                        <div class="text-sm font-medium text-gray-900 mt-1">
                          {{ formatDate(evaluation.date_evaluation) }}
                        </div>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">Validé le</div>
                        <div class="text-sm font-medium text-gray-900 mt-1">
                          {{ formatDate(evaluation.date_validation_ru) }}
                        </div>
                      </div>
                      <div class="bg-gray-50 rounded-lg p-3">
                        <div class="text-xs font-medium text-gray-500 uppercase tracking-wider">Validé par</div>
                        <div class="text-sm font-medium text-gray-900 mt-1">
                          {{ evaluation.ru_validateur.user.prenom }} {{ evaluation.ru_validateur.user.nom }}
                        </div>
                      </div>
                    </div>

                    <div v-if="evaluation.commentaire_ru" class="bg-emerald-50 rounded-lg p-4">
                      <h4 class="text-sm font-medium text-emerald-900 mb-2">Votre commentaire de validation :</h4>
                      <p class="text-sm text-emerald-800">{{ evaluation.commentaire_ru }}</p>
                    </div>
                  </div>
                </div>

                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    ✅ Validée
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de validation -->
    <Modal :show="showModalValidation" @close="fermerModalValidation">
      <div class="p-8">
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-semibold text-gray-900">Valider l'évaluation</h3>
            <p class="text-gray-500">
              {{ evaluationAValider?.stage?.stagiaire?.user?.prenom }} {{ evaluationAValider?.stage?.stagiaire?.user?.nom }}
            </p>
          </div>
        </div>

        <div v-if="evaluationAValider" class="space-y-6">
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-2">Détails de l'évaluation</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">Note obtenue :</span>
                <span class="font-semibold ml-2">{{ evaluationAValider.note_totale }}/20</span>
              </div>
              <div>
                <span class="text-gray-500">Structure :</span>
                <span class="font-semibold ml-2">{{ evaluationAValider.stage.structure.libelle }}</span>
              </div>
            </div>
            <div v-if="evaluationAValider.commentaire_general" class="mt-3">
              <span class="text-gray-500">Commentaire du MS :</span>
              <p class="text-gray-900 mt-1">{{ evaluationAValider.commentaire_general }}</p>
            </div>
          </div>

          <div>
            <label for="commentaire_ru" class="block text-sm font-medium text-gray-700 mb-2">
              Commentaire de validation (optionnel)
            </label>
            <textarea
              id="commentaire_ru"
              v-model="commentaireRU"
              rows="4"
              class="w-full border border-gray-300 rounded-md shadow-sm focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="Ajoutez un commentaire sur cette évaluation..."
            ></textarea>
          </div>

          <div class="flex justify-end gap-3">
            <button
              @click="fermerModalValidation"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              @click="validerEvaluation"
              :disabled="isValidating"
              class="px-6 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 disabled:opacity-50"
            >
              <span v-if="isValidating">Validation...</span>
              <span v-else">✅ Valider</span>
            </button>
          </div>
        </div>
      </div>
    </Modal>
  </ResponsableUniversiteLayout>
</template>

<script setup>
import { ref } from 'vue'
import { router } from '@inertiajs/vue3'
import ResponsableUniversiteLayout from '@/Layouts/ResponsableUniversiteLayout.vue'
import Modal from '@/Components/Modal.vue'

const props = defineProps({
  evaluationsEnAttente: Array,
  evaluationsValidees: Array,
  universite: Object,
  notifications: Array
})

const activeTab = ref('attente')
const showModalValidation = ref(false)
const evaluationAValider = ref(null)
const commentaireRU = ref('')
const isValidating = ref(false)

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const ouvrirModalValidation = (evaluation) => {
  evaluationAValider.value = evaluation
  commentaireRU.value = ''
  showModalValidation.value = true
}

const fermerModalValidation = () => {
  showModalValidation.value = false
  evaluationAValider.value = null
  commentaireRU.value = ''
}

const validerEvaluation = () => {
  if (!evaluationAValider.value) return
  
  isValidating.value = true
  
  router.post(route('responsable-universite.evaluations.valider', evaluationAValider.value.id), {
    commentaire_ru: commentaireRU.value
  }, {
    onSuccess: () => {
      fermerModalValidation()
      // Rafraîchir la page pour voir les changements
      router.reload()
    },
    onError: (errors) => {
      console.error('Erreur lors de la validation:', errors)
    },
    onFinish: () => {
      isValidating.value = false
    }
  })
}
</script>
