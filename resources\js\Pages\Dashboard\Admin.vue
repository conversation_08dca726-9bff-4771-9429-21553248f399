<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { computed, ref, onMounted } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

// Access props from controller
const props = defineProps({
    stats: Object,
    error: {
        type: String,
        default: null
    },
    recentActivities: {
        type: Array,
        default: () => []
    }
});

// Map stats to our cards format with enhanced visual elements and trend indicators
const statsCards = computed(() => [
    { 
        title: 'Utilisateurs', 
        count: props.stats.users, 
        icon: 'users', 
        color: 'blue', 
        gradient: 'from-blue-400 to-blue-600',
        route: 'admin.users.index',
        trend: 5, // Example: 5% increase
        trendUp: true
    },
    { 
        title: 'Structures', 
        count: props.stats.structures, 
        icon: 'building', 
        color: 'emerald', 
        gradient: 'from-emerald-400 to-emerald-600',
        route: 'admin.structures.index',
        trend: 2, // Example: 2% increase
        trendUp: true
    },
    { 
        title: 'Stagiaires', 
        count: props.stats.stagiaires, 
        icon: 'user-graduate', 
        color: 'violet', 
        gradient: 'from-violet-400 to-violet-600',
        route: 'admin.stagiaires.index',
        trend: 8, // Example: 8% increase
        trendUp: true
    },
    { 
        title: 'Agents', 
        count: props.stats.agents, 
        icon: 'briefcase', 
        color: 'amber', 
        gradient: 'from-amber-400 to-amber-600',
        route: 'admin.agents.index',
        trend: 1, // Example: 1% decrease
        trendUp: false
    },
]);

// Quick actions with enhanced visual styling and organized by categories
const quickActions = [
    { 
        title: 'Ajouter un utilisateur', 
        route: 'admin.users.index', 
        icon: 'user-plus', 
        color: 'blue',
        description: 'Créer un nouveau compte utilisateur',
        category: 'users'
    },
    { 
        title: 'Ajouter une structure', 
        route: 'admin.structures.index', 
        icon: 'building', 
        color: 'emerald',
        description: 'Enregistrer une nouvelle structure',
        category: 'structures'
    },
    { 
        title: 'Voir les stagiaires', 
        route: 'admin.stagiaires.index', 
        icon: 'user-graduate', 
        color: 'violet',
        description: 'Accéder à la liste des stagiaires',
        category: 'stagiaires'
    },
    { 
        title: 'Ajouter un Agent', 
        route: 'admin.agents.index', 
        icon: 'briefcase', 
        color: 'amber',
        description: 'Enregistrer un nouvel agent',
        category: 'agents'
    },
    {
        title: 'Télécharger le rapport mensuel',
        route: 'admin.rapport-mensuel',
        icon: 'download', // icône changée de 'file-download' à 'download'
        color: 'indigo',
        description: 'Exporter le rapport mensuel des activités',
        download: true,
        category: 'reports'
    }
];

// Activity icon mapping with improved color scheme
const activityIconMap = {
    'stagiaire': { icon: 'user-graduate', color: 'blue', gradient: 'from-blue-400 to-blue-600' },
    'structure': { icon: 'building', color: 'emerald', gradient: 'from-emerald-400 to-emerald-600' },
    'agent': { icon: 'briefcase', color: 'amber', gradient: 'from-amber-400 to-amber-600' },
    'user': { icon: 'users', color: 'violet', gradient: 'from-violet-400 to-violet-600' },
    'rapport': { icon: 'file-alt', color: 'indigo', gradient: 'from-indigo-400 to-indigo-600' },
    'demande-stage': { icon: 'file-signature', color: 'teal', gradient: 'from-teal-400 to-teal-600' }, // Ajout pour dernière demande de stage
    'demande': { icon: 'file-signature', color: 'teal', gradient: 'from-teal-400 to-teal-600' },
    'default': { icon: 'circle-info', color: 'gray', gradient: 'from-gray-400 to-gray-600' }
};

// Group activities by date for better organization
const groupedActivities = computed(() => {
    const groups = {};
    const today = new Date().toLocaleDateString('fr-FR');
    const yesterday = new Date(Date.now() - 86400000).toLocaleDateString('fr-FR');
    
    props.recentActivities.forEach(activity => {
        const date = new Date(activity.created_at).toLocaleDateString('fr-FR');
        let groupName = date;
        
        if (date === today) {
            groupName = "Aujourd'hui";
        } else if (date === yesterday) {
            groupName = "Hier";
        }
        
        if (!groups[groupName]) {
            groups[groupName] = [];
        }
        
        groups[groupName].push(activity);
    });
    
    return groups;
});

// Helper function for time formatting
function timeAgo(date) {
    const now = new Date();
    const d = new Date(date);
    const diff = (now - d) / 1000;
    if (diff < 60) return "À l'instant";
    if (diff < 3600) return `Il y a ${Math.floor(diff/60)} min`;
    if (diff < 86400) return `Il y a ${Math.floor(diff/3600)} h`;
    if (diff < 172800) return 'Hier';
    return `Il y a ${Math.floor(diff/86400)} jours`;
}

// Format time as hours:minutes
function formatTime(date) {
    return new Date(date).toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
}

// For animation states and dashboard refresh
const isLoaded = ref(false);
const lastRefreshed = ref(new Date());
const refreshing = ref(false);

// Initialize animations and dashboard data
onMounted(() => {
    setTimeout(() => {
        isLoaded.value = true;
    }, 100);
});

// Function to refresh dashboard data
function refreshDashboard() {
    refreshing.value = true;
    // Simulate refresh time (in a real app, you would fetch data here)
    setTimeout(() => {
        lastRefreshed.value = new Date();
        refreshing.value = false;
    }, 800);
}
</script>

<template>
    <Head title="Administration" />

    <AdminLayout>
        <!-- Header professionnel -->
        <div class="py-6">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div class="mb-8">
                    <div class="flex items-center gap-3 mb-4">
                        <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-12 h-12 flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-800">
                                Tableau de bord d'administration
                            </h1>
                            <p class="text-sm text-gray-600 mt-1">Vue d'ensemble des statistiques et activités du système</p>
                        </div>
                    </div>
                </div>

                <!-- Error Message -->
                <transition
                    enter-active-class="transition duration-300 ease-out"
                    enter-from-class="transform -translate-y-4 opacity-0"
                    enter-to-class="transform translate-y-0 opacity-100"
                    leave-active-class="transition duration-200 ease-in"
                    leave-from-class="opacity-100"
                    leave-to-class="opacity-0"
                >
                    <div
                        v-if="error"
                        class="mb-6 rounded-lg bg-red-50 p-4 shadow-sm border-l-4 border-red-500"
                    >
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <FontAwesomeIcon icon="exclamation-circle" class="h-5 w-5 text-red-500" />
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">{{ error }}</h3>
                            </div>
                        </div>
                    </div>
                </transition>
                
                <!-- Cartes statistiques professionnelles -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                    <div
                        v-for="(stat, index) in statsCards"
                        :key="index"
                        class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow"
                    >
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 rounded-xl p-3 shadow-lg">
                                    <FontAwesomeIcon
                                        :icon="['fas', stat.icon]"
                                        class="text-white text-lg"
                                    />
                                </div>
                            </div>
                            <div class="ml-4 flex-1">
                                <p class="text-sm font-medium text-gray-600 mb-1">
                                    {{ stat.title }}
                                </p>
                                <div class="flex items-center justify-between">
                                    <p class="text-2xl font-bold text-gray-900">
                                        {{ stat.count }}
                                    </p>
                                    <Link
                                        :href="route(stat.route)"
                                        class="text-purple-600 hover:text-purple-700 transition-colors"
                                    >
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Panneau d'activités récentes -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Activités récentes -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <div class="flex items-center gap-3">
                                    <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-10 h-10 flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">Activités récentes</h3>
                                        <p class="text-sm text-gray-600">{{ props.recentActivities.length }} activités</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <template v-if="props.recentActivities.length > 0">
                                    <div class="space-y-4">
                                        <div v-for="(activity, idx) in props.recentActivities.slice(0, 5)" :key="idx" class="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                            <div class="flex-shrink-0">
                                                <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <p class="text-sm font-medium text-gray-900 truncate">
                                                    {{ activity.title }}
                                                    <span v-if="activity.user" class="font-normal">: {{ activity.user }}</span>
                                                </p>
                                                <p v-if="activity.description" class="text-sm text-gray-500 mt-1 truncate">
                                                    {{ activity.description }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="text-center py-8">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune activité</h3>
                                        <p class="mt-1 text-sm text-gray-500">Aucune activité récente à afficher.</p>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                                <div class="flex items-center gap-3">
                                    <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-10 h-10 flex items-center justify-center shadow-lg">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">Actions rapides</h3>
                                        <p class="text-sm text-gray-600">Raccourcis utiles</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="space-y-3">
                                    <template v-for="(action, index) in quickActions.slice(0, 4)" :key="index">
                                        <Link
                                            :href="route(action.route)"
                                            class="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-purple-50 hover:border-purple-200 transition-all"
                                        >
                                            <div class="flex-shrink-0 bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 rounded-lg p-2">
                                                <FontAwesomeIcon
                                                    :icon="action.icon"
                                                    class="text-white h-4 w-4"
                                                />
                                            </div>
                                            <div class="ml-3 flex-1">
                                                <p class="text-sm font-medium text-gray-900">{{ action.title }}</p>
                                                <p class="text-xs text-gray-500 mt-1">{{ action.description }}</p>
                                            </div>
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                            </svg>
                                        </Link>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<style scoped>
/* Enhanced animations and transitions */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.stats-card-animation {
  animation: fadeIn 0.3s ease-out forwards;
}

.slide-animation {
  animation: slideIn 0.3s ease-out forwards;
}

/* Improve responsiveness for small screens */
@media (max-width: 640px) {
  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .date-display {
    margin-top: 0.5rem;
  }
}
</style>