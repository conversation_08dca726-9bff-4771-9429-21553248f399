<template>
  <AdminLayout>
    <template #header>
      <div class="flex items-center gap-3 mb-2">
        <div class="bg-gradient-to-br from-indigo-600 via-indigo-700 to-blue-600 text-white rounded-xl w-12 h-12 flex items-center justify-center shadow-lg">
          <ChartBarIcon class="w-6 h-6" />
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800">
            Organigrammes
          </h1>
          <p class="text-sm text-gray-600 mt-1">Vue d'ensemble de l'organisation (lecture seule)</p>
        </div>
      </div>
    </template>

    <AdminToast ref="toastRef" />

    <div class="space-y-6">
      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="flex items-center gap-4">
          <button @click="expandAll" 
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <FolderOpenIcon class="w-4 h-4 mr-2" />
            Tout déplier
          </button>
          <button @click="collapseAll" 
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <FolderIcon class="w-4 h-4 mr-2" />
            Tout replier
          </button>
        </div>
        <button @click="printOrganigramme" 
                class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          <PrinterIcon class="w-4 h-4 mr-2" />
          Imprimer
        </button>
      </div>

      <!-- Statistiques -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-indigo-100">
              <BuildingOfficeIcon class="w-6 h-6 text-indigo-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Structures DG</p>
              <p class="text-2xl font-bold text-gray-900">{{ structures.length }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100">
              <BuildingOffice2Icon class="w-6 h-6 text-purple-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Sous-structures</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalSousStructures }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100">
              <UserIcon class="w-6 h-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Agents RS</p>
              <p class="text-2xl font-bold text-gray-900">{{ agentsRS }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <UserGroupIcon class="w-6 h-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Agents MS</p>
              <p class="text-2xl font-bold text-gray-900">{{ agentsMS }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Organigramme -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 class="text-lg font-semibold text-gray-900">Organigramme Général</h3>
          <p class="text-sm text-gray-600 mt-1">Vue d'ensemble de toute l'organisation</p>
        </div>

        <div class="p-8">
          <div class="bg-gradient-to-br from-indigo-50 via-white to-blue-50 rounded-3xl p-8 border-2 border-indigo-100 shadow-inner">
            <div v-if="structures.length > 0" class="space-y-6">
              <div v-for="structure in structures" :key="structure.id" class="bg-white rounded-lg border border-gray-200 p-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <BuildingOfficeIcon class="w-6 h-6 text-indigo-600" />
                    <div>
                      <h3 class="font-semibold text-gray-900">{{ structure.nom }}</h3>
                      <p class="text-sm text-gray-500">{{ structure.type }}</p>
                    </div>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">
                      Responsable: {{ structure.responsable?.user?.nom || 'Non assigné' }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-12">
              <ChartBarIcon class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune structure</h3>
              <p class="mt-1 text-sm text-gray-500">Aucune structure n'a été créée pour le moment.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';
import { computed, ref } from 'vue';
import {
  ChartBarIcon,
  BuildingOfficeIcon,
  BuildingOffice2Icon,
  UserIcon,
  UserGroupIcon,
  FolderOpenIcon,
  FolderIcon,
  PrinterIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  structures: Array,
  agents: Array
});

const toastRef = ref(null);
const expandedStructures = ref(new Set());

const totalSousStructures = computed(() => {
  let count = 0;
  const countChildren = (structure) => {
    if (structure.children) {
      count += structure.children.length;
      structure.children.forEach(countChildren);
    }
  };
  props.structures.forEach(countChildren);
  return count;
});

const agentsRS = computed(() => {
  return props.agents.filter(agent => agent.role_agent === 'RS').length;
});

const agentsMS = computed(() => {
  return props.agents.filter(agent => agent.role_agent === 'MS').length;
});

const getAgentsForStructure = (structureId) => {
  return props.agents.filter(agent => agent.structure_id === structureId);
};

const toggleExpand = (structureId) => {
  if (expandedStructures.value.has(structureId)) {
    expandedStructures.value.delete(structureId);
  } else {
    expandedStructures.value.add(structureId);
  }
};

const expandAll = () => {
  const allIds = new Set();
  const collectIds = (structure) => {
    allIds.add(structure.id);
    if (structure.children) {
      structure.children.forEach(collectIds);
    }
  };
  props.structures.forEach(collectIds);
  expandedStructures.value = allIds;
};

const collapseAll = () => {
  expandedStructures.value.clear();
};

const printOrganigramme = () => {
  window.print();
};
</script>

<style>
@media print {
  .no-print {
    display: none !important;
  }
}
</style>
