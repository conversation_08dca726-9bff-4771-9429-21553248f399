<template>
    <teleport to="body">
        <div class="fixed top-4 right-4 z-50 space-y-2">
            <SuccessMessage
                v-for="message in successMessages"
                :key="message.id"
                :show="message.show"
                :title="message.title"
                :message="message.message"
                :duration="message.duration"
                @close="removeMessage(message.id)"
            />
        </div>
    </teleport>
</template>

<script setup>
import { useSuccessMessage } from '@/Composables/useSuccessMessage'
import SuccessMessage from '@/Components/SuccessMessage.vue'

const { successMessages, removeMessage } = useSuccessMessage()
</script>
