# Templates d'Emails Modernisés

## Vue d'ensemble

Ce système de templates d'emails modernisés offre une approche cohérente et professionnelle pour tous les emails de l'application de gestion des stages.

## Structure

### Layout Principal
- `layouts/modern.blade.php` : Layout de base avec header, footer et styles responsive

### Composants Réutilisables
- `components/success-message.blade.php` : Messages de succès avec style moderne
- `components/info-box.blade.php` : Boîtes d'information colorées
- `components/button.blade.php` : Boutons d'action avec gradients
- `components/details-table.blade.php` : Tableaux de détails responsive

### Templates Modernisés
- `demande-confirmation.blade.php` : Confirmation de demande (modernisé)
- `theme_propose_modern.blade.php` : Exemple d'utilisation des composants

## Utilisation

### Créer un nouveau template

```blade
@extends('emails.layouts.modern')

@section('title', 'Titre de l\'email')
@section('header-title', 'Titre Principal')
@section('header-subtitle', 'Sous-titre')

@section('content')
    <!-- Votre contenu ici -->
@endsection
```

### Utiliser les composants

#### Message de succès
```blade
@include('emails.components.success-message', [
    'title' => 'Félicitations !',
    'message' => 'Votre action a été effectuée avec succès.',
    'badge' => 'SUCCÈS'
])
```

#### Boîte d'information
```blade
@include('emails.components.info-box', [
    'title' => 'Information importante',
    'icon' => '📋',
    'color' => '#3b82f6'
])
    <p>Contenu de la boîte d'information</p>
@endinclude
```

#### Bouton d'action
```blade
@include('emails.components.button', [
    'url' => 'https://example.com',
    'text' => 'Cliquez ici',
    'icon' => '🔗',
    'color' => '#10b981',
    'colorHover' => '#059669'
])
```

#### Tableau de détails
```blade
@include('emails.components.details-table', [
    'title' => 'Détails',
    'icon' => '📊'
])
    <tr>
        <td class="label">Libellé :</td>
        <td class="value">Valeur</td>
    </tr>
@endinclude
```

## Palette de Couleurs

### Couleurs Principales
- Bleu principal : `#3b82f6`
- Bleu foncé : `#1e40af`
- Slate : `#475569`
- Slate foncé : `#1e293b`

### Couleurs d'État
- Succès : `#10b981`
- Attention : `#f59e0b`
- Erreur : `#ef4444`
- Information : `#3b82f6`

## Responsive Design

Tous les templates sont optimisés pour :
- Clients desktop (Outlook, Thunderbird, etc.)
- Clients web (Gmail, Yahoo, etc.)
- Applications mobiles
- Mode sombre (partiellement supporté)

## Bonnes Pratiques

1. **Utilisez toujours le layout de base** pour la cohérence
2. **Réutilisez les composants** plutôt que de créer du HTML personnalisé
3. **Testez sur différents clients** email avant déploiement
4. **Gardez le contenu concis** et structuré
5. **Utilisez les icônes emoji** pour améliorer la lisibilité

## Migration des Templates Existants

Pour migrer un template existant :

1. Remplacez l'ancien HTML par `@extends('emails.layouts.modern')`
2. Déplacez le contenu dans `@section('content')`
3. Remplacez les éléments par les composants appropriés
4. Testez le rendu sur différents clients email

## Support

Pour toute question sur l'utilisation de ces templates, consultez la documentation ou contactez l'équipe de développement.
