<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demande de stage acceptée</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); color: #2c3e50; margin: 0; padding: 20px; line-height: 1.6;">
    <div style="max-width: 650px; margin: 20px auto; background: #ffffff; border-radius: 16px; box-shadow: 0 8px 32px rgba(16, 185, 129, 0.12); overflow: hidden; border: 1px solid rgba(16, 185, 129, 0.1);">

        <!-- Header Section -->
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 40px 24px; text-align: center; position: relative;">
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #34d399, #6ee7b7, #34d399);"></div>
            <img src="{{ asset('images/logoministere.png') }}" alt="Logo Ministère" style="height: 70px; margin-bottom: 16px; filter: brightness(1.1);">
            <h1 style="color: #ffffff; margin: 0; font-size: 1.75rem; font-weight: 600; letter-spacing: -0.5px;">Gestion des Stages</h1>
            <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 0.95rem;">Demande acceptée avec succès</p>
        </div>


            <!-- Success Message -->
            <div style="border-left: 4px solid #10b981; padding-left: 20px; margin-bottom: 32px;">
                <h2 style="color: #059669; margin: 0 0 16px 0; font-size: 1.5rem; font-weight: 600;">
                    ✅ Demande de stage acceptée
                </h2>
                <p style="margin: 0; font-size: 1.1rem; color: #4a5568;">
                    Bonjour <strong style="color: #059669;">{{ $stagiaire->user->prenom }} {{ $stagiaire->user->nom }}</strong>,
                </p>
                <p style="margin: 12px 0 0 0; color: #6b7280;">
                    Nous avons le plaisir de vous informer que votre demande de stage a été <span style="color: #10b981; font-weight: 600; background: #f0fdf4; padding: 2px 8px; border-radius: 12px;">acceptée</span> par nos services.
                </p>
            </div>

            <!-- Stage Details -->
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%); border: 1px solid #bbf7d0; border-radius: 12px; padding: 24px; margin: 24px 0;">
                <h3 style="color: #059669; margin: 0 0 20px 0; font-size: 1.25rem; font-weight: 600; display: flex; align-items: center;">
                    <span style="background: #10b981; color: white; border-radius: 50%; width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 0.875rem;">📋</span>
                    Détails de votre stage
                </h3>
                <div style="display: grid; gap: 12px;">
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #059669; font-weight: 600; min-width: 140px;">📅 Période :</span>
                        <span style="color: #374151;">du {{ \Carbon\Carbon::parse($demande->date_debut)->format('d/m/Y') }} au {{ \Carbon\Carbon::parse($demande->date_fin)->format('d/m/Y') }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #059669; font-weight: 600; min-width: 140px;">🏢 Structure :</span>
                        <span style="color: #374151;">{{ $demande->structure->libelle ?? 'À définir' }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #059669; font-weight: 600; min-width: 140px;">📝 Type :</span>
                        <span style="color: #374151;">Stage {{ $demande->type }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #059669; font-weight: 600; min-width: 140px;">👥 Nature :</span>
                        <span style="color: #374151;">{{ $demande->nature }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #059669; font-weight: 600; min-width: 140px;">🔍 Code de suivi :</span>
                        <span style="color: #374151; font-family: monospace; background: #f3f4f6; padding: 4px 8px; border-radius: 6px;">{{ $demande->code_suivi }}</span>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div style="background: #fefce8; border: 1px solid #fde047; border-radius: 12px; padding: 20px; margin: 24px 0;">
                <h3 style="color: #a16207; margin: 0 0 16px 0; font-size: 1.1rem; font-weight: 600;">🎯 Prochaines étapes</h3>
                <ol style="margin: 0; padding-left: 20px; color: #92400e;">
                    <li style="margin-bottom: 8px;">Vous serez contacté(e) prochainement pour finaliser les modalités</li>
                    <li style="margin-bottom: 8px;">Un maître de stage vous sera assigné</li>
                    <li style="margin-bottom: 8px;">Vous recevrez les informations pratiques pour votre premier jour</li>
                </ol>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 32px 0;">
                <a href="{{ route('stagiaire.dashboard') }}" style="display: inline-block; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 12px; font-weight: 600; font-size: 1rem; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3); transition: transform 0.2s;">
                    📊 Accéder à mon espace stagiaire
                </a>
            </div>

            <!-- Congratulations Message -->
            <div style="background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%); border-left: 4px solid #10b981; padding: 20px; margin: 24px 0; border-radius: 0 12px 12px 0;">
                <p style="margin: 0; color: #059669; font-weight: 600; font-size: 1.1rem;">
                    🎉 Nous vous souhaitons un excellent stage au sein du Ministère de l'Économie et des Finances du Bénin !
                </p>
                <p style="margin: 8px 0 0 0; color: #6b7280; font-size: 0.95rem;">
                    En cas de questions, n'hésitez pas à nous contacter.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%); padding: 24px 32px; border-top: 1px solid #e5e7eb; text-align: center;">
            <p style="margin: 0 0 8px 0; color: #6b7280; font-size: 0.875rem;">
                Cet email a été envoyé automatiquement par le système de gestion des stages.
            </p>
            <p style="margin: 0; color: #374151; font-weight: 600; font-size: 0.95rem;">
                Ministère de l'Économie et des Finances du Bénin
            </p>
            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 0.875rem;">
                Direction du Placement et de l'Affectation des Fonctionnaires (DPAF)
            </p>
        </div>
    </div>
</body>
</html>
