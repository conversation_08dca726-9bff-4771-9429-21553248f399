<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\Universite;
use App\Models\DemandeStage;
use App\Models\User;

class RapportController extends Controller
{
    /**
     * Affiche les rapports et statistiques de l'université
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        // Période par défaut : année courante
        $annee = $request->get('annee', date('Y'));
        $mois = $request->get('mois');

        // Statistiques générales
        $stats = [
            'total_etudiants' => User::whereHas('stagiaire', function ($q) use ($universite) {
                $q->where('universite', $universite->nom_complet);
            })->count(),
            
            'total_demandes' => DemandeStage::whereHas('stagiaire.user', function ($q) use ($universite) {
                $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                    $subQ->where('universite', $universite->nom_complet);
                });
            })->count(),
            
            'demandes_acceptees' => DemandeStage::whereHas('stagiaire.user', function ($q) use ($universite) {
                $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                    $subQ->where('universite', $universite->nom_complet);
                });
            })->where('statut', 'Acceptée')->count(),
            
            'stages_termines' => DemandeStage::whereHas('stagiaire.user', function ($q) use ($universite) {
                $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                    $subQ->where('universite', $universite->nom_complet);
                });
            })->where('statut', 'Terminé')->count(),
        ];

        // Répartition par type de stage
        $repartitionTypeStage = DemandeStage::whereHas('stagiaire.user', function ($q) use ($universite) {
            $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                $subQ->where('universite', $universite->nom_complet);
            });
        })
        ->selectRaw('type_stage, COUNT(*) as count')
        ->groupBy('type_stage')
        ->get();

        // Répartition par statut
        $repartitionStatut = DemandeStage::whereHas('stagiaire.user', function ($q) use ($universite) {
            $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                $subQ->where('universite', $universite->nom_complet);
            });
        })
        ->selectRaw('statut, COUNT(*) as count')
        ->groupBy('statut')
        ->get();

        // Évolution mensuelle des demandes
        $evolutionMensuelle = DemandeStage::whereHas('stagiaire.user', function ($q) use ($universite) {
            $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                $subQ->where('universite', $universite->nom_complet);
            });
        })
        ->whereYear('created_at', $annee)
        ->selectRaw('MONTH(created_at) as mois, COUNT(*) as count')
        ->groupBy('mois')
        ->orderBy('mois')
        ->get();

        // Top 5 des structures d'accueil
        $topStructures = DemandeStage::with('structure')
            ->whereHas('stagiaire.user', function ($q) use ($universite) {
                $q->whereHas('stagiaire', function ($subQ) use ($universite) {
                    $subQ->where('universite', $universite->nom_complet);
                });
            })
            ->where('statut', 'Acceptée')
            ->selectRaw('structure_id, COUNT(*) as count')
            ->groupBy('structure_id')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get();

        return Inertia::render('Agent/RU/Rapports/Index', [
            'pageTitle' => 'Rapports et Statistiques',
            'universite' => $universite,
            'stats' => $stats,
            'repartitionTypeStage' => $repartitionTypeStage,
            'repartitionStatut' => $repartitionStatut,
            'evolutionMensuelle' => $evolutionMensuelle,
            'topStructures' => $topStructures,
            'filters' => [
                'annee' => $annee,
                'mois' => $mois,
            ],
        ]);
    }
}
