<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Agent;
use App\Models\Structure;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class FinalValidationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer les structures de base
        $this->createBasicStructures();
    }

    private function createBasicStructures()
    {
        // Structure principale
        Structure::create([
            'id' => 1,
            'libelle' => 'Direction Générale',
            'sigle' => 'DG',
            'parent_id' => null,
            'responsable_id' => null,
        ]);

        // Sous-structure
        Structure::create([
            'id' => 2,
            'libelle' => 'Direction des Ressources Humaines',
            'sigle' => 'DRH',
            'parent_id' => 1,
            'responsable_id' => null,
        ]);
    }

    /** @test */
    public function admin_can_access_dashboard()
    {
        // Créer un utilisateur admin
        $admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        // Créer l'agent admin
        Agent::create([
            'user_id' => $admin->id,
            'role_agent' => 'admin',
            'structure_id' => 1,
            'created_by' => null,
        ]);

        // Tester l'accès au dashboard admin
        $response = $this->actingAs($admin)->get('/admin/dashboard');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Dashboard'));
    }

    /** @test */
    public function admin_can_only_create_rs_agents()
    {
        // Créer un utilisateur admin
        $admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        Agent::create([
            'user_id' => $admin->id,
            'role_agent' => 'admin',
            'structure_id' => 1,
            'created_by' => null,
        ]);

        // Créer un utilisateur pour devenir agent RS
        $rsUser = User::factory()->create([
            'role' => 'agent',
            'email' => '<EMAIL>'
        ]);

        // Tester la création d'un agent RS
        $response = $this->actingAs($admin)->post('/admin/agents', [
            'user_id' => $rsUser->id,
            'role_agent' => 'RS',
            'structure_id' => 1,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('agents', [
            'user_id' => $rsUser->id,
            'role_agent' => 'RS',
            'created_by' => $admin->id,
        ]);
    }

    /** @test */
    public function rs_agent_can_access_dashboard()
    {
        // Créer un utilisateur RS
        $rsUser = User::factory()->create([
            'role' => 'agent',
            'email' => '<EMAIL>'
        ]);

        // Créer l'agent RS
        Agent::create([
            'user_id' => $rsUser->id,
            'role_agent' => 'RS',
            'structure_id' => 1,
            'created_by' => 1,
        ]);

        // Mettre à jour la structure pour avoir ce RS comme responsable
        Structure::where('id', 1)->update(['responsable_id' => $rsUser->agent->id]);

        // Tester l'accès au dashboard RS
        $response = $this->actingAs($rsUser)->get('/agent/rs/dashboard');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Agent/RS/Dashboard'));
    }

    /** @test */
    public function ms_agent_can_access_dashboard()
    {
        // Créer un utilisateur MS
        $msUser = User::factory()->create([
            'role' => 'agent',
            'email' => '<EMAIL>'
        ]);

        // Créer l'agent MS
        Agent::create([
            'user_id' => $msUser->id,
            'role_agent' => 'MS',
            'structure_id' => 2,
            'created_by' => 1,
        ]);

        // Tester l'accès au dashboard MS
        $response = $this->actingAs($msUser)->get('/agent/ms/dashboard');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Agent/MS/Dashboard'));
    }

    /** @test */
    public function unauthorized_access_is_blocked()
    {
        // Créer un utilisateur stagiaire
        $stagiaire = User::factory()->create([
            'role' => 'stagiaire',
            'email' => '<EMAIL>'
        ]);

        // Tester que le stagiaire ne peut pas accéder au dashboard admin
        $response = $this->actingAs($stagiaire)->get('/admin/dashboard');
        $response->assertStatus(403);

        // Tester que le stagiaire ne peut pas accéder au dashboard RS
        $response = $this->actingAs($stagiaire)->get('/agent/rs/dashboard');
        $response->assertStatus(403);

        // Tester que le stagiaire ne peut pas accéder au dashboard MS
        $response = $this->actingAs($stagiaire)->get('/agent/ms/dashboard');
        $response->assertStatus(403);
    }

    /** @test */
    public function database_structure_is_correct()
    {
        // Vérifier que les tables principales existent
        $this->assertTrue(\Schema::hasTable('users'));
        $this->assertTrue(\Schema::hasTable('agents'));
        $this->assertTrue(\Schema::hasTable('structures'));
        $this->assertTrue(\Schema::hasTable('demande_stages'));
        $this->assertTrue(\Schema::hasTable('stages'));
        $this->assertTrue(\Schema::hasTable('affectation_maitre_stages'));

        // Vérifier que la colonne structure_id existe dans agents
        $this->assertTrue(\Schema::hasColumn('agents', 'structure_id'));
        $this->assertTrue(\Schema::hasColumn('agents', 'created_by'));
    }

    /** @test */
    public function phone_number_formatting_works()
    {
        // Créer un utilisateur avec un numéro de téléphone
        $user = User::factory()->create([
            'telephone' => '+22912345678'
        ]);

        $this->assertEquals('+229 12 34 56 78', $user->formatted_phone);
    }
}
