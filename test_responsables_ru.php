<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TEST RESPONSABLES RU ===\n";

try {
    // Vérifier les universités disponibles
    $universites = App\Models\Universite::whereNull('responsable_id')->get();
    echo "Universités sans responsable: " . $universites->count() . "\n";
    
    if ($universites->count() == 0) {
        // Créer une université de test
        $universite = App\Models\Universite::create([
            'nom_complet' => 'Université d\'Abomey-Calavi',
            'sigle' => 'UAC',
            'description' => 'Université publique du Bénin',
            'localisation' => 'Abomey-Calavi, Bénin',
            'active' => true
        ]);
        echo "Université créée avec ID: " . $universite->id . "\n";
    }
    
    // Vérifier les agents RU
    $agentsRU = App\Models\Agent::where('role_agent', 'RU')->get();
    echo "Agents RU existants: " . $agentsRU->count() . "\n";
    
    // Vérifier les routes
    echo "Routes disponibles:\n";
    echo "- admin.responsables-ru.index\n";
    echo "- admin.responsables-ru.create\n";
    echo "- admin.responsables-ru.store\n";
    echo "- admin.responsables-ru.show\n";
    echo "- admin.responsables-ru.edit\n";
    echo "- admin.responsables-ru.update\n";
    echo "- admin.responsables-ru.destroy\n";
    
    echo "\n✅ Test terminé avec succès!\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
