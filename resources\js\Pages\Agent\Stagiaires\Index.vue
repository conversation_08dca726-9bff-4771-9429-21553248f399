<template>
  <Head title="Liste des Stagiaires" />

  <AdminLayout>
    <div class="py-6">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header professionnel -->
        <div class="mb-8">
          <div class="flex items-center gap-3 mb-4">
            <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-12 h-12 flex items-center justify-center shadow-lg">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-gray-800">
                Liste des Stagiaires
              </h1>
              <p class="text-sm text-gray-600 mt-1">Stagiaires de votre structure ({{ stagiaires.length }} stagiaires)</p>
            </div>
          </div>
        </div>

        <!-- Carte liste stagiaires professionnelle -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center gap-3">
              <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-10 h-10 flex items-center justify-center shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Stagiaires de votre Structure</h3>
                <p class="text-sm text-gray-600 mt-1">Gestion des stagiaires affectés</p>
              </div>
            </div>
          </div>

          <div v-if="stagiaires.length === 0" class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun stagiaire</h3>
            <p class="mt-1 text-sm text-gray-500">Aucun stagiaire n'est affecté à votre structure pour le moment.</p>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stagiaire</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-100">
                <tr v-for="stagiaire in stagiaires" :key="stagiaire.id" class="hover:bg-gray-50 transition">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          {{ (stagiaire.user?.prenom?.charAt(0) || '') + (stagiaire.user?.nom?.charAt(0) || '') }}
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-semibold text-slate-900">{{ stagiaire.user?.prenom }} {{ stagiaire.user?.nom }}</div>
                        <div class="text-sm text-slate-600">Stagiaire</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    <div>
                      <div class="font-medium">{{ stagiaire.user?.email || '-' }}</div>
                      <div class="text-gray-500">{{ stagiaire.user?.telephone || '-' }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-center">
                    <Link
                      :href="route('agent.stagiaires.show', stagiaire.id)"
                      class="inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium text-sm"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                        <circle cx="12" cy="12" r="3"/>
                      </svg>
                      Voir détails
                    </Link>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';

defineProps({
  stagiaires: {
    type: Array,
    required: true
  }
});
</script> 