<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de fin de stage</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .info-box {
            background-color: #faf5ff;
            border-left: 4px solid #a855f7;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .next-steps-box {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .details-table th {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .footer {
            background-color: #f3f4f6;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="{{ asset('images/logo-ministere.png') }}" alt="Ministère de l'Économie et des Finances" class="logo">
            <h1>🎓 Fin de stage</h1>
            <p>Félicitations pour votre parcours !</p>
        </div>

        <div class="content">
            <p>Bonjour {{ $stagiaire->prenom }},</p>

            <div class="info-box">
                <h3>✅ Stage terminé avec succès</h3>
                <p>Nous vous confirmons que votre stage <strong>{{ $stage->demandeStage->code_suivi }}</strong> s'est terminé le {{ \Carbon\Carbon::parse($stage->date_fin)->locale('fr')->isoFormat('DD MMMM YYYY') }}.</p>
            </div>

            <h2>Récapitulatif de votre stage</h2>
            
            <table class="details-table">
                <tr>
                    <th>Code de suivi</th>
                    <td><strong>{{ $stage->demandeStage->code_suivi }}</strong></td>
                </tr>
                <tr>
                    <th>Structure d'accueil</th>
                    <td>{{ $stage->structure->libelle }}</td>
                </tr>
                <tr>
                    <th>Période de stage</th>
                    <td>
                        Du {{ \Carbon\Carbon::parse($stage->date_debut)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                        au {{ \Carbon\Carbon::parse($stage->date_fin)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                        <br><small>({{ \Carbon\Carbon::parse($stage->date_debut)->diffInDays(\Carbon\Carbon::parse($stage->date_fin)) }} jours)</small>
                    </td>
                </tr>
                <tr>
                    <th>Type de stage</th>
                    <td>{{ $stage->type }}</td>
                </tr>
                <tr>
                    <th>Statut final</th>
                    <td><span style="color: #7c3aed; font-weight: bold;">{{ $stage->statut }}</span></td>
                </tr>
            </table>

            <div class="next-steps-box">
                <h4>📋 Prochaines étapes</h4>
                <ul>
                    <li><strong>Évaluation :</strong> Votre maître de stage procédera à votre évaluation</li>
                    @if($stage->type === 'Académique')
                    <li><strong>Validation universitaire :</strong> Les notes seront transmises à votre université</li>
                    @endif
                    <li><strong>Attestation :</strong> Vous recevrez une attestation de stage officielle</li>
                    <li><strong>Rapport de stage :</strong> Finalisez votre rapport si requis par votre établissement</li>
                </ul>
            </div>

            <div class="info-box">
                <h4>🙏 Remerciements</h4>
                <p>Nous tenons à vous remercier pour votre engagement et votre professionnalisme durant ce stage. Nous espérons que cette expérience au sein du Ministère de l'Économie et des Finances du Bénin vous a été enrichissante et formatrice.</p>
            </div>

            <div class="next-steps-box">
                <h4>🔗 Restez connecté</h4>
                <p>N'hésitez pas à maintenir le contact avec votre maître de stage et l'équipe qui vous a accueilli. Ces relations professionnelles pourront vous être précieuses pour votre avenir.</p>
            </div>

            <p>Nous vous souhaitons plein succès dans la suite de vos études et votre carrière professionnelle.</p>

            <div style="text-align: center;">
                <a href="{{ route('stagiaire.stages.show', $stage->id) }}" class="btn">
                    Consulter mon dossier de stage
                </a>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Ministère de l'Économie et des Finances du Bénin</strong><br>
                Direction de la Planification et de l'Administration Financière (DPAF)<br>
                Système de gestion des stages
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                Pour toute question concernant votre attestation ou vos documents, contactez : <EMAIL>
            </p>
        </div>
    </div>
</body>
</html>
