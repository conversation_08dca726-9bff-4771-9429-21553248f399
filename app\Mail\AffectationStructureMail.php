<?php

namespace App\Mail;

use App\Models\DemandeStage;
use App\Models\Structure;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AffectationStructureMail extends Mailable
{
    use Queueable, SerializesModels;

    public $demande;
    public $structure;
    public $responsable;

    /**
     * Create a new message instance.
     */
    public function __construct(DemandeStage $demande, Structure $structure, User $responsable)
    {
        $this->demande = $demande;
        $this->structure = $structure;
        $this->responsable = $responsable;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Nouvelle demande de stage affectée à votre structure',
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.affectation-structure',
            with: [
                'demande' => $this->demande,
                'structure' => $this->structure,
                'responsable' => $this->responsable,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
