<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Evaluation extends Model
{
    protected $fillable = [
        'stage_id',
        'agent_id',
        // Critères standards (stages professionnels)
        'ponctualite',
        'motivation',
        'capacite_apprendre',
        'qualite_travail',
        'rapidite_execution',
        'jugement',
        'esprit_motivation',
        'esprit_collaboration',
        'sens_responsabilite',
        'communication',
        // Critères personnalisés (stages académiques)
        'criteres_personnalises', // JSON pour les critères universitaires
        'format_evaluation_universite_id', // Référence au format utilisé
        // Commun
        'note_totale',
        'commentaire_general',
        'date_evaluation',
        // Nouveaux champs pour validation RU (approche chirurgicale)
        'validee_par_ru',
        'date_validation_ru',
        'ru_validateur_id',
        'commentaire_ru',
    ];

    public function stage()
    {
        return $this->belongsTo(Stage::class);
    }

    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Relation avec l'agent RU qui a validé l'évaluation
     */
    public function ruValidateur()
    {
        return $this->belongsTo(Agent::class, 'ru_validateur_id');
    }

    /**
     * Relation avec le format d'évaluation universitaire utilisé
     */
    public function formatEvaluationUniversite()
    {
        return $this->belongsTo(FormatEvaluationUniversite::class);
    }

    /**
     * Casts pour les nouveaux champs
     */
    protected $casts = [
        'date_evaluation' => 'datetime',
        'date_validation_ru' => 'datetime',
        'validee_par_ru' => 'boolean',
        'criteres_personnalises' => 'array', // Pour les évaluations académiques
    ];

    /**
     * Vérifier si l'évaluation peut être visible au stagiaire
     * Pour les stages académiques, elle doit être validée par le RU
     */
    public function estVisiblePourStagiaire()
    {
        // Si le stage est professionnel, l'évaluation est immédiatement visible
        if ($this->stage && $this->stage->type === 'professionnel') {
            return true;
        }

        // Si le stage est académique, l'évaluation doit être validée par le RU
        if ($this->stage && $this->stage->type === 'academique') {
            return $this->validee_par_ru;
        }

        // Par défaut, visible (pour compatibilité avec l'existant)
        return true;
    }

    /**
     * Vérifier si cette évaluation utilise le format standard (professionnel)
     */
    public function estFormatStandard()
    {
        return $this->stage && $this->stage->type === 'professionnel';
    }

    /**
     * Vérifier si cette évaluation utilise un format personnalisé (académique)
     */
    public function estFormatPersonnalise()
    {
        return $this->stage && $this->stage->type === 'academique';
    }

    /**
     * Obtenir les critères d'évaluation selon le type
     */
    public function getCriteres()
    {
        if ($this->estFormatStandard()) {
            // Format standard pour stages professionnels
            return [
                'ponctualite' => $this->ponctualite,
                'motivation' => $this->motivation,
                'capacite_apprendre' => $this->capacite_apprendre,
                'qualite_travail' => $this->qualite_travail,
                'rapidite_execution' => $this->rapidite_execution,
                'jugement' => $this->jugement,
                'esprit_motivation' => $this->esprit_motivation,
                'esprit_collaboration' => $this->esprit_collaboration,
                'sens_responsabilite' => $this->sens_responsabilite,
                'communication' => $this->communication,
            ];
        } else {
            // Format personnalisé pour stages académiques
            return $this->criteres_personnalises ?? [];
        }
    }

    /**
     * Obtenir les intitulés des critères selon le format
     */
    public function getIntitulesCriteres()
    {
        if ($this->estFormatStandard()) {
            // Intitulés standards pour stages professionnels
            return [
                'ponctualite' => 'Ponctualité',
                'motivation' => 'Motivation',
                'capacite_apprendre' => 'Capacité d\'apprentissage',
                'qualite_travail' => 'Qualité du travail',
                'rapidite_execution' => 'Rapidité d\'exécution',
                'jugement' => 'Jugement',
                'esprit_motivation' => 'Esprit de motivation',
                'esprit_collaboration' => 'Esprit de collaboration',
                'sens_responsabilite' => 'Sens des responsabilités',
                'communication' => 'Communication',
            ];
        } else if ($this->formatEvaluationUniversite) {
            // Intitulés personnalisés pour stages académiques
            return $this->formatEvaluationUniversite->criteres ?? [];
        }

        return [];
    }
}
