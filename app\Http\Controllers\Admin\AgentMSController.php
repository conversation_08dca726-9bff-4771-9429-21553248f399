<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class AgentMSController extends Controller
{
    /**
     * Affiche la liste des agents MS (consultation seulement pour l'admin)
     */
    public function index()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // CONSULTATION SEULEMENT : L'admin peut voir tous les agents MS mais ne peut pas les modifier
        $agents = Agent::with([
            'user',
            'creator',
            'structuresResponsable', // Sous-structures dont il est responsable
            'structure.parent.parent.parent' // Pour remonter jusqu'à la structure de direction générale
        ])
            ->where('role_agent', 'MS')
            ->get();

        return Inertia::render('Admin/AgentsMS/Index', [
            'agents' => $agents,
            'notifications' => Auth::user()->notifications()->latest()->take(20)->get() // APPROCHE CHIRURGICALE : Ajout notifications
        ]);
    }

    /**
     * Affiche les détails d'un agent MS (consultation seulement)
     */
    public function show(Agent $agent)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Vérifier que c'est bien un agent MS
        if ($agent->role_agent !== 'MS') {
            return redirect()->route('admin.agents-ms.index')
                ->with('error', 'Cet agent n\'est pas un Maître de Stage.');
        }

        // Charger les relations nécessaires
        $agent->load([
            'user',
            'creator',
            'structuresResponsable', // Sous-structures dont il est responsable
            'structure.parent.parent.parent' // Pour remonter jusqu'à la structure de direction générale
        ]);

        // Calculer la structure de direction générale
        $structureDirectionGenerale = null;
        if ($agent->structure) {
            $currentStructure = $agent->structure;
            // Remonter jusqu'à la structure racine (Direction Générale)
            while ($currentStructure->parent) {
                $currentStructure = $currentStructure->parent;
            }
            $structureDirectionGenerale = $currentStructure;
        }

        // Vérifier si l'agent est responsable de sa sous-structure
        $estResponsableSousStructure = $agent->structuresResponsable->contains('id', $agent->structure_id);

        return Inertia::render('Admin/AgentsMS/Show', [
            'agent' => $agent,
            'structureDirectionGenerale' => $structureDirectionGenerale,
            'estResponsableSousStructure' => $estResponsableSousStructure,
        ]);
    }
}
