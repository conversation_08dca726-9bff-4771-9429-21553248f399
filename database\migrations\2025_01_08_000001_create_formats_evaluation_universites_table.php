<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('formats_evaluation_universites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('universite_id')->constrained('universites')->onDelete('cascade');
            $table->integer('nombre_criteres'); // 10, 5, ou 2
            $table->integer('points_par_critere'); // 2, 4, ou 10
            $table->json('criteres'); // Intitulés des critères définis par le MS
            $table->foreignId('cree_par_agent_id')->constrained('agents')->onDelete('cascade');
            $table->boolean('actif')->default(true); // Seul le format actif est utilisé
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['universite_id', 'actif']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('formats_evaluation_universites');
    }
};
