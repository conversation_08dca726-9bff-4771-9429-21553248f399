<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migration consolidée pour la table demande_stages avec toutes les colonnes nécessaires
     */
    public function up(): void
    {
        Schema::create('demande_stages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stagiaire_id')->constrained('stagiaires', 'id_stagiaire')->onDelete('cascade');
            $table->foreignId('structure_id')->nullable()->constrained('structures')->onDelete('set null');
            $table->enum('nature', ['Individuel', 'Groupe'])->default('Individuel');
            $table->enum('type', ['Académique', 'Professionnel'])->default('Académique');
            $table->string('statut')->default('En attente');
            $table->text('motif_refus')->nullable();
            $table->timestamp('date_soumission')->nullable();
            $table->timestamp('date_traitement')->nullable();
            $table->foreignId('traite_par')->nullable()->constrained('agents')->onDelete('set null');
            
            // Documents
            $table->string('lettre_cv_path')->nullable();
            $table->string('lettre_motivation_path')->nullable();
            $table->string('releve_notes_path')->nullable();
            $table->string('convention_stage_path')->nullable();
            $table->string('assurance_path')->nullable();
            $table->string('diplomes_path')->nullable();
            $table->string('visage_path')->nullable(); // Photo du stagiaire
            
            // Informations de stage
            $table->string('code_suivi')->nullable()->unique();
            $table->integer('structure_souhaitee')->nullable(); // ID de la structure souhaitée
            $table->date('date_debut')->nullable();
            $table->date('date_fin')->nullable();
            $table->foreignId('agent_id')->nullable()->constrained('agents')->onDelete('set null');
            
            $table->timestamps();
        });

        // Remplir la colonne code_suivi avec des valeurs uniques pour les enregistrements existants
        \App\Models\DemandeStage::all()->each(function ($demande) {
            if (!$demande->code_suivi) {
                $demande->code_suivi = \Illuminate\Support\Str::uuid();
                $demande->save();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('demande_stages');
    }
};
