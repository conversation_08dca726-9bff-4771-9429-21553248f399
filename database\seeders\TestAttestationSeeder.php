<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Agent;
use App\Models\Stagiaire;
use App\Models\Structure;
use App\Models\DemandeStage;
use App\Models\Stage;
use App\Models\AffectationMaitreStage;
use App\Models\Evaluation;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class TestAttestationSeeder extends Seeder
{
    /**
     * Créer des données de test pour le workflow d'attestations
     */
    public function run(): void
    {
        // 1. Créer une structure de test
        $structure = Structure::firstOrCreate([
            'libelle' => 'Direction des Systèmes d\'Information',
            'sigle' => 'DSI',
            'description' => 'Structure de test pour les attestations',
            'adresse' => 'Cotonou, Bénin'
        ]);

        // 2. Créer un responsable de structure (RS)
        $userRS = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'nom' => 'RESPONSABLE',
            'prenom' => 'Structure',
            'telephone' => '97000001',
            'password' => Hash::make('password'),
            'role' => 'Agent',
            'email_verified_at' => now()
        ]);

        $agentRS = Agent::firstOrCreate([
            'user_id' => $userRS->id
        ], [
            'role_agent' => 'RS',
            'structure_id' => $structure->id
        ]);

        // Assigner le responsable à la structure
        $structure->update(['responsable_id' => $agentRS->id]);

        // 3. Créer un maître de stage (MS)
        $userMS = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'nom' => 'MAITRE',
            'prenom' => 'Stage',
            'telephone' => '97000002',
            'password' => Hash::make('password'),
            'role' => 'Agent',
            'email_verified_at' => now()
        ]);

        $agentMS = Agent::firstOrCreate([
            'user_id' => $userMS->id
        ], [
            'role_agent' => 'MS',
            'structure_id' => $structure->id
        ]);

        // 4. Créer des stagiaires de test
        $stagiaires = [];
        
        // Stagiaire 1 - Stage terminé
        $userStagiaire1 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'nom' => 'STAGIAIRE',
            'prenom' => 'Un',
            'telephone' => '97000003',
            'password' => Hash::make('password'),
            'role' => 'stagiaire',
            'email_verified_at' => now()
        ]);

        $stagiaire1 = Stagiaire::firstOrCreate([
            'user_id' => $userStagiaire1->id
        ], [
            'niveau_etude' => 'Master',
            'filiere' => 'Informatique',
            'universite' => 'Université d\'Abomey-Calavi'
        ]);

        // Stagiaire 2 - Stage en cours
        $userStagiaire2 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'nom' => 'STAGIAIRE',
            'prenom' => 'Deux',
            'telephone' => '97000004',
            'password' => Hash::make('password'),
            'role' => 'stagiaire',
            'email_verified_at' => now()
        ]);

        $stagiaire2 = Stagiaire::firstOrCreate([
            'user_id' => $userStagiaire2->id
        ], [
            'niveau_etude' => 'Licence',
            'filiere' => 'Gestion',
            'universite' => 'Université de Parakou'
        ]);

        // 5. Créer des demandes de stage
        $demande1 = DemandeStage::firstOrCreate([
            'stagiaire_id' => $stagiaire1->id_stagiaire,
            'structure_id' => $structure->id
        ], [
            'date_debut' => Carbon::now()->subMonths(3),
            'date_fin' => Carbon::now()->subDays(10), // Stage terminé il y a 10 jours
            'type' => 'professionnel',
            'nature' => 'individuel',
            'code_suivi' => 'TEST001',
            'statut' => 'Acceptée',
            'date_soumission' => Carbon::now()->subMonths(4),
            'date_traitement' => Carbon::now()->subMonths(3)
        ]);

        $demande2 = DemandeStage::firstOrCreate([
            'stagiaire_id' => $stagiaire2->id_stagiaire,
            'structure_id' => $structure->id
        ], [
            'date_debut' => Carbon::now()->subMonth(),
            'date_fin' => Carbon::now()->addDays(10), // Stage en cours
            'type' => 'academique',
            'nature' => 'individuel',
            'code_suivi' => 'TEST002',
            'statut' => 'Acceptée',
            'date_soumission' => Carbon::now()->subMonths(2),
            'date_traitement' => Carbon::now()->subMonth()
        ]);

        // 6. Créer les stages
        $stage1 = Stage::firstOrCreate([
            'demande_stage_id' => $demande1->id,
            'stagiaire_id' => $stagiaire1->id_stagiaire
        ], [
            'structure_id' => $structure->id,
            'date_debut' => $demande1->date_debut,
            'date_fin' => $demande1->date_fin,
            'statut' => 'Terminé',
            'type' => 'Professionnelle',
            'termine_par_ms' => true,
            'date_confirmation_ms' => Carbon::now()->subDays(8)
        ]);

        $stage2 = Stage::firstOrCreate([
            'demande_stage_id' => $demande2->id,
            'stagiaire_id' => $stagiaire2->id_stagiaire
        ], [
            'structure_id' => $structure->id,
            'date_debut' => $demande2->date_debut,
            'date_fin' => $demande2->date_fin,
            'statut' => 'En cours',
            'type' => 'Académique',
            'termine_par_ms' => false
        ]);

        // 7. Créer les affectations de maître de stage
        AffectationMaitreStage::firstOrCreate([
            'stage_id' => $stage1->id,
            'maitre_stage_id' => $userMS->id
        ], [
            'agent_affectant_id' => $agentRS->id,
            'date_affectation' => Carbon::now()->subMonths(3),
            'statut' => 'Acceptée'
        ]);

        AffectationMaitreStage::firstOrCreate([
            'stage_id' => $stage2->id,
            'maitre_stage_id' => $userMS->id
        ], [
            'agent_affectant_id' => $agentRS->id,
            'date_affectation' => Carbon::now()->subMonth(),
            'statut' => 'Acceptée'
        ]);

        // 8. Créer une évaluation pour le stage terminé
        Evaluation::firstOrCreate([
            'stage_id' => $stage1->id,
            'agent_id' => $agentMS->id
        ], [
            'ponctualite' => 2,
            'motivation' => 2,
            'capacite_apprendre' => 2,
            'qualite_travail' => 2,
            'rapidite_execution' => 2,
            'jugement' => 2,
            'esprit_motivation' => 2,
            'esprit_collaboration' => 2,
            'sens_responsabilite' => 2,
            'communication' => 2,
            'note_totale' => 20,
            'commentaire_general' => 'Excellent stagiaire, très motivé et compétent.',
            'date_evaluation' => Carbon::now()->subDays(7)
        ]);

        $this->command->info('✅ Données de test créées avec succès !');
        $this->command->info("📧 Comptes créés :");
        $this->command->info("   - RS: <EMAIL> / password");
        $this->command->info("   - MS: <EMAIL> / password");
        $this->command->info("   - Stagiaire 1: <EMAIL> / password (stage terminé)");
        $this->command->info("   - Stagiaire 2: <EMAIL> / password (stage en cours)");
        $this->command->info("🏢 Structure: {$structure->libelle} ({$structure->sigle})");
        $this->command->info("📋 Stages créés: {$stage1->id} (terminé), {$stage2->id} (en cours)");
    }
}
