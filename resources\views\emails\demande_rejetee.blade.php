<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Réponse à votre demande de stage</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); color: #2c3e50; margin: 0; padding: 20px; line-height: 1.6;">
    <div style="max-width: 650px; margin: 20px auto; background: #ffffff; border-radius: 16px; box-shadow: 0 8px 32px rgba(220, 38, 38, 0.12); overflow: hidden; border: 1px solid rgba(220, 38, 38, 0.1);">

        <!-- Header Section -->
        <div style="background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); padding: 40px 24px; text-align: center; position: relative;">
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #f87171, #fca5a5, #f87171);"></div>
            <img src="{{ asset('images/logoministere.png') }}" alt="Logo Ministère" style="height: 70px; margin-bottom: 16px; filter: brightness(1.1);">
            <h1 style="color: #ffffff; margin: 0; font-size: 1.75rem; font-weight: 600; letter-spacing: -0.5px;">Gestion des Stages</h1>
            <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 0.95rem;">Réponse à votre demande</p>
        </div>


            <!-- Refusal Message -->
            <div style="border-left: 4px solid #dc2626; padding-left: 20px; margin-bottom: 32px;">
                <h2 style="color: #b91c1c; margin: 0 0 16px 0; font-size: 1.5rem; font-weight: 600;">
                    ❌ Demande de stage refusée
                </h2>
                <p style="margin: 0; font-size: 1.1rem; color: #4a5568;">
                    Bonjour <strong style="color: #b91c1c;">{{ $stagiaire->user->prenom }} {{ $stagiaire->user->nom }}</strong>,
                </p>
                <p style="margin: 12px 0 0 0; color: #6b7280;">
                    Nous vous remercions pour l'intérêt que vous portez au Ministère de l'Économie et des Finances du Bénin. Après examen, votre demande de stage <span style="color: #dc2626; font-weight: 600; background: #fef2f2; padding: 2px 8px; border-radius: 12px;">n'a pas pu être acceptée</span>.
                </p>
            </div>

            <!-- Request Details -->
            <div style="background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%); border: 1px solid #fecaca; border-radius: 12px; padding: 24px; margin: 24px 0;">
                <h3 style="color: #b91c1c; margin: 0 0 20px 0; font-size: 1.25rem; font-weight: 600; display: flex; align-items: center;">
                    <span style="background: #dc2626; color: white; border-radius: 50%; width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 0.875rem;">📋</span>
                    Détails de votre demande
                </h3>
                <div style="display: grid; gap: 12px;">
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #b91c1c; font-weight: 600; min-width: 140px;">📅 Période :</span>
                        <span style="color: #374151;">du {{ \Carbon\Carbon::parse($demande->date_debut)->format('d/m/Y') }} au {{ \Carbon\Carbon::parse($demande->date_fin)->format('d/m/Y') }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #b91c1c; font-weight: 600; min-width: 140px;">📝 Type :</span>
                        <span style="color: #374151;">Stage {{ $demande->type }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #b91c1c; font-weight: 600; min-width: 140px;">👥 Nature :</span>
                        <span style="color: #374151;">{{ $demande->nature }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #b91c1c; font-weight: 600; min-width: 140px;">🔍 Code de suivi :</span>
                        <span style="color: #374151; font-family: monospace; background: #f3f4f6; padding: 4px 8px; border-radius: 6px;">{{ $demande->code_suivi }}</span>
                    </div>
                    <div style="display: flex; align-items: center; padding: 8px 0;">
                        <span style="color: #b91c1c; font-weight: 600; min-width: 140px;">📅 Date de soumission :</span>
                        <span style="color: #374151;">{{ \Carbon\Carbon::parse($demande->created_at)->format('d/m/Y') }}</span>
                    </div>
                </div>
            </div>

            @if($motifRejet)
            <!-- Rejection Reason -->
            <div style="background: #fff1f2; border: 2px solid #fecaca; border-radius: 12px; padding: 24px; margin: 24px 0;">
                <h3 style="color: #b91c1c; margin: 0 0 16px 0; font-size: 1.1rem; font-weight: 600; display: flex; align-items: center;">
                    <span style="background: #dc2626; color: white; border-radius: 50%; width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 0.875rem;">📝</span>
                    Motif du rejet
                </h3>
                <div style="background: #ffffff; border: 1px solid #f3f4f6; border-radius: 8px; padding: 16px;">
                    <p style="margin: 0; color: #7f1d1d; font-style: italic; line-height: 1.6;">{{ $motifRejet }}</p>
                </div>
            </div>
            @endif

            <!-- Recommendations -->
            <div style="background: #f0f9ff; border: 1px solid #bfdbfe; border-radius: 12px; padding: 20px; margin: 24px 0;">
                <h3 style="color: #1d4ed8; margin: 0 0 16px 0; font-size: 1.1rem; font-weight: 600;">💡 Recommandations</h3>
                <p style="margin: 0 0 12px 0; color: #1e40af;">Nous vous encourageons à :</p>
                <ul style="margin: 0; padding-left: 20px; color: #3730a3;">
                    <li style="margin-bottom: 8px;">Revoir les critères d'éligibilité sur notre plateforme</li>
                    <li style="margin-bottom: 8px;">Soumettre une nouvelle demande lors des prochaines sessions</li>
                    <li style="margin-bottom: 8px;">Vous rapprocher de votre établissement pour des conseils</li>
                    <li style="margin-bottom: 8px;">Consulter les autres opportunités de stage disponibles</li>
                </ul>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 32px 0;">
                <a href="{{ route('stagiaire.dashboard') }}" style="display: inline-block; background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: #ffffff; text-decoration: none; padding: 16px 32px; border-radius: 12px; font-weight: 600; font-size: 1rem; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); transition: transform 0.2s;">
                    📊 Accéder à mon espace stagiaire
                </a>
            </div>

            <!-- Closing Message -->
            <div style="background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%); border-left: 4px solid #6b7280; padding: 20px; margin: 24px 0; border-radius: 0 12px 12px 0;">
                <p style="margin: 0; color: #374151; font-weight: 600; font-size: 1.1rem;">
                    Nous vous remercions pour votre compréhension et vous souhaitons bonne chance dans vos démarches futures.
                </p>
                <p style="margin: 8px 0 0 0; color: #6b7280; font-size: 0.95rem;">
                    En cas de questions concernant cette décision, n'hésitez pas à nous contacter.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%); padding: 24px 32px; border-top: 1px solid #e5e7eb; text-align: center;">
            <p style="margin: 0 0 8px 0; color: #6b7280; font-size: 0.875rem;">
                Cet email a été envoyé automatiquement par le système de gestion des stages.
            </p>
            <p style="margin: 0; color: #374151; font-weight: 600; font-size: 0.95rem;">
                Ministère de l'Économie et des Finances du Bénin
            </p>
            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 0.875rem;">
                Direction du Placement et de l'Affectation des Fonctionnaires (DPAF)
            </p>
        </div>
    </div>
</body>
</html>
