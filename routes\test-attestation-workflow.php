<?php

use Illuminate\Support\Facades\Route;
use App\Models\Stage;
use App\Models\Evaluation;
use App\Models\DemandeStage;
use App\Models\Stagiaire;
use App\Models\User;
use App\Models\Agent;
use App\Models\Structure;
use App\Models\AffectationMaitreStage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

/**
 * Routes de test pour le workflow d'attestations
 * Ces routes permettent de tester les fonctionnalités existantes avant d'implémenter les nouvelles
 */

Route::prefix('test-attestation')->group(function () {
    
    /**
     * Test 1: Vérifier les fonctionnalités d'évaluation existantes
     */
    Route::get('/test-evaluation', function () {
        try {
            $results = [];
            
            // 1. Vérifier les stages en cours avec date de fin dépassée
            $stagesTerminables = Stage::where('statut', 'En cours')
                ->where('date_fin', '<=', now())
                ->where('termine_par_ms', false)
                ->with(['stagiaire.user', 'structure', 'demandeStage'])
                ->get();
            
            $results['stages_terminables'] = [
                'count' => $stagesTerminables->count(),
                'stages' => $stagesTerminables->map(function($stage) {
                    return [
                        'id' => $stage->id,
                        'stagiaire' => $stage->stagiaire->user->nom . ' ' . $stage->stagiaire->user->prenom,
                        'structure' => $stage->structure->libelle,
                        'date_fin' => $stage->date_fin->format('d/m/Y'),
                        'type' => $stage->type,
                        'statut' => $stage->statut,
                        'termine_par_ms' => $stage->termine_par_ms
                    ];
                })
            ];
            
            // 2. Vérifier les stages déjà terminés par MS
            $stagesTermines = Stage::where('termine_par_ms', true)
                ->with(['stagiaire.user', 'structure', 'evaluation'])
                ->get();
            
            $results['stages_termines'] = [
                'count' => $stagesTermines->count(),
                'stages' => $stagesTermines->map(function($stage) {
                    return [
                        'id' => $stage->id,
                        'stagiaire' => $stage->stagiaire->user->nom . ' ' . $stage->stagiaire->user->prenom,
                        'structure' => $stage->structure->libelle,
                        'date_confirmation_ms' => $stage->date_confirmation_ms ? $stage->date_confirmation_ms->format('d/m/Y H:i') : null,
                        'a_evaluation' => $stage->evaluation ? true : false,
                        'note_evaluation' => $stage->evaluation ? $stage->evaluation->note_totale : null
                    ];
                })
            ];
            
            // 3. Vérifier les évaluations existantes
            $evaluations = Evaluation::with(['stage.stagiaire.user', 'stage.structure', 'agent.user'])
                ->get();
            
            $results['evaluations'] = [
                'count' => $evaluations->count(),
                'evaluations' => $evaluations->map(function($eval) {
                    return [
                        'id' => $eval->id,
                        'stage_id' => $eval->stage_id,
                        'stagiaire' => $eval->stage->stagiaire->user->nom . ' ' . $eval->stage->stagiaire->user->prenom,
                        'evaluateur' => $eval->agent->user->nom . ' ' . $eval->agent->user->prenom,
                        'note_totale' => $eval->note_totale,
                        'date_evaluation' => $eval->date_evaluation ? $eval->date_evaluation->format('d/m/Y H:i') : null,
                        'stage_termine' => $eval->stage->termine_par_ms
                    ];
                })
            ];
            
            return response()->json($results, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    });
    
    /**
     * Test 2: Tester le marquage de fin de stage
     */
    Route::post('/test-marquer-termine/{stage}', function (Stage $stage) {
        try {
            // Vérifier les conditions
            if ($stage->statut !== 'En cours') {
                return response()->json([
                    'success' => false,
                    'message' => 'Le stage doit être en cours pour être terminé.',
                    'stage_statut' => $stage->statut
                ]);
            }
            
            if (now()->lt($stage->date_fin)) {
                return response()->json([
                    'success' => false,
                    'message' => 'La date de fin du stage n\'est pas encore atteinte.',
                    'date_fin' => $stage->date_fin->format('d/m/Y'),
                    'date_actuelle' => now()->format('d/m/Y')
                ]);
            }
            
            // Simuler le marquage (sans vraiment modifier)
            return response()->json([
                'success' => true,
                'message' => 'Le stage peut être marqué comme terminé',
                'stage_id' => $stage->id,
                'stagiaire' => $stage->stagiaire->user->nom . ' ' . $stage->stagiaire->user->prenom,
                'date_fin' => $stage->date_fin->format('d/m/Y'),
                'action_requise' => 'Appeler confirmerFinStage pour marquer réellement comme terminé'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    });
    
    /**
     * Test 3: Tester la génération d'attestation d'effectivité
     */
    Route::get('/test-attestation-effectivite/{stage}', function (Stage $stage) {
        try {
            // Vérifier que le stage est terminé par MS
            if (!$stage->termine_par_ms) {
                return response()->json([
                    'success' => false,
                    'message' => 'Le stage doit être marqué comme terminé par le MS pour générer l\'attestation',
                    'termine_par_ms' => $stage->termine_par_ms
                ]);
            }
            
            // Charger les données nécessaires
            $stage->load(['stagiaire.user', 'structure.responsable.user']);
            
            $attestationData = [
                'stage_id' => $stage->id,
                'stagiaire' => [
                    'nom' => $stage->stagiaire->user->nom,
                    'prenom' => $stage->stagiaire->user->prenom,
                    'email' => $stage->stagiaire->user->email
                ],
                'structure' => [
                    'libelle' => $stage->structure->libelle,
                    'sigle' => $stage->structure->sigle,
                    'responsable' => $stage->structure->responsable ? 
                        $stage->structure->responsable->user->nom . ' ' . $stage->structure->responsable->user->prenom : 
                        'Non défini'
                ],
                'dates' => [
                    'debut' => $stage->date_debut->format('d/m/Y'),
                    'fin' => $stage->date_fin->format('d/m/Y'),
                    'confirmation_ms' => $stage->date_confirmation_ms ? $stage->date_confirmation_ms->format('d/m/Y H:i') : null
                ],
                'url_attestation' => route('agent.rs.stages.attestation', $stage->id)
            ];
            
            return response()->json([
                'success' => true,
                'message' => 'Attestation d\'effectivité peut être générée',
                'data' => $attestationData
            ], 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    });
    
    /**
     * Test 4: Analyser le workflow complet
     */
    Route::get('/test-workflow-complet', function () {
        try {
            $workflow = [];
            
            // Étape 1: Stages en cours
            $stagesEnCours = Stage::where('statut', 'En cours')->count();
            $workflow['etape_1_stages_en_cours'] = $stagesEnCours;
            
            // Étape 2: Stages terminables (date dépassée)
            $stagesTerminables = Stage::where('statut', 'En cours')
                ->where('date_fin', '<=', now())
                ->where('termine_par_ms', false)
                ->count();
            $workflow['etape_2_stages_terminables'] = $stagesTerminables;
            
            // Étape 3: Stages terminés par MS
            $stagesTerminesMS = Stage::where('termine_par_ms', true)->count();
            $workflow['etape_3_stages_termines_ms'] = $stagesTerminesMS;
            
            // Étape 4: Stages avec évaluation
            $stagesAvecEvaluation = Stage::whereHas('evaluation')->count();
            $workflow['etape_4_stages_avec_evaluation'] = $stagesAvecEvaluation;
            
            // Étape 5: Attestations générables (terminés par MS)
            $attestationsGenerables = Stage::where('termine_par_ms', true)->count();
            $workflow['etape_5_attestations_generables'] = $attestationsGenerables;
            
            // Statistiques par type
            $statsByType = Stage::select('type', DB::raw('count(*) as total'))
                ->groupBy('type')
                ->get();
            $workflow['stats_par_type'] = $statsByType;
            
            // Statistiques par statut
            $statsByStatus = Stage::select('statut', DB::raw('count(*) as total'))
                ->groupBy('statut')
                ->get();
            $workflow['stats_par_statut'] = $statsByStatus;
            
            return response()->json($workflow, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 500);
        }
    });

    /**
     * Test 5: Créer des données de test rapidement
     */
    Route::get('/create-test-data', function () {
        try {
            // Créer un utilisateur admin temporaire pour les tests
            $adminUser = User::firstOrCreate([
                'email' => '<EMAIL>'
            ], [
                'nom' => 'ADMIN',
                'prenom' => 'Test',
                'telephone' => '97000000',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'email_verified_at' => now()
            ]);

            // Créer une structure simple
            $structure = Structure::firstOrCreate([
                'libelle' => 'Direction des Systèmes d\'Information'
            ], [
                'sigle' => 'DSI',
                'description' => 'Direction des Systèmes d\'Information du MEF'
            ]);

            // Créer un responsable de structure
            $userRS = User::firstOrCreate([
                'email' => '<EMAIL>'
            ], [
                'nom' => 'AHOUNOU',
                'prenom' => 'Alain',
                'sexe' => 'Homme',
                'telephone' => '97000001',
                'password' => Hash::make('password'),
                'role' => 'Agent',
                'email_verified_at' => now()
            ]);

            $agentRS = Agent::firstOrCreate([
                'user_id' => $userRS->id
            ], [
                'role_agent' => 'RS',
                'structure_id' => $structure->id
            ]);

            // Assigner le responsable à la structure
            $structure->update(['responsable_id' => $agentRS->id]);

            // Créer un stage de test simple
            $userStagiaire = User::firstOrCreate([
                'email' => '<EMAIL>'
            ], [
                'nom' => 'SOKE',
                'prenom' => 'Joseph',
                'sexe' => 'Homme',
                'telephone' => '97000099',
                'password' => Hash::make('password'),
                'role' => 'stagiaire',
                'email_verified_at' => now()
            ]);

            $stagiaire = Stagiaire::firstOrCreate([
                'user_id' => $userStagiaire->id
            ], [
                'niveau_etude' => '3ème année de Licence Professionnelle',
                'filiere' => 'Génie Électrique et Informatique',
                'universite' => 'Institut National Supérieur de Technologie Industrielle (INSTI) de Lokossa'
            ]);

            $demande = DemandeStage::firstOrCreate([
                'stagiaire_id' => $stagiaire->id_stagiaire,
                'structure_id' => $structure->id
            ], [
                'date_debut' => Carbon::parse('2025-03-03'),
                'date_fin' => Carbon::parse('2025-05-30'),
                'type' => 'academique',
                'nature' => 'individuel',
                'code_suivi' => 'TESTDATA001',
                'statut' => 'Acceptée',
                'date_soumission' => now()->subDays(70),
                'date_traitement' => now()->subDays(65)
            ]);

            $stage = Stage::firstOrCreate([
                'demande_stage_id' => $demande->id,
                'stagiaire_id' => $stagiaire->id_stagiaire
            ], [
                'structure_id' => $structure->id,
                'date_debut' => $demande->date_debut,
                'date_fin' => $demande->date_fin,
                'statut' => 'Terminé',
                'type' => 'Académique',
                'termine_par_ms' => true,
                'date_confirmation_ms' => now()->subDays(8)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Données de test créées',
                'data' => [
                    'admin' => $adminUser->email,
                    'stagiaire' => $userStagiaire->email,
                    'stage_id' => $stage->id,
                    'structure' => $structure->libelle,
                    'attestation_url' => route('agent.rs.stages.attestation', $stage->id)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    });

    /**
     * Test 6: Tester l'attestation existante
     */
    Route::get('/test-attestation-view/{stage?}', function ($stageId = null) {
        try {
            // Si pas d'ID fourni, prendre le premier stage terminé
            if (!$stageId) {
                $stage = Stage::where('termine_par_ms', true)->first();
                if (!$stage) {
                    return response()->json([
                        'error' => 'Aucun stage terminé trouvé. Utilisez /test-attestation/create-test-data d\'abord.'
                    ]);
                }
            } else {
                $stage = Stage::find($stageId);
                if (!$stage) {
                    return response()->json(['error' => 'Stage non trouvé']);
                }
            }

            // Charger les données nécessaires
            $stage->load(['stagiaire.user', 'structure.responsable.user']);
            $demande = $stage->demandeStage;
            $stagiairePrincipal = $demande ? $demande->stagiaire : $stage->stagiaire;
            $structure = $stage->structure;
            $responsable = $structure && $structure->responsable ? $structure->responsable : null;
            $nom_rs = $responsable ? ($responsable->user->nom . ' ' . $responsable->user->prenom) : 'Responsable Non Défini';
            $libelle_structure = $structure ? $structure->libelle : 'Structure Non Définie';
            $sigle_structure = $structure ? $structure->sigle : '';

            // Retourner la vue d'attestation
            return view('attestation', [
                'stage' => $stage,
                'stagiaire' => $stage->stagiaire,
                'structure' => $structure,
                'stagiaire_principal' => $stagiairePrincipal,
                'nom_rs' => $nom_rs,
                'libelle_structure' => $libelle_structure,
                'sigle_structure' => $sigle_structure,
                'numero_attestation' => 'TEST-' . str_pad($stage->id, 4, '0', STR_PAD_LEFT)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    });

    /**
     * Test 7: Tester la nouvelle attestation professionnelle conforme au modèle officiel
     */
    Route::get('/test-nouvelle-attestation/{stage?}', function ($stageId = null) {
        try {
            // Si pas d'ID fourni, prendre le premier stage terminé
            if (!$stageId) {
                $stage = Stage::where('termine_par_ms', true)->first();
                if (!$stage) {
                    return response()->json([
                        'error' => 'Aucun stage terminé trouvé. Utilisez /test-attestation/create-test-data d\'abord.'
                    ]);
                }
            } else {
                $stage = Stage::find($stageId);
                if (!$stage) {
                    return response()->json(['error' => 'Stage non trouvé']);
                }
            }

            // Simuler l'appel au contrôleur RS avec la nouvelle logique
            $controller = new \App\Http\Controllers\Agent\RS\StageController();
            return $controller->attestation($stage);

        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    });

    /**
     * Test 8: Créer des données complètes pour tous les cas de validation (DGI, DSI, DPAF)
     */
    Route::get('/create-validation-data', function () {
        try {
            $results = [];
            $stagesCreated = [];

            // Créer les 3 structures : DGI, DSI, DPAF
            $structures = [
                [
                    'libelle' => 'Direction Générale des Impôts',
                    'sigle' => 'DGI',
                    'responsable_nom' => 'HOUNGBO',
                    'responsable_prenom' => 'Marcel'
                ],
                [
                    'libelle' => 'Direction des Systèmes d\'Information',
                    'sigle' => 'DSI',
                    'responsable_nom' => 'AHOUNOU',
                    'responsable_prenom' => 'Alain'
                ],
                [
                    'libelle' => 'Direction de la Programmation et de l\'Administration Financière',
                    'sigle' => 'DPAF',
                    'responsable_nom' => 'KPOSSOU',
                    'responsable_prenom' => 'Bernadette'
                ]
            ];

            foreach ($structures as $structureData) {
                // Créer la structure
                $structure = Structure::firstOrCreate([
                    'libelle' => $structureData['libelle']
                ], [
                    'sigle' => $structureData['sigle'],
                    'description' => $structureData['libelle'] . ' du MEF'
                ]);

                // Créer le responsable de structure
                $userRS = User::firstOrCreate([
                    'email' => strtolower($structureData['responsable_prenom'] . '.' . $structureData['responsable_nom']) . '@mef.bj'
                ], [
                    'nom' => $structureData['responsable_nom'],
                    'prenom' => $structureData['responsable_prenom'],
                    'sexe' => 'Homme',
                    'telephone' => '97' . rand(100000, 999999),
                    'password' => Hash::make('password'),
                    'role' => 'Agent',
                    'email_verified_at' => now()
                ]);

                $agentRS = Agent::firstOrCreate([
                    'user_id' => $userRS->id
                ], [
                    'role_agent' => 'RS',
                    'structure_id' => $structure->id
                ]);

                // Assigner le responsable à la structure
                $structure->update(['responsable_id' => $agentRS->id]);

                // Créer les 4 cas de test pour chaque structure
                $casTests = [
                    [
                        'nom' => 'SOKE',
                        'prenom' => 'Joseph',
                        'sexe' => 'Homme',
                        'type_stage' => 'Académique',
                        'niveau_etude' => '3ème année de Licence Professionnelle',
                        'filiere' => 'Génie Électrique et Informatique',
                        'universite' => 'Institut National Supérieur de Technologie Industrielle (INSTI) de Lokossa'
                    ],
                    [
                        'nom' => 'ADJOVI',
                        'prenom' => 'Marie',
                        'sexe' => 'Femme',
                        'type_stage' => 'Académique',
                        'niveau_etude' => '2ème année de Master',
                        'filiere' => 'Comptabilité et Gestion',
                        'universite' => 'Université d\'Abomey-Calavi (UAC)'
                    ],
                    [
                        'nom' => 'KOUDJO',
                        'prenom' => 'Pierre',
                        'sexe' => 'Homme',
                        'type_stage' => 'Professionnelle',
                        'niveau_etude' => 'Master',
                        'filiere' => 'Finance et Banque',
                        'universite' => 'Université de Parakou'
                    ],
                    [
                        'nom' => 'AGBESSI',
                        'prenom' => 'Sylvie',
                        'sexe' => 'Femme',
                        'type_stage' => 'Professionnelle',
                        'niveau_etude' => 'Licence',
                        'filiere' => 'Administration Publique',
                        'universite' => 'École Nationale d\'Administration et de Magistrature (ENAM)'
                    ]
                ];

                foreach ($casTests as $index => $casTest) {
                    // Créer l'utilisateur stagiaire
                    $userStagiaire = User::firstOrCreate([
                        'email' => strtolower($casTest['prenom'] . '.' . $casTest['nom'] . '.' . $structureData['sigle']) . '@test.com'
                    ], [
                        'nom' => $casTest['nom'],
                        'prenom' => $casTest['prenom'],
                        'sexe' => $casTest['sexe'],
                        'telephone' => '97' . rand(100000, 999999),
                        'password' => Hash::make('password'),
                        'role' => 'stagiaire',
                        'email_verified_at' => now()
                    ]);

                    // Créer le stagiaire
                    $stagiaire = Stagiaire::firstOrCreate([
                        'user_id' => $userStagiaire->id
                    ], [
                        'niveau_etude' => $casTest['niveau_etude'],
                        'filiere' => $casTest['filiere'],
                        'universite' => $casTest['universite']
                    ]);

                    // Créer la demande de stage
                    $demande = DemandeStage::firstOrCreate([
                        'stagiaire_id' => $stagiaire->id_stagiaire,
                        'structure_id' => $structure->id,
                        'code_suivi' => 'TEST' . $structureData['sigle'] . str_pad($index + 1, 3, '0', STR_PAD_LEFT)
                    ], [
                        'date_debut' => Carbon::parse('2025-03-03'),
                        'date_fin' => Carbon::parse('2025-05-30'),
                        'type' => strtolower($casTest['type_stage']),
                        'nature' => 'individuel',
                        'statut' => 'Acceptée',
                        'date_soumission' => now()->subDays(70),
                        'date_traitement' => now()->subDays(65)
                    ]);

                    // Créer le stage
                    $stage = Stage::firstOrCreate([
                        'demande_stage_id' => $demande->id,
                        'stagiaire_id' => $stagiaire->id_stagiaire
                    ], [
                        'structure_id' => $structure->id,
                        'date_debut' => $demande->date_debut,
                        'date_fin' => $demande->date_fin,
                        'statut' => 'Terminé',
                        'type' => $casTest['type_stage'],
                        'termine_par_ms' => true,
                        'date_confirmation_ms' => now()->subDays(8)
                    ]);

                    $stagesCreated[] = [
                        'stage_id' => $stage->id,
                        'stagiaire' => $casTest['prenom'] . ' ' . $casTest['nom'],
                        'sexe' => $casTest['sexe'],
                        'type_stage' => $casTest['type_stage'],
                        'structure' => $structureData['sigle'],
                        'email' => $userStagiaire->email,
                        'attestation_url' => '/test-attestation/test-nouvelle-attestation/' . $stage->id
                    ];
                }
            }

            $results['message'] = 'Données complètes créées avec succès pour tous les cas de test';
            $results['structures_creees'] = ['DGI', 'DSI', 'DPAF'];
            $results['stages_crees'] = $stagesCreated;
            $results['total_stages'] = count($stagesCreated);

            return response()->json($results, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    });
});
