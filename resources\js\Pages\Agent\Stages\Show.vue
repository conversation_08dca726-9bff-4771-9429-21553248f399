<template>
  <AgentDPAFLayout>
    <template #header>
      <div class="flex items-center gap-4 mb-2">
        <div class="bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-xl w-16 h-16 flex items-center justify-center shadow-lg transform transition-transform hover:scale-105">
          <ClipboardDocumentIcon class="w-8 h-8" />
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800 leading-tight md:text-3xl tracking-tight">Détails du Stage</h1>
          <p class="text-sm text-gray-600 mt-1 font-medium">
            Vue détaillée du stage et informations complètes
          </p>
        </div>
      </div>
    </template>

    <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Bouton retour -->
        <div class="mb-6">
          <Link
            :href="route('agent.stages')"
            class="inline-flex items-center gap-2 px-4 py-2 bg-white/80 text-gray-700 rounded-xl hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 font-medium"
          >
            <ArrowLeftIcon class="h-4 w-4" />
            Retour à la liste
          </Link>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          <!-- Informations principales -->
          <div class="lg:col-span-2 space-y-6">
            
            <!-- Informations du stagiaire -->
            <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
              <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white">
                <h2 class="text-xl font-bold flex items-center gap-3">
                  <UserIcon class="h-6 w-6" />
                  Informations du Stagiaire
                </h2>
              </div>
              
              <div class="p-6">
                <div v-if="stage.demande_stage?.stagiaire?.user" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Nom complet</label>
                      <p class="text-lg font-bold text-gray-900">
                        {{ stage.demande_stage.stagiaire.user.nom }} {{ stage.demande_stage.stagiaire.user.prenom }}
                      </p>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Email</label>
                      <p class="text-gray-800">{{ stage.demande_stage.stagiaire.user.email }}</p>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Téléphone</label>
                      <p class="text-gray-800">{{ stage.demande_stage.stagiaire.user.telephone || 'Non renseigné' }}</p>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <div v-if="stage.demande_stage.type === 'Académique'">
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Université</label>
                      <p class="text-gray-800">{{ stage.demande_stage.stagiaire.universite || 'Non renseigné' }}</p>
                    </div>
                    <div v-if="stage.demande_stage.type === 'Académique'">
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Filière</label>
                      <p class="text-gray-800">{{ stage.demande_stage.stagiaire.filiere || 'Non renseigné' }}</p>
                    </div>
                    <div v-if="stage.demande_stage.type === 'Académique'">
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Niveau d'étude</label>
                      <p class="text-gray-800">{{ stage.demande_stage.stagiaire.niveau_etude || 'Non renseigné' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Informations du stage -->
            <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
              <div class="bg-gradient-to-r from-green-600 to-emerald-600 p-6 text-white">
                <h2 class="text-xl font-bold flex items-center gap-3">
                  <ClipboardDocumentIcon class="h-6 w-6" />
                  Détails du Stage
                </h2>
              </div>
              
              <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Structure d'accueil</label>
                      <p class="text-lg font-bold text-gray-900">{{ stage.structure?.sigle }}</p>
                      <p class="text-sm text-gray-600">{{ stage.structure?.libelle }}</p>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Type de demande</label>
                      <span :class="getTypeClass(stage.demande_stage?.type)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                        {{ stage.demande_stage?.type }}
                      </span>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Statut</label>
                      <span :class="getStatusClass(stage.statut)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium">
                        {{ stage.statut }}
                      </span>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Type de stage</label>
                      <span :class="getTypeStageClass(stage.type)" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path v-if="stage.type && stage.type.toLowerCase().includes('académique')" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                          <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"></path>
                        </svg>
                        {{ stage.type || 'Non défini' }}
                      </span>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Demande de stage</label>
                      <button
                        v-if="stage.demande_stage_id"
                        @click="voirDemande"
                        class="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-indigo-500 to-blue-600 text-white rounded-lg font-semibold text-sm hover:from-indigo-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Voir la demande
                      </button>
                      <p v-else class="text-gray-500 italic text-sm">Demande non disponible</p>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Date de début</label>
                      <p class="text-gray-800 flex items-center gap-2">
                        <CalendarIcon class="h-4 w-4 text-gray-400" />
                        {{ formatDate(stage.date_debut) }}
                      </p>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Date de fin</label>
                      <p class="text-gray-800 flex items-center gap-2">
                        <CalendarIcon class="h-4 w-4 text-gray-400" />
                        {{ formatDate(stage.date_fin) }}
                      </p>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Durée</label>
                      <p class="text-gray-800">{{ calculateDuration(stage.date_debut, stage.date_fin) }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Affectation maître de stage -->
            <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
              <div class="bg-gradient-to-r from-purple-600 to-violet-600 p-6 text-white">
                <h2 class="text-xl font-bold flex items-center gap-3">
                  <UserGroupIcon class="h-6 w-6" />
                  Maître de Stage
                </h2>
              </div>
              
              <div class="p-6">
                <div v-if="stage.affectation_recente?.maitre_stage" class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Nom complet</label>
                      <p class="text-lg font-bold text-gray-900">
                        {{ stage.affectation_recente.maitre_stage.nom }} {{ stage.affectation_recente.maitre_stage.prenom }}
                      </p>
                    </div>
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Email</label>
                      <p class="text-gray-800">{{ stage.affectation_recente.maitre_stage.email }}</p>
                    </div>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Date d'affectation</label>
                      <p class="text-gray-800">{{ formatDate(stage.affectation_recente.date_affectation) }}</p>
                    </div>
                    <div v-if="stage.affectation_recente.agent_affectant">
                      <label class="text-sm font-semibold text-gray-700 block mb-1">Affecté par</label>
                      <p class="text-gray-800">
                        {{ stage.affectation_recente.agent_affectant.nom }} {{ stage.affectation_recente.agent_affectant.prenom }}
                      </p>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8">
                  <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <UserGroupIcon class="w-8 h-8 text-gray-400" />
                  </div>
                  <p class="text-gray-600 font-medium">Aucun maître de stage affecté</p>
                  <p class="text-gray-400 text-sm">Le stage n'a pas encore de maître de stage assigné</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar avec actions et informations complémentaires -->
          <div class="space-y-6">
            
            <!-- Actions rapides -->
            <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
              <div class="bg-gradient-to-r from-orange-600 to-red-600 p-6 text-white">
                <h3 class="text-lg font-bold flex items-center gap-3">
                  <CogIcon class="h-5 w-5" />
                  Actions
                </h3>
              </div>
              
              <div class="p-6 space-y-3">
                <Link
                  :href="route('agent.demandes.show', stage.demande_stage?.id)"
                  v-if="stage.demande_stage?.id"
                  class="w-full inline-flex items-center justify-center gap-2 px-4 py-3 bg-blue-50 text-blue-700 rounded-xl hover:bg-blue-100 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 font-medium"
                >
                  <DocumentTextIcon class="h-4 w-4" />
                  Voir la demande
                </Link>
              </div>
            </div>

            <!-- Informations système -->
            <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
              <div class="bg-gradient-to-r from-gray-600 to-slate-600 p-6 text-white">
                <h3 class="text-lg font-bold flex items-center gap-3">
                  <InformationCircleIcon class="h-5 w-5" />
                  Informations
                </h3>
              </div>
              
              <div class="p-6 space-y-4">
                <div>
                  <label class="text-sm font-semibold text-gray-700 block mb-1">ID du stage</label>
                  <p class="text-gray-800 font-mono text-sm">#{{ stage.id }}</p>
                </div>
                <div v-if="stage.demande_stage?.code">
                  <label class="text-sm font-semibold text-gray-700 block mb-1">Code de demande</label>
                  <p class="text-gray-800 font-mono text-sm">{{ stage.demande_stage.code }}</p>
                </div>
                <div>
                  <label class="text-sm font-semibold text-gray-700 block mb-1">Créé le</label>
                  <p class="text-gray-800 text-sm">{{ formatDateTime(stage.created_at) }}</p>
                </div>
                <div>
                  <label class="text-sm font-semibold text-gray-700 block mb-1">Modifié le</label>
                  <p class="text-gray-800 text-sm">{{ formatDateTime(stage.updated_at) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AgentDPAFLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3';
import AgentDPAFLayout from '@/Layouts/AgentDPAF.vue';
import { 
  ClipboardDocumentIcon,
  UserIcon,
  UserGroupIcon,
  CalendarIcon,
  ArrowLeftIcon,
  CogIcon,
  DocumentTextIcon,
  InformationCircleIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  stage: Object,
});

// Formater une date
function formatDate(date) {
  return new Date(date).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// Formater une date avec heure
function formatDateTime(datetime) {
  return new Date(datetime).toLocaleString('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

// Calculer la durée du stage
function calculateDuration(dateDebut, dateFin) {
  const debut = new Date(dateDebut);
  const fin = new Date(dateFin);
  const diffTime = Math.abs(fin - debut);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 7) {
    return `${diffDays} jour${diffDays > 1 ? 's' : ''}`;
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks} semaine${weeks > 1 ? 's' : ''}`;
  } else {
    const months = Math.floor(diffDays / 30);
    return `${months} mois`;
  }
}

// Obtenir la classe CSS pour le statut
function getStatusClass(status) {
  const classes = {
    'En attente': 'bg-yellow-100 text-yellow-800',
    'En cours': 'bg-blue-100 text-blue-800',
    'Terminé': 'bg-green-100 text-green-800',
    'Annulé': 'bg-red-100 text-red-800',
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
}

// Obtenir la classe CSS pour le type
function getTypeClass(type) {
  const classes = {
    'Académique': 'bg-purple-100 text-purple-800',
    'Professionnel': 'bg-indigo-100 text-indigo-800',
  };
  return classes[type] || 'bg-gray-100 text-gray-800';
}

// Obtenir la classe CSS pour le type de stage
function getTypeStageClass(typeStage) {
  if (!typeStage) return 'bg-gray-100 text-gray-800';

  const type = typeStage.toLowerCase();
  if (type.includes('académique') || type.includes('academique')) {
    return 'bg-emerald-100 text-emerald-800';
  } else if (type.includes('professionnel')) {
    return 'bg-blue-100 text-blue-800';
  }

  return 'bg-gray-100 text-gray-800';
}

// Fonction pour voir la demande de stage
function voirDemande() {
  if (props.stage?.demande_stage_id) {
    // Rediriger vers la page de détails de la demande
    window.open(route('agent.demandes.show', props.stage.demande_stage_id), '_blank');
  }
}
</script>
