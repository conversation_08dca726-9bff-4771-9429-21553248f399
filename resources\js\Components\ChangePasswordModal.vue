<template>
    <teleport to="body">
        <div
            v-if="show"
            class="fixed inset-0 z-50 overflow-y-auto"
            aria-labelledby="modal-title"
            role="dialog"
            aria-modal="true"
        >
            <!-- Overlay -->
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div
                    class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                    aria-hidden="true"
                    @click="closeModal"
                ></div>

                <!-- Modal -->
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <form @submit.prevent="submitForm">
                        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div class="sm:flex sm:items-start">
                                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                                    <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                        Changer le mot de passe
                                    </h3>
                                    <div class="mt-4 space-y-4">
                                        <!-- Ancien mot de passe -->
                                        <div>
                                            <label for="current_password" class="block text-sm font-medium text-gray-700">
                                                Mot de passe actuel
                                            </label>
                                            <input
                                                id="current_password"
                                                v-model="form.current_password"
                                                type="password"
                                                required
                                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                                :class="{ 'border-red-300': form.errors.current_password }"
                                            />
                                            <p v-if="form.errors.current_password" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.current_password }}
                                            </p>
                                        </div>

                                        <!-- Nouveau mot de passe -->
                                        <div>
                                            <label for="password" class="block text-sm font-medium text-gray-700">
                                                Nouveau mot de passe
                                            </label>
                                            <input
                                                id="password"
                                                v-model="form.password"
                                                type="password"
                                                required
                                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                                :class="{ 'border-red-300': form.errors.password }"
                                            />
                                            <p v-if="form.errors.password" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.password }}
                                            </p>
                                        </div>

                                        <!-- Confirmation du nouveau mot de passe -->
                                        <div>
                                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                                                Confirmer le nouveau mot de passe
                                            </label>
                                            <input
                                                id="password_confirmation"
                                                v-model="form.password_confirmation"
                                                type="password"
                                                required
                                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                                :class="{ 'border-red-300': form.errors.password_confirmation }"
                                            />
                                            <p v-if="form.errors.password_confirmation" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.password_confirmation }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                            <button
                                type="submit"
                                :disabled="form.processing"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <svg v-if="form.processing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ form.processing ? 'Modification...' : 'Modifier le mot de passe' }}
                            </button>
                            <button
                                type="button"
                                @click="closeModal"
                                :disabled="form.processing"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Annuler
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </teleport>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import { useSuccessMessage } from '@/Composables/useSuccessMessage'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close'])

const { showPasswordChanged } = useSuccessMessage()

const form = useForm({
    current_password: '',
    password: '',
    password_confirmation: ''
})

const submitForm = () => {
    form.put(route('password.update'), {
        onSuccess: () => {
            showPasswordChanged()
            closeModal()
        },
        onError: () => {
            // Les erreurs sont automatiquement gérées par Inertia
        }
    })
}

const closeModal = () => {
    form.reset()
    form.clearErrors()
    emit('close')
}
</script>
