<template>
    <teleport to="body">
        <div
            v-if="show"
            class="fixed inset-0 z-50 overflow-y-auto"
            aria-labelledby="modal-title"
            role="dialog"
            aria-modal="true"
        >
            <!-- Overlay -->
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div
                    class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                    aria-hidden="true"
                    @click="closeModal"
                ></div>

                <!-- Modal -->
                <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-blue-100">
                    <form @submit.prevent="submitForm">
                        <div class="bg-white px-6 pt-6 pb-4 sm:p-8 sm:pb-6">
                            <div class="sm:flex sm:items-start">
                                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg sm:mx-0 sm:h-12 sm:w-12">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                    <h3 class="text-xl leading-6 font-semibold text-gray-900" id="modal-title">
                                        Changer le mot de passe
                                    </h3>
                                    <p class="mt-2 text-sm text-gray-600">
                                        Saisissez votre mot de passe actuel et choisissez un nouveau mot de passe sécurisé.
                                    </p>
                                    <div class="mt-6 space-y-6">
                                        <!-- Ancien mot de passe -->
                                        <div>
                                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                                                Mot de passe actuel
                                            </label>
                                            <input
                                                id="current_password"
                                                v-model="form.current_password"
                                                type="password"
                                                required
                                                class="block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                                :class="{ 'border-red-500 bg-red-50': form.errors.current_password }"
                                                placeholder="Saisissez votre mot de passe actuel"
                                            />
                                            <p v-if="form.errors.current_password" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.current_password }}
                                            </p>
                                        </div>

                                        <!-- Nouveau mot de passe -->
                                        <div>
                                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                                Nouveau mot de passe
                                            </label>
                                            <input
                                                id="password"
                                                v-model="form.password"
                                                type="password"
                                                required
                                                class="block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                                :class="{ 'border-red-500 bg-red-50': form.errors.password }"
                                                placeholder="Saisissez votre nouveau mot de passe"
                                            />
                                            <p v-if="form.errors.password" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.password }}
                                            </p>
                                        </div>

                                        <!-- Confirmation du nouveau mot de passe -->
                                        <div>
                                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                                                Confirmer le nouveau mot de passe
                                            </label>
                                            <input
                                                id="password_confirmation"
                                                v-model="form.password_confirmation"
                                                type="password"
                                                required
                                                class="block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white"
                                                :class="{ 'border-red-500 bg-red-50': form.errors.password_confirmation }"
                                                placeholder="Confirmez votre nouveau mot de passe"
                                            />
                                            <p v-if="form.errors.password_confirmation" class="mt-1 text-sm text-red-600">
                                                {{ form.errors.password_confirmation }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-6 py-4 sm:px-8 sm:flex sm:flex-row-reverse border-t border-gray-200">
                            <button
                                type="submit"
                                :disabled="form.processing"
                                class="w-full inline-flex justify-center items-center rounded-xl border border-transparent shadow-lg px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-base font-medium text-white hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-xl"
                            >
                                <svg v-if="form.processing" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ form.processing ? 'Modification...' : 'Modifier le mot de passe' }}
                            </button>
                            <button
                                type="button"
                                @click="closeModal"
                                :disabled="form.processing"
                                class="mt-3 w-full inline-flex justify-center items-center rounded-xl border border-gray-300 shadow-sm px-6 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:mr-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                            >
                                Annuler
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </teleport>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import { useSuccessMessage } from '@/Composables/useSuccessMessage'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['close'])

const { showPasswordChanged } = useSuccessMessage()

const form = useForm({
    current_password: '',
    password: '',
    password_confirmation: ''
})

const submitForm = () => {
    form.put(route('password.update'), {
        onSuccess: () => {
            showPasswordChanged()
            closeModal()
        },
        onError: () => {
            // Les erreurs sont automatiquement gérées par Inertia
        }
    })
}

const closeModal = () => {
    form.reset()
    form.clearErrors()
    emit('close')
}
</script>
