<template>
  <Head title="Organigramme" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header professionnel -->
        <div class="flex items-center gap-4 mb-8">
          <div class="p-3 bg-indigo-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-slate-800">Organigramme Général</h1>
            <p class="text-slate-600 mt-1">Vue hiérarchique de toutes les structures et sous-structures</p>
          </div>
        </div>

        <!-- Statistiques -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-indigo-100 rounded-xl">
                <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Structures DG</p>
                <p class="text-2xl font-bold text-slate-800">{{ structures.length }}</p>
              </div>
            </div>
          </div>
          
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-blue-100 rounded-xl">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Sous-structures</p>
                <p class="text-2xl font-bold text-slate-800">{{ totalSousStructures }}</p>
              </div>
            </div>
          </div>
          
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-green-100 rounded-xl">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Agents RS</p>
                <p class="text-2xl font-bold text-slate-800">{{ agentsRS }}</p>
              </div>
            </div>
          </div>
          
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-purple-100 rounded-xl">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Agents MS</p>
                <p class="text-2xl font-bold text-slate-800">{{ agentsMS }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Filtres -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6 mb-8">
          <h3 class="text-lg font-bold text-slate-800 mb-4">Filtres</h3>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-semibold text-slate-700 mb-2">Type de structure</label>
              <select v-model="filters.type" class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="all">Toutes les structures</option>
                <option value="Direction">Directions</option>
                <option value="Service">Services</option>
                <option value="Département">Départements</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-semibold text-slate-700 mb-2">Statut responsable</label>
              <select v-model="filters.responsable" class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                <option value="all">Tous</option>
                <option value="assigned">Avec responsable</option>
                <option value="unassigned">Sans responsable</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-semibold text-slate-700 mb-2">Recherche</label>
              <input 
                v-model="filters.search" 
                type="text" 
                placeholder="Rechercher une structure..."
                class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
            </div>
            
            <div class="flex items-end gap-2">
              <button @click="resetFilters" class="px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-lg font-semibold transition-colors">
                Réinitialiser
              </button>
              <button @click="expandAll" class="px-4 py-2 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded-lg font-semibold transition-colors">
                Tout déplier
              </button>
            </div>
          </div>
        </div>

        <!-- Organigramme hiérarchique -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
            <div class="flex items-center justify-between">
              <div>
                <h2 class="text-xl font-bold text-slate-800">Organigramme Hiérarchique</h2>
                <p class="text-sm text-slate-600 mt-1">{{ filteredStructures.length }} structure(s) affichée(s)</p>
              </div>
              <button @click="printOrganigramme" class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg font-semibold transition-colors">
                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"/>
                </svg>
                Imprimer
              </button>
            </div>
          </div>

          <!-- État vide -->
          <div v-if="filteredStructures.length === 0" class="text-center py-16">
            <div class="w-20 h-20 mx-auto bg-slate-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-slate-800 mb-2">Aucune structure trouvée</h3>
            <p class="text-slate-600">Aucune structure ne correspond aux critères de filtrage.</p>
          </div>

          <!-- Arbre hiérarchique -->
          <div v-else class="p-6">
            <div class="space-y-4">
              <StructureNode 
                v-for="structure in filteredStructures" 
                :key="structure.id"
                :structure="structure"
                :agents="agents"
                :expanded="expandedNodes"
                @toggle="toggleNode"
                :level="0"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import StructureNode from '@/Components/StructureNode.vue';
import { ref, computed, onMounted } from 'vue';

// Définir le composant StructureNode comme composant récursif
const components = {
  StructureNode
};

const props = defineProps({
  structures: Array,
  agents: Array,
});

// État des filtres
const filters = ref({
  type: 'all',
  responsable: 'all',
  search: ''
});

// État des nœuds expandus
const expandedNodes = ref(new Set());

// Computed properties pour les statistiques
const totalSousStructures = computed(() => {
  let count = 0;
  const countChildren = (structures) => {
    structures.forEach(structure => {
      if (structure.children && structure.children.length > 0) {
        count += structure.children.length;
        countChildren(structure.children);
      }
    });
  };
  countChildren(props.structures);
  return count;
});

const agentsRS = computed(() => {
  return props.agents.filter(agent => agent.role_agent === 'RS').length;
});

const agentsMS = computed(() => {
  return props.agents.filter(agent => agent.role_agent === 'MS').length;
});

// Filtrage des structures
const filteredStructures = computed(() => {
  let filtered = [...props.structures];

  // Filtrage par type
  if (filters.value.type !== 'all') {
    filtered = filtered.filter(structure => 
      structure.type_structure === filters.value.type
    );
  }

  // Filtrage par responsable
  if (filters.value.responsable !== 'all') {
    if (filters.value.responsable === 'assigned') {
      filtered = filtered.filter(structure => structure.responsable);
    } else if (filters.value.responsable === 'unassigned') {
      filtered = filtered.filter(structure => !structure.responsable);
    }
  }

  // Filtrage par recherche
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase();
    filtered = filtered.filter(structure => 
      structure.sigle.toLowerCase().includes(searchTerm) ||
      structure.libelle.toLowerCase().includes(searchTerm)
    );
  }

  return filtered;
});

// Méthodes
const resetFilters = () => {
  filters.value = {
    type: 'all',
    responsable: 'all',
    search: ''
  };
};

const expandAll = () => {
  const getAllIds = (structures) => {
    let ids = [];
    structures.forEach(structure => {
      ids.push(structure.id);
      if (structure.children && structure.children.length > 0) {
        ids = ids.concat(getAllIds(structure.children));
      }
    });
    return ids;
  };
  
  expandedNodes.value = new Set(getAllIds(props.structures));
};

const toggleNode = (nodeId) => {
  if (expandedNodes.value.has(nodeId)) {
    expandedNodes.value.delete(nodeId);
  } else {
    expandedNodes.value.add(nodeId);
  }
};

const printOrganigramme = () => {
  window.print();
};

// Initialisation
onMounted(() => {
  // Expand first level by default
  props.structures.forEach(structure => {
    expandedNodes.value.add(structure.id);
  });
});
</script>
