<template>
    <Head title="Rapports des Stages" />
    
    <AdminLayout>
        <template #header>
            <div class="flex items-center text-sm text-gray-500">
                <span class="mr-4 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Rapports et Statistiques
                </span>
                <button 
                    @click="exporterCsv" 
                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs rounded 
                          text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 
                          focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                    :disabled="exportEnCours"
                >
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {{ exportEnCours ? 'Export...' : 'Exporter CSV' }}
                </button>
            </div>
        </template>

        <!-- Page Title -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                Rapports des Stages
            </h1>
            <p class="text-gray-600 mt-2">
                Analyse détaillée des performances et statistiques des stages
            </p>
        </div>

        <!-- Filtres de période -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <div class="flex flex-col sm:flex-row sm:items-end gap-4">
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date de début</label>
                    <input 
                        v-model="filtres.date_debut" 
                        type="date" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date de fin</label>
                    <input 
                        v-model="filtres.date_fin" 
                        type="date" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
                <button 
                    @click="appliquerFiltres" 
                    class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                    :disabled="chargement"
                >
                    {{ chargement ? 'Chargement...' : 'Appliquer' }}
                </button>
            </div>
        </div>

        <!-- Message d'erreur -->
        <div v-if="error" class="mb-6 rounded-lg bg-red-50 p-4 border border-red-200">
            <div class="flex">
                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Erreur</h3>
                    <p class="text-sm text-red-700 mt-1">{{ error }}</p>
                </div>
            </div>
        </div>

        <!-- Statistiques générales -->
        <div v-if="statistiques" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Demandes -->
            <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-600 text-sm font-medium">Demandes de stage</p>
                        <p class="text-2xl font-bold text-blue-900">{{ statistiques.demandes.total }}</p>
                        <p class="text-xs text-blue-700 mt-1">
                            {{ statistiques.demandes.taux_acceptation }}% acceptées
                        </p>
                    </div>
                    <div class="bg-blue-600 rounded-lg p-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex justify-between text-xs">
                    <span class="text-green-600">✓ {{ statistiques.demandes.acceptees }} acceptées</span>
                    <span class="text-red-600">✗ {{ statistiques.demandes.refusees }} refusées</span>
                    <span class="text-yellow-600">⏳ {{ statistiques.demandes.en_attente }} en attente</span>
                </div>
            </div>

            <!-- Stages -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-600 text-sm font-medium">Stages actifs</p>
                        <p class="text-2xl font-bold text-green-900">{{ statistiques.stages.total }}</p>
                        <p class="text-xs text-green-700 mt-1">
                            {{ statistiques.stages.taux_completion }}% terminés
                        </p>
                    </div>
                    <div class="bg-green-600 rounded-lg p-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2-2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex justify-between text-xs">
                    <span class="text-blue-600">🔄 {{ statistiques.stages.en_cours }} en cours</span>
                    <span class="text-green-600">✅ {{ statistiques.stages.termines }} terminés</span>
                </div>
            </div>

            <!-- Stagiaires -->
            <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-600 text-sm font-medium">Stagiaires</p>
                        <p class="text-2xl font-bold text-purple-900">{{ statistiques.stagiaires.total }}</p>
                        <p class="text-xs text-purple-700 mt-1">Total actifs</p>
                    </div>
                    <div class="bg-purple-600 rounded-lg p-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Taux de réussite -->
            <div v-if="tauxReussite" class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-6 border border-orange-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-orange-600 text-sm font-medium">Note moyenne</p>
                        <p class="text-2xl font-bold text-orange-900">{{ tauxReussite.note_moyenne }}/20</p>
                        <p class="text-xs text-orange-700 mt-1">{{ tauxReussite.total_evaluations }} évaluations</p>
                    </div>
                    <div class="bg-orange-600 rounded-lg p-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphiques et tableaux -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Répartition par structure -->
            <div v-if="repartitionStructures && repartitionStructures.length > 0" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Répartition par structure</h3>
                <div class="space-y-3">
                    <div v-for="structure in repartitionStructures" :key="structure.sigle" class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">{{ structure.sigle }}</p>
                            <p class="text-xs text-gray-500">{{ structure.libelle }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                {{ structure.nombre_stages }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top maîtres de stage -->
            <div v-if="statistiquesMaitres && statistiquesMaitres.length > 0" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Top maîtres de stage</h3>
                <div class="space-y-3">
                    <div v-for="maitre in statistiquesMaitres" :key="maitre.email" class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">{{ maitre.nom }} {{ maitre.prenom }}</p>
                            <p class="text-xs text-gray-500">{{ maitre.email }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                {{ maitre.nombre_stages }} stages
                            </div>
                            <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                {{ Math.round(maitre.taux_completion) }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'

const props = defineProps({
    statistiques: Object,
    repartitionStructures: Array,
    evolutionMensuelle: Object,
    statistiquesMaitres: Array,
    tauxReussite: Object,
    filtres: Object,
    error: String
})

const chargement = ref(false)
const exportEnCours = ref(false)

const filtres = ref({
    date_debut: props.filtres?.date_debut || '',
    date_fin: props.filtres?.date_fin || ''
})

const appliquerFiltres = () => {
    chargement.value = true
    router.get(route('admin.rapports.stages'), filtres.value, {
        preserveState: true,
        onFinish: () => {
            chargement.value = false
        }
    })
}

const exporterCsv = () => {
    exportEnCours.value = true
    window.location.href = route('admin.rapports.stages.export', filtres.value)
    setTimeout(() => {
        exportEnCours.value = false
    }, 2000)
}
</script>
