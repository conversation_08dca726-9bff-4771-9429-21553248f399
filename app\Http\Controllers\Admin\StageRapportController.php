<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Stage;
use App\Models\DemandeStage;
use App\Models\Structure;
use App\Models\Stagiaire;
use App\Models\AffectationMaitreStage;
use App\Models\Evaluation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Carbon\Carbon;

class StageRapportController extends Controller
{
    /**
     * Afficher le tableau de bord des rapports de stages
     */
    public function index(Request $request)
    {
        try {
            // Période par défaut : 12 derniers mois
            $dateDebut = $request->get('date_debut', Carbon::now()->subYear()->format('Y-m-d'));
            $dateFin = $request->get('date_fin', Carbon::now()->format('Y-m-d'));
            
            // Statistiques générales
            $statistiques = $this->getStatistiquesGenerales($dateDebut, $dateFin);
            
            // Répartition par structure
            $repartitionStructures = $this->getRepartitionParStructure($dateDebut, $dateFin);
            
            // Évolution mensuelle
            $evolutionMensuelle = $this->getEvolutionMensuelle($dateDebut, $dateFin);
            
            // Statistiques des maîtres de stage
            $statistiquesMaitres = $this->getStatistiquesMaitres($dateDebut, $dateFin);
            
            // Taux de réussite
            $tauxReussite = $this->getTauxReussite($dateDebut, $dateFin);
            
            return Inertia::render('Admin/Rapports/Stages', [
                'statistiques' => $statistiques,
                'repartitionStructures' => $repartitionStructures,
                'evolutionMensuelle' => $evolutionMensuelle,
                'statistiquesMaitres' => $statistiquesMaitres,
                'tauxReussite' => $tauxReussite,
                'filtres' => [
                    'date_debut' => $dateDebut,
                    'date_fin' => $dateFin
                ],
                'notifications' => Auth::user()->notifications()->latest()->take(20)->get() // APPROCHE CHIRURGICALE : Ajout notifications
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erreur lors de la génération des rapports de stages', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return Inertia::render('Admin/Rapports/Stages', [
                'error' => 'Erreur lors de la génération des rapports'
            ]);
        }
    }
    
    /**
     * Obtenir les statistiques générales
     */
    private function getStatistiquesGenerales($dateDebut, $dateFin)
    {
        $totalDemandes = DemandeStage::whereBetween('date_soumission', [$dateDebut, $dateFin])->count();
        $demandesAcceptees = DemandeStage::whereBetween('date_soumission', [$dateDebut, $dateFin])
            ->where('statut', 'Acceptée')->count();
        $demandesRefusees = DemandeStage::whereBetween('date_soumission', [$dateDebut, $dateFin])
            ->where('statut', 'Refusée')->count();
        $demandesEnAttente = DemandeStage::whereBetween('date_soumission', [$dateDebut, $dateFin])
            ->where('statut', 'En attente')->count();
            
        $totalStages = Stage::whereBetween('date_debut', [$dateDebut, $dateFin])->count();
        $stagesEnCours = Stage::whereBetween('date_debut', [$dateDebut, $dateFin])
            ->where('statut', 'En cours')->count();
        $stagesTermines = Stage::whereBetween('date_debut', [$dateDebut, $dateFin])
            ->where('statut', 'Terminé')->count();
            
        $totalStagiaires = Stagiaire::whereHas('demandesStages', function($query) use ($dateDebut, $dateFin) {
            $query->whereBetween('date_soumission', [$dateDebut, $dateFin]);
        })->count();
        
        return [
            'demandes' => [
                'total' => $totalDemandes,
                'acceptees' => $demandesAcceptees,
                'refusees' => $demandesRefusees,
                'en_attente' => $demandesEnAttente,
                'taux_acceptation' => $totalDemandes > 0 ? round(($demandesAcceptees / $totalDemandes) * 100, 1) : 0
            ],
            'stages' => [
                'total' => $totalStages,
                'en_cours' => $stagesEnCours,
                'termines' => $stagesTermines,
                'taux_completion' => $totalStages > 0 ? round(($stagesTermines / $totalStages) * 100, 1) : 0
            ],
            'stagiaires' => [
                'total' => $totalStagiaires
            ]
        ];
    }
    
    /**
     * Obtenir la répartition par structure
     */
    private function getRepartitionParStructure($dateDebut, $dateFin)
    {
        return Structure::select('structures.sigle', 'structures.libelle')
            ->selectRaw('COUNT(stages.id) as nombre_stages')
            ->leftJoin('stages', 'structures.id', '=', 'stages.structure_id')
            ->whereBetween('stages.date_debut', [$dateDebut, $dateFin])
            ->groupBy('structures.id', 'structures.sigle', 'structures.libelle')
            ->orderByDesc('nombre_stages')
            ->limit(10)
            ->get();
    }
    
    /**
     * Obtenir l'évolution mensuelle
     */
    private function getEvolutionMensuelle($dateDebut, $dateFin)
    {
        $demandes = DemandeStage::selectRaw('DATE_FORMAT(date_soumission, "%Y-%m") as mois')
            ->selectRaw('COUNT(*) as nombre')
            ->whereBetween('date_soumission', [$dateDebut, $dateFin])
            ->groupBy('mois')
            ->orderBy('mois')
            ->get();
            
        $stages = Stage::selectRaw('DATE_FORMAT(date_debut, "%Y-%m") as mois')
            ->selectRaw('COUNT(*) as nombre')
            ->whereBetween('date_debut', [$dateDebut, $dateFin])
            ->groupBy('mois')
            ->orderBy('mois')
            ->get();
            
        return [
            'demandes' => $demandes,
            'stages' => $stages
        ];
    }
    
    /**
     * Obtenir les statistiques des maîtres de stage
     */
    private function getStatistiquesMaitres($dateDebut, $dateFin)
    {
        return DB::table('users')
            ->join('agents', 'users.id', '=', 'agents.user_id')
            ->join('affectation_maitre_stages', 'users.id', '=', 'affectation_maitre_stages.maitre_stage_id')
            ->join('stages', 'affectation_maitre_stages.stage_id', '=', 'stages.id')
            ->select('users.nom', 'users.prenom', 'users.email')
            ->selectRaw('COUNT(stages.id) as nombre_stages')
            ->selectRaw('AVG(CASE WHEN stages.statut = "Terminé" THEN 1 ELSE 0 END) * 100 as taux_completion')
            ->where('agents.role_agent', 'MS')
            ->whereBetween('stages.date_debut', [$dateDebut, $dateFin])
            ->groupBy('users.id', 'users.nom', 'users.prenom', 'users.email')
            ->orderByDesc('nombre_stages')
            ->limit(10)
            ->get();
    }
    
    /**
     * Obtenir les taux de réussite
     */
    private function getTauxReussite($dateDebut, $dateFin)
    {
        $evaluations = Evaluation::join('stages', 'evaluations.stage_id', '=', 'stages.id')
            ->whereBetween('stages.date_debut', [$dateDebut, $dateFin])
            ->select('note_finale')
            ->whereNotNull('note_finale')
            ->get();
            
        $totalEvaluations = $evaluations->count();
        $notesMoyennes = $evaluations->avg('note_finale');
        
        $repartitionNotes = [
            'excellent' => $evaluations->where('note_finale', '>=', 16)->count(),
            'bien' => $evaluations->whereBetween('note_finale', [14, 15.99])->count(),
            'assez_bien' => $evaluations->whereBetween('note_finale', [12, 13.99])->count(),
            'passable' => $evaluations->whereBetween('note_finale', [10, 11.99])->count(),
            'insuffisant' => $evaluations->where('note_finale', '<', 10)->count(),
        ];
        
        return [
            'total_evaluations' => $totalEvaluations,
            'note_moyenne' => $notesMoyennes ? round($notesMoyennes, 2) : 0,
            'repartition' => $repartitionNotes
        ];
    }
    
    /**
     * Exporter les données en CSV
     */
    public function exportCsv(Request $request)
    {
        $dateDebut = $request->get('date_debut', Carbon::now()->subYear()->format('Y-m-d'));
        $dateFin = $request->get('date_fin', Carbon::now()->format('Y-m-d'));
        
        $stages = Stage::with(['structure', 'stagiaire.user', 'demandeStage', 'evaluation'])
            ->whereBetween('date_debut', [$dateDebut, $dateFin])
            ->get();
            
        $filename = 'rapport_stages_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        
        $callback = function() use ($stages) {
            $file = fopen('php://output', 'w');
            
            // En-têtes CSV
            fputcsv($file, [
                'ID Stage', 'Stagiaire', 'Email', 'Structure', 'Date Début', 
                'Date Fin', 'Statut', 'Type', 'Note Finale', 'Date Création'
            ]);
            
            foreach ($stages as $stage) {
                fputcsv($file, [
                    $stage->id,
                    $stage->stagiaire ? $stage->stagiaire->user->nom . ' ' . $stage->stagiaire->user->prenom : 'N/A',
                    $stage->stagiaire ? $stage->stagiaire->user->email : 'N/A',
                    $stage->structure ? $stage->structure->libelle : 'N/A',
                    $stage->date_debut ? $stage->date_debut->format('d/m/Y') : 'N/A',
                    $stage->date_fin ? $stage->date_fin->format('d/m/Y') : 'N/A',
                    $stage->statut,
                    $stage->type,
                    $stage->evaluation ? $stage->evaluation->note_finale : 'N/A',
                    $stage->created_at->format('d/m/Y H:i')
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }
}
