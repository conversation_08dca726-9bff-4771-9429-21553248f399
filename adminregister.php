<?php

/**
 * Script pour créer deux utilisateurs administrateurs
 * À exécuter dans Tinker : php artisan tinker
 * Puis : include 'adminregister.php';
 */

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Carbon;

echo "=== Création des utilisateurs administrateurs ===\n\n";

// Données des administrateurs
$admins = [
    [
        'nom' => 'Admin',
        'prenom' => 'Un',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'date_de_naissance' => Carbon::create(1980, 1, 15),
        'sexe' => 'Homme',
        'adresse' => 'Adresse Admin 1, <PERSON><PERSON>ou',
        'telephone' => '+229 90 12 34 56',
    ],
    [
        'nom' => 'Admin',
        'prenom' => 'Deux',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'date_de_naissance' => Carbon::create(1985, 6, 20),
        'sexe' => 'Femme',
        'adresse' => 'Adresse Admin 2, Co<PERSON><PERSON>',
        'telephone' => '+229 97 65 43 21',
    ]
];

foreach ($admins as $adminData) {
    // Vérifier si l'utilisateur existe déjà
    $existingUser = User::where('email', $adminData['email'])->first();

    if ($existingUser) {
        echo "❌ L'utilisateur {$adminData['email']} existe déjà (ID: {$existingUser->id})\n";
        continue;
    }

    try {
        // Créer l'utilisateur admin
        $user = User::create([
            'nom' => $adminData['nom'],
            'prenom' => $adminData['prenom'],
            'date_de_naissance' => $adminData['date_de_naissance'],
            'sexe' => $adminData['sexe'],
            'adresse' => $adminData['adresse'],
            'email' => $adminData['email'],
            'password' => Hash::make($adminData['password']),
            'telephone' => $adminData['telephone'],
            'date_d_inscription' => now(),
            'role' => 'admin',
            'remember_token' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Assigner le rôle admin avec Spatie Permission (si le package est configuré)
        try {
            if (class_exists('Spatie\Permission\Models\Role')) {
                $adminRole = \Spatie\Permission\Models\Role::where('name', 'admin')->first();
                if ($adminRole) {
                    $user->assignRole('admin');
                    echo "✅ Rôle Spatie 'admin' assigné à {$adminData['email']}\n";
                }
            }
        } catch (Exception $e) {
            echo "⚠️  Erreur lors de l'assignation du rôle Spatie : " . $e->getMessage() . "\n";
        }

        echo "✅ Utilisateur admin créé avec succès :\n";
        echo "   - Email: {$adminData['email']}\n";
        echo "   - Mot de passe: {$adminData['password']}\n";
        echo "   - ID: {$user->id}\n";
        echo "   - Rôle: {$user->role}\n\n";

    } catch (Exception $e) {
        echo "❌ Erreur lors de la création de {$adminData['email']} : " . $e->getMessage() . "\n\n";
    }
}

echo "=== Vérification des utilisateurs admin créés ===\n";

$adminUsers = User::where('role', 'admin')->get();
echo "Nombre total d'administrateurs : " . $adminUsers->count() . "\n\n";

foreach ($adminUsers as $admin) {
    echo "👤 Admin ID {$admin->id} :\n";
    echo "   - Nom complet: {$admin->prenom} {$admin->nom}\n";
    echo "   - Email: {$admin->email}\n";
    echo "   - Rôle: {$admin->role}\n";
    echo "   - Date création: {$admin->created_at}\n";

    // Vérifier les rôles Spatie si disponibles
    try {
        if (method_exists($admin, 'getRoleNames')) {
            $spatieRoles = $admin->getRoleNames();
            echo "   - Rôles Spatie: " . $spatieRoles->implode(', ') . "\n";
        }
    } catch (Exception $e) {
        echo "   - Rôles Spatie: Non disponibles\n";
    }

    echo "\n";
}

echo "=== Script terminé ===\n";
echo "Les administrateurs peuvent maintenant se connecter avec :\n";
echo "- <EMAIL> / password123\n";
echo "- <EMAIL> / password123\n";