-- Script pour créer un utilisateur MS de test
-- À exécuter dans phpMyAdmin

-- 1. <PERSON><PERSON>er une structure de test si elle n'existe pas
INSERT IGNORE INTO structures (libelle, description, created_at, updated_at) 
VALUES ('Direction Test MS', 'Structure de test pour agent MS', NOW(), NOW());

-- 2. Récupérer l'ID de la structure
SET @structure_id = (SELECT id FROM structures WHERE libelle = 'Direction Test MS' LIMIT 1);

-- 3. <PERSON><PERSON>er l'utilisateur MS de test
INSERT IGNORE INTO users (
    nom, 
    prenom, 
    email, 
    telephone, 
    email_verified_at, 
    password, 
    role, 
    created_at, 
    updated_at
) VALUES (
    'Test', 
    'Agent MS', 
    '<EMAIL>', 
    '+229 12 34 56 78', 
    NOW(), 
    '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password = 'password'
    'Agent', 
    NOW(), 
    NOW()
);

-- 4. <PERSON><PERSON><PERSON><PERSON>rer l'ID de l'utilisateur créé
SET @user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- 5. Créer l'agent MS associé
INSERT IGNORE INTO agents (
    user_id, 
    structure_id, 
    role_agent, 
    created_at, 
    updated_at
) VALUES (
    @user_id, 
    @structure_id, 
    'MS', 
    NOW(), 
    NOW()
);

-- Vérification des données créées
SELECT 
    u.nom, 
    u.prenom, 
    u.email, 
    u.role as role_user,
    a.role_agent,
    s.libelle as structure
FROM users u
LEFT JOIN agents a ON u.id = a.user_id
LEFT JOIN structures s ON a.structure_id = s.id
WHERE u.email = '<EMAIL>';
