<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class ForceSessionExpiry
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Si l'utilisateur est authentifié
        if (Auth::check()) {
            $userId = Auth::id();
            $sessionId = Session::getId();

            // Vérifier si cette session existe dans la base de données
            $sessionExists = DB::table('sessions')
                ->where('id', $sessionId)
                ->where('user_id', $userId)
                ->exists();

            // Si la session n'existe pas en base, forcer la déconnexion
            if (!$sessionExists) {
                Auth::logout();
                Session::flush();
                Session::regenerate();

                return redirect()->route('login')->with('message', 'Votre session a expiré. Veuillez vous reconnecter.');
            }

            // Vérifier l'âge de la session
            $sessionData = DB::table('sessions')
                ->where('id', $sessionId)
                ->first();

            if ($sessionData) {
                $lastActivity = $sessionData->last_activity;
                $currentTime = time();

                // Si la session est inactive depuis plus de 30 minutes, la supprimer
                if (($currentTime - $lastActivity) > 1800) {
                    DB::table('sessions')->where('id', $sessionId)->delete();
                    Auth::logout();
                    Session::flush();
                    Session::regenerate();

                    return redirect()->route('login')->with('message', 'Votre session a expiré pour des raisons de sécurité.');
                }
            }
        }

        return $next($request);
    }
}
