<?php

namespace App\Http\Controllers\ResponsableUniversite;

use App\Http\Controllers\Controller;
use App\Models\Evaluation;
use App\Models\Agent;
use App\Models\Stage;
use App\Models\Stagiaire;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class EvaluationController extends Controller
{
    /**
     * Afficher la liste des évaluations académiques en attente de validation
     */
    public function index()
    {
        try {
            $user = Auth::user();
            
            // Vérifier que l'utilisateur est un agent RU
            if ($user->role !== 'agent' || !$user->agent || $user->agent->role_agent !== 'RU') {
                return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
            }

            $agent = $user->agent;
            
            // Récupérer l'université dont cet agent est responsable
            $universite = $agent->universiteResponsable;
            if (!$universite) {
                return redirect()->route('dashboard')->with('error', 'Aucune université assignée à ce responsable.');
            }

            // Récupérer toutes les évaluations académiques non validées pour les stagiaires de cette université
            $evaluationsEnAttente = Evaluation::whereHas('stage', function($query) use ($universite) {
                $query->where('type', 'academique')
                      ->whereHas('stagiaire', function($subQuery) use ($universite) {
                          $subQuery->where('universite_id', $universite->id);
                      });
            })
            ->where('validee_par_ru', false)
            ->with([
                'stage.stagiaire.user',
                'stage.structure',
                'stage.demandeStage',
                'agent.user'
            ])
            ->orderBy('date_evaluation', 'desc')
            ->get();

            // Récupérer les évaluations déjà validées (pour historique)
            $evaluationsValidees = Evaluation::whereHas('stage', function($query) use ($universite) {
                $query->where('type', 'academique')
                      ->whereHas('stagiaire', function($subQuery) use ($universite) {
                          $subQuery->where('universite_id', $universite->id);
                      });
            })
            ->where('validee_par_ru', true)
            ->with([
                'stage.stagiaire.user',
                'stage.structure',
                'stage.demandeStage',
                'agent.user',
                'ruValidateur.user'
            ])
            ->orderBy('date_validation_ru', 'desc')
            ->limit(20)
            ->get();

            return Inertia::render('ResponsableUniversite/Evaluations/Index', [
                'evaluationsEnAttente' => $evaluationsEnAttente,
                'evaluationsValidees' => $evaluationsValidees,
                'universite' => $universite,
                'notifications' => $user->notifications->where('read_at', null)->sortByDesc('created_at')->take(10),
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des évaluations RU', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return redirect()->route('dashboard')->with('error', 'Une erreur est survenue lors du chargement des évaluations.');
        }
    }

    /**
     * Valider une évaluation académique
     */
    public function valider(Request $request, Evaluation $evaluation)
    {
        try {
            $user = Auth::user();
            
            // Vérifier que l'utilisateur est un agent RU
            if ($user->role !== 'agent' || !$user->agent || $user->agent->role_agent !== 'RU') {
                return response()->json(['success' => false, 'message' => 'Accès non autorisé.'], 403);
            }

            $agent = $user->agent;
            
            // Vérifier que l'évaluation concerne un stagiaire de l'université de ce RU
            $universite = $agent->universiteResponsable;
            if (!$universite || $evaluation->stage->stagiaire->universite_id !== $universite->id) {
                return response()->json(['success' => false, 'message' => 'Cette évaluation ne concerne pas votre université.'], 403);
            }

            // Vérifier que l'évaluation n'est pas déjà validée
            if ($evaluation->validee_par_ru) {
                return response()->json(['success' => false, 'message' => 'Cette évaluation a déjà été validée.'], 400);
            }

            $validated = $request->validate([
                'commentaire_ru' => 'nullable|string|max:1000'
            ]);

            // Marquer l'évaluation comme validée
            $evaluation->update([
                'validee_par_ru' => true,
                'date_validation_ru' => now(),
                'ru_validateur_id' => $agent->id,
                'commentaire_ru' => $validated['commentaire_ru'] ?? null
            ]);

            // Notifier le stagiaire que son évaluation est maintenant disponible
            $stagiaire = $evaluation->stage->stagiaire;
            if ($stagiaire && $stagiaire->user) {
                $stagiaire->user->notify(new \App\Notifications\StagiaireNotification(
                    'Votre évaluation de stage académique a été validée et est maintenant disponible.',
                    route('stagiaire.stages.show', $evaluation->stage->id)
                ));

                // Envoyer aussi un email
                try {
                    \Mail::to($stagiaire->user->email)
                        ->send(new \App\Mail\EvaluationValideeRUMail($stagiaire, $evaluation->stage, $evaluation));
                } catch (\Exception $e) {
                    Log::error('Erreur lors de l\'envoi du mail de validation RU', [
                        'error' => $e->getMessage(),
                        'evaluation_id' => $evaluation->id
                    ]);
                }
            }

            Log::info('Évaluation validée par RU', [
                'evaluation_id' => $evaluation->id,
                'ru_agent_id' => $agent->id,
                'stagiaire_id' => $stagiaire->id_stagiaire
            ]);

            return response()->json(['success' => true, 'message' => 'Évaluation validée avec succès.']);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la validation de l\'évaluation par RU', [
                'error' => $e->getMessage(),
                'evaluation_id' => $evaluation->id,
                'user_id' => Auth::id()
            ]);
            return response()->json(['success' => false, 'message' => 'Une erreur est survenue lors de la validation.'], 500);
        }
    }
}
