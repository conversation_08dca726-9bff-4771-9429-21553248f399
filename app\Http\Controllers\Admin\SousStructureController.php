<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Structure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class SousStructureController extends Controller
{
    /**
     * Affiche la liste des sous-structures (vue seulement pour l'admin)
     */
    public function index()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // VUE SEULEMENT : L'admin peut voir toutes les sous-structures mais ne peut pas les modifier
        // Les sous-structures sont celles qui ont un parent_id (ne sont pas des structures DG)
        $sousStructures = Structure::with(['parent', 'responsable.user', 'children'])
            ->whereNotNull('parent_id') // Seulement les sous-structures
            ->get();

        return Inertia::render('Admin/SousStructures/Index', [
            'sousStructures' => $sousStructures,
        ]);
    }

    /**
     * Affiche les détails d'une sous-structure (vue seulement)
     */
    public function show(Structure $structure)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Vérifier que c'est bien une sous-structure
        if ($structure->parent_id === null) {
            return redirect()->route('admin.sous-structures.index')
                ->with('error', 'Cette structure est une Direction Générale, pas une sous-structure.');
        }

        $structure->load(['parent', 'responsable.user', 'children']);

        return Inertia::render('Admin/SousStructures/Show', [
            'structure' => $structure,
        ]);
    }
}
