const Ziggy = {"url":"http:\/\/127.0.0.1:8000","port":8000,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"ignition.healthCheck":{"uri":"_ignition\/health-check","methods":["GET","HEAD"]},"ignition.executeSolution":{"uri":"_ignition\/execute-solution","methods":["POST"]},"ignition.updateConfig":{"uri":"_ignition\/update-config","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"profile.avatar.remove":{"uri":"profile\/avatar","methods":["DELETE"]},"password.update":{"uri":"password","methods":["PUT"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"admin.structures.available":{"uri":"admin\/structures\/available","methods":["GET","HEAD"]},"admin.structures.index":{"uri":"admin\/structures","methods":["GET","HEAD"]},"admin.structures.show":{"uri":"admin\/structures\/{structure}","methods":["GET","HEAD"],"parameters":["structure"],"bindings":{"structure":"id"}},"admin.structures.store":{"uri":"admin\/structures","methods":["POST"]},"admin.structures.update":{"uri":"admin\/structures\/{structure}","methods":["PUT"],"parameters":["structure"],"bindings":{"structure":"id"}},"admin.structures.destroy":{"uri":"admin\/structures\/{structure}","methods":["DELETE"],"parameters":["structure"],"bindings":{"structure":"id"}},"admin.stagiaires.index":{"uri":"admin\/stagiaires","methods":["GET","HEAD"]},"admin.agents.index":{"uri":"admin\/agents","methods":["GET","HEAD"]},"admin.agents.store":{"uri":"admin\/agents","methods":["POST"]},"admin.agents.update":{"uri":"admin\/agents\/{agent}","methods":["PUT"],"parameters":["agent"],"bindings":{"agent":"id"}},"admin.agents.destroy":{"uri":"admin\/agents\/{agent}","methods":["DELETE"],"parameters":["agent"],"bindings":{"agent":"id"}},"admin.agent-rs.create":{"uri":"admin\/agent-rs\/create","methods":["GET","HEAD"]},"admin.agent-rs.store":{"uri":"admin\/agent-rs","methods":["POST"]},"admin.agents-ms.index":{"uri":"admin\/agents-ms","methods":["GET","HEAD"]},"admin.agents-ms.show":{"uri":"admin\/agents-ms\/{agent}","methods":["GET","HEAD"],"parameters":["agent"],"bindings":{"agent":"id"}},"admin.sous-structures.index":{"uri":"admin\/sous-structures","methods":["GET","HEAD"]},"admin.sous-structures.show":{"uri":"admin\/sous-structures\/{structure}","methods":["GET","HEAD"],"parameters":["structure"],"bindings":{"structure":"id"}},"admin.organigrammes.index":{"uri":"admin\/organigrammes","methods":["GET","HEAD"]},"admin.organigrammes.show":{"uri":"admin\/organigrammes\/{structure}","methods":["GET","HEAD"],"parameters":["structure"],"bindings":{"structure":"id"}},"admin.universites.index":{"uri":"admin\/universites","methods":["GET","HEAD"]},"admin.universites.create":{"uri":"admin\/universites\/create","methods":["GET","HEAD"]},"admin.universites.store":{"uri":"admin\/universites","methods":["POST"]},"admin.universites.show":{"uri":"admin\/universites\/{universite}","methods":["GET","HEAD"],"parameters":["universite"],"bindings":{"universite":"id"}},"admin.universites.edit":{"uri":"admin\/universites\/{universite}\/edit","methods":["GET","HEAD"],"parameters":["universite"],"bindings":{"universite":"id"}},"admin.universites.update":{"uri":"admin\/universites\/{universite}","methods":["PUT"],"parameters":["universite"],"bindings":{"universite":"id"}},"admin.universites.destroy":{"uri":"admin\/universites\/{universite}","methods":["DELETE"],"parameters":["universite"],"bindings":{"universite":"id"}},"admin.universites.toggle-active":{"uri":"admin\/universites\/{universite}\/toggle-active","methods":["POST"],"parameters":["universite"],"bindings":{"universite":"id"}},"admin.responsables-ru.index":{"uri":"admin\/responsables-ru","methods":["GET","HEAD"]},"admin.responsables-ru.create":{"uri":"admin\/responsables-ru\/create","methods":["GET","HEAD"]},"admin.responsables-ru.store":{"uri":"admin\/responsables-ru","methods":["POST"]},"admin.responsables-ru.show":{"uri":"admin\/responsables-ru\/{responsableRu}","methods":["GET","HEAD"],"parameters":["responsableRu"],"bindings":{"responsableRu":"id"}},"admin.responsables-ru.edit":{"uri":"admin\/responsables-ru\/{responsableRu}\/edit","methods":["GET","HEAD"],"parameters":["responsableRu"],"bindings":{"responsableRu":"id"}},"admin.responsables-ru.update":{"uri":"admin\/responsables-ru\/{responsableRu}","methods":["PUT"],"parameters":["responsableRu"],"bindings":{"responsableRu":"id"}},"admin.responsables-ru.destroy":{"uri":"admin\/responsables-ru\/{responsableRu}","methods":["DELETE"],"parameters":["responsableRu"],"bindings":{"responsableRu":"id"}},"demande_stages.store":{"uri":"demande-stages","methods":["POST"]},"mes.demandes":{"uri":"mes-demandes","methods":["GET","HEAD"]},"mes.demandes.show":{"uri":"mes-demandes\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"mes.demandes.annuler":{"uri":"mes-demandes\/{id}","methods":["DELETE"],"parameters":["id"]},"demandes.search":{"uri":"demandes\/recherche","methods":["POST"]},"demandes.search.get":{"uri":"demandes\/recherche","methods":["GET","HEAD"]},"recherche.code":{"uri":"recherche-code","methods":["GET","HEAD"]},"stagiaires.index":{"uri":"stagiaires","methods":["GET","HEAD"]},"stagiaires.create":{"uri":"stagiaires\/create","methods":["GET","HEAD"]},"stagiaires.store":{"uri":"stagiaires","methods":["POST"]},"stagiaires.show":{"uri":"stagiaires\/{stagiaire}","methods":["GET","HEAD"],"parameters":["stagiaire"]},"stagiaires.edit":{"uri":"stagiaires\/{stagiaire}\/edit","methods":["GET","HEAD"],"parameters":["stagiaire"]},"stagiaires.update":{"uri":"stagiaires\/{stagiaire}","methods":["PUT","PATCH"],"parameters":["stagiaire"]},"stagiaires.destroy":{"uri":"stagiaires\/{stagiaire}","methods":["DELETE"],"parameters":["stagiaire"]},"agent.dashboard":{"uri":"agent\/dashboard","methods":["GET","HEAD"]},"agent.demandes":{"uri":"agent\/demandes","methods":["GET","HEAD"]},"agent.demandes.show":{"uri":"agent\/demandes\/{demande}","methods":["GET","HEAD"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.demandes.approve":{"uri":"agent\/demandes\/{demande}\/approve","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.demandes.reject":{"uri":"agent\/demandes\/{demande}\/reject","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.stages":{"uri":"agent\/stages","methods":["GET","HEAD"]},"agent.stages.show":{"uri":"agent\/stages\/{stage}","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.structures":{"uri":"agent\/structures","methods":["GET","HEAD"]},"agent.structures.show":{"uri":"agent\/structures\/{structure}","methods":["GET","HEAD"],"parameters":["structure"],"bindings":{"structure":"id"}},"agent.stagiaires.index":{"uri":"agent\/stagiaires","methods":["GET","HEAD"]},"agent.stagiaires.show":{"uri":"agent\/stagiaires\/{stagiaire}","methods":["GET","HEAD"],"parameters":["stagiaire"],"bindings":{"stagiaire":"id_stagiaire"}},"agent.demandes.affecter":{"uri":"agent\/demandes\/{demande}\/affecter","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.demandes.refuseDefinitivement":{"uri":"agent\/demandes\/{demande}\/refuse-definitivement","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.rs.dashboard":{"uri":"agent\/rs\/dashboard","methods":["GET","HEAD"]},"agent.rs.demandes":{"uri":"agent\/rs\/demandes","methods":["GET","HEAD"]},"agent.rs.demandes.show":{"uri":"agent\/rs\/demandes\/{demande}","methods":["GET","HEAD"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.rs.demandes.approve":{"uri":"agent\/rs\/demandes\/{demande}\/approve","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.rs.demandes.reject":{"uri":"agent\/rs\/demandes\/{demande}\/reject","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.rs.responsable-agents":{"uri":"agent\/rs\/responsable-agents","methods":["GET","HEAD"]},"agent.rs.demandes.affecter-maitre":{"uri":"agent\/rs\/demandes\/{demande}\/affecter-maitre","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.rs.stages":{"uri":"agent\/rs\/stages","methods":["GET","HEAD"]},"agent.rs.stages.show":{"uri":"agent\/rs\/stages\/{stage}","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.rs.stages.affecter-maitre":{"uri":"agent\/rs\/stages\/{stage}\/affecter-maitre","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.rs.stages.attestation":{"uri":"agent\/rs\/stages\/{stage}\/attestation","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.rs.agents.index":{"uri":"agent\/rs\/agents","methods":["GET","HEAD"]},"agent.rs.agents.create":{"uri":"agent\/rs\/agents\/create","methods":["GET","HEAD"]},"agent.rs.agents.store":{"uri":"agent\/rs\/agents","methods":["POST"]},"agent.rs.agents.show":{"uri":"agent\/rs\/agents\/{agent}","methods":["GET","HEAD"],"parameters":["agent"]},"agent.rs.agents.edit":{"uri":"agent\/rs\/agents\/{agent}\/edit","methods":["GET","HEAD"],"parameters":["agent"],"bindings":{"agent":"id"}},"agent.rs.agents.update":{"uri":"agent\/rs\/agents\/{agent}","methods":["PUT","PATCH"],"parameters":["agent"],"bindings":{"agent":"id"}},"agent.rs.agents.destroy":{"uri":"agent\/rs\/agents\/{agent}","methods":["DELETE"],"parameters":["agent"],"bindings":{"agent":"id"}},"agent.rs.organigramme.sous-structures":{"uri":"agent\/rs\/organigramme\/sous-structures","methods":["GET","HEAD"]},"agent.rs.organigramme.index":{"uri":"agent\/rs\/organigramme","methods":["GET","HEAD"]},"agent.rs.organigramme.create":{"uri":"agent\/rs\/organigramme\/create","methods":["GET","HEAD"]},"agent.rs.organigramme.store":{"uri":"agent\/rs\/organigramme","methods":["POST"]},"agent.rs.organigramme.show":{"uri":"agent\/rs\/organigramme\/{structure}","methods":["GET","HEAD"],"parameters":["structure"]},"agent.rs.organigramme.edit":{"uri":"agent\/rs\/organigramme\/{structure}\/edit","methods":["GET","HEAD"],"parameters":["structure"]},"agent.rs.organigramme.update":{"uri":"agent\/rs\/organigramme\/{structure}","methods":["PUT","PATCH"],"parameters":["structure"],"bindings":{"structure":"id"}},"agent.rs.organigramme.destroy":{"uri":"agent\/rs\/organigramme\/{structure}","methods":["DELETE"],"parameters":["structure"],"bindings":{"structure":"id"}},"agent.rs.organigramme.assign-agent":{"uri":"agent\/rs\/organigramme\/{structure}\/assign-agent","methods":["POST"],"parameters":["structure"],"bindings":{"structure":"id"}},"agent.ru.dashboard":{"uri":"agent\/ru\/dashboard","methods":["GET","HEAD"]},"agent.ru.universite.show":{"uri":"agent\/ru\/universite","methods":["GET","HEAD"]},"agent.ru.universite.update":{"uri":"agent\/ru\/universite","methods":["PUT"]},"agent.ru.stages.index":{"uri":"agent\/ru\/stages","methods":["GET","HEAD"]},"agent.ru.stages.show":{"uri":"agent\/ru\/stages\/{stage}","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ru.stages.print-notes":{"uri":"agent\/ru\/stages\/{stage}\/print-notes","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ru.etudiants.index":{"uri":"agent\/ru\/etudiants","methods":["GET","HEAD"]},"agent.ru.etudiants.show":{"uri":"agent\/ru\/etudiants\/{etudiant}","methods":["GET","HEAD"],"parameters":["etudiant"]},"agent.ru.demandes.index":{"uri":"agent\/ru\/demandes","methods":["GET","HEAD"]},"agent.ru.demandes.show":{"uri":"agent\/ru\/demandes\/{demande}","methods":["GET","HEAD"],"parameters":["demande"]},"agent.ru.rapports.index":{"uri":"agent\/ru\/rapports","methods":["GET","HEAD"]},"agent.ru.evaluations.index":{"uri":"agent\/ru\/evaluations","methods":["GET","HEAD"]},"agent.ru.evaluations.show":{"uri":"agent\/ru\/evaluations\/{evaluation}","methods":["GET","HEAD"],"parameters":["evaluation"],"bindings":{"evaluation":"id"}},"agent.ru.evaluations.valider":{"uri":"agent\/ru\/evaluations\/{evaluation}\/valider","methods":["POST"],"parameters":["evaluation"],"bindings":{"evaluation":"id"}},"agent.ru.evaluations.pdf":{"uri":"agent\/ru\/evaluations\/{evaluation}\/pdf","methods":["GET","HEAD"],"parameters":["evaluation"],"bindings":{"evaluation":"id"}},"agent.ru.profile.edit":{"uri":"agent\/ru\/profile","methods":["GET","HEAD"]},"agent.ru.profile.update":{"uri":"agent\/ru\/profile","methods":["PATCH"]},"agent.ru.profile.destroy":{"uri":"agent\/ru\/profile","methods":["DELETE"]},"agent.ms.dashboard":{"uri":"agent\/ms\/dashboard","methods":["GET","HEAD"]},"agent.ms.stages":{"uri":"agent\/ms\/stages","methods":["GET","HEAD"]},"agent.ms.stages.show":{"uri":"agent\/ms\/stages\/{stage}","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.update-status":{"uri":"agent\/ms\/stages\/{stage}\/update-status","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.theme.store":{"uri":"agent\/ms\/stages\/{stage}\/theme","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.valider-theme":{"uri":"agent\/ms\/stages\/{stage}\/valider-theme","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.refuser-theme":{"uri":"agent\/ms\/stages\/{stage}\/refuser-theme","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.noter":{"uri":"agent\/ms\/stages\/{stage}\/noter","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.format-evaluation":{"uri":"agent\/ms\/stages\/{stage}\/format-evaluation","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.format-evaluation-universite":{"uri":"agent\/ms\/stages\/{stage}\/format-evaluation\/{universiteNom}","methods":["GET","HEAD"],"parameters":["stage","universiteNom"],"bindings":{"stage":"id"}},"agent.ms.stages.creer-format-evaluation":{"uri":"agent\/ms\/stages\/{stage}\/format-evaluation","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.maitres-stage-substructures":{"uri":"agent\/ms\/stages\/{stage}\/maitres-stage-substructures","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.reaffecter":{"uri":"agent\/ms\/stages\/{stage}\/reaffecter","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.themes-proposes":{"uri":"agent\/ms\/stages\/{stage}\/themes-proposes","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.theme-action":{"uri":"agent\/ms\/stages\/{stage}\/themes\/{theme}\/action","methods":["POST"],"parameters":["stage","theme"],"bindings":{"stage":"id","theme":"id"}},"agent.ms.":{"uri":"agent\/ms\/stages\/{stage}\/evaluations\/{membre}","methods":["GET","HEAD"],"parameters":["stage","membre"],"bindings":{"stage":"id"}},"agent.ms.stages.send-message":{"uri":"agent\/ms\/stages\/send-message","methods":["POST"]},"agent.ms.ms.stages.confirmer-fin":{"uri":"agent\/ms\/stages\/{stage}\/confirmer-fin","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"agent.ms.stages.proposer-theme":{"uri":"agent\/ms\/stages\/{stage}\/themes","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"stagiaire.dashboard":{"uri":"stagiaire\/dashboard","methods":["GET","HEAD"]},"stagiaire.stages":{"uri":"stagiaire\/stages","methods":["GET","HEAD"]},"stagiaire.stages.show":{"uri":"stagiaire\/stages\/{stage}","methods":["GET","HEAD"],"parameters":["stage"],"bindings":{"stage":"id"}},"stagiaire.stages.proposer-theme":{"uri":"stagiaire\/stages\/{stage}\/themes","methods":["POST"],"parameters":["stage"],"bindings":{"stage":"id"}},"admin.rapport-mensuel":{"uri":"admin\/rapport-mensuel","methods":["GET","HEAD"]},"stagiaire.notifications.markAsRead":{"uri":"stagiaire\/notifications\/{id}\/mark-as-read","methods":["POST"],"parameters":["id"]},"responsable-universite.evaluations.index":{"uri":"responsable-universite\/evaluations","methods":["GET","HEAD"]},"responsable-universite.evaluations.valider":{"uri":"responsable-universite\/evaluations\/{evaluation}\/valider","methods":["POST"],"parameters":["evaluation"],"bindings":{"evaluation":"id"}},"agent.notifications.markAsRead":{"uri":"agent\/notifications\/{id}\/mark-as-read","methods":["POST"],"parameters":["id"]},"admin.notifications.index":{"uri":"admin\/notifications","methods":["GET","HEAD"]},"admin.notifications.markAsRead":{"uri":"admin\/notifications\/{id}\/mark-as-read","methods":["POST"],"parameters":["id"]},"admin.notifications.markAllAsRead":{"uri":"admin\/notifications\/mark-all-as-read","methods":["POST"]},"admin.notifications.destroy":{"uri":"admin\/notifications\/{id}","methods":["DELETE"],"parameters":["id"]},"admin.notifications.unread":{"uri":"admin\/notifications\/unread","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
