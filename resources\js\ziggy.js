const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"ignition.healthCheck":{"uri":"_ignition\/health-check","methods":["GET","HEAD"]},"ignition.executeSolution":{"uri":"_ignition\/execute-solution","methods":["POST"]},"ignition.updateConfig":{"uri":"_ignition\/update-config","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"profile","methods":["GET","HEAD"]},"profile.update":{"uri":"profile","methods":["PATCH"]},"profile.destroy":{"uri":"profile","methods":["DELETE"]},"admin.dashboard":{"uri":"admin\/dashboard","methods":["GET","HEAD"]},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"admin.structures.index":{"uri":"admin\/structures","methods":["GET","HEAD"]},"admin.structures.store":{"uri":"admin\/structures","methods":["POST"]},"admin.structures.update":{"uri":"admin\/structures\/{structure}","methods":["PUT"],"parameters":["structure"],"bindings":{"structure":"id"}},"admin.structures.destroy":{"uri":"admin\/structures\/{structure}","methods":["DELETE"],"parameters":["structure"],"bindings":{"structure":"id"}},"admin.structures.available":{"uri":"admin\/structures\/available","methods":["GET","HEAD"]},"admin.stagiaires.index":{"uri":"admin\/stagiaires","methods":["GET","HEAD"]},"admin.agents.index":{"uri":"admin\/agents","methods":["GET","HEAD"]},"admin.agents.store":{"uri":"admin\/agents","methods":["POST"]},"admin.agents.update":{"uri":"admin\/agents\/{agent}","methods":["PUT"],"parameters":["agent"],"bindings":{"agent":"id"}},"admin.agents.destroy":{"uri":"admin\/agents\/{agent}","methods":["DELETE"],"parameters":["agent"],"bindings":{"agent":"id"}},"demande_stages.store":{"uri":"demande-stages","methods":["POST"]},"mes.demandes":{"uri":"mes-demandes","methods":["GET","HEAD"]},"mes.demandes.show":{"uri":"mes-demandes\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"mes.demandes.annuler":{"uri":"mes-demandes\/{id}","methods":["DELETE"],"parameters":["id"]},"demandes.search":{"uri":"demandes\/recherche","methods":["POST"]},"demandes.search.get":{"uri":"demandes\/recherche","methods":["GET","HEAD"]},"recherche.code":{"uri":"recherche-code","methods":["GET","HEAD"]},"stagiaires.index":{"uri":"stagiaires","methods":["GET","HEAD"]},"stagiaires.create":{"uri":"stagiaires\/create","methods":["GET","HEAD"]},"stagiaires.store":{"uri":"stagiaires","methods":["POST"]},"stagiaires.show":{"uri":"stagiaires\/{stagiaire}","methods":["GET","HEAD"],"parameters":["stagiaire"]},"stagiaires.edit":{"uri":"stagiaires\/{stagiaire}\/edit","methods":["GET","HEAD"],"parameters":["stagiaire"]},"stagiaires.update":{"uri":"stagiaires\/{stagiaire}","methods":["PUT","PATCH"],"parameters":["stagiaire"]},"stagiaires.destroy":{"uri":"stagiaires\/{stagiaire}","methods":["DELETE"],"parameters":["stagiaire"]},"agent.dashboard":{"uri":"agent\/dashboard","methods":["GET","HEAD"]},"agent.demandes":{"uri":"agent\/demandes","methods":["GET","HEAD"]},"agent.demandes.show":{"uri":"agent\/demandes\/{demande}","methods":["GET","HEAD"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.demandes.approve":{"uri":"agent\/demandes\/{demande}\/approve","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.demandes.reject":{"uri":"agent\/demandes\/{demande}\/reject","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.structures":{"uri":"agent\/structures","methods":["GET","HEAD"]},"agent.structures.show":{"uri":"agent\/structures\/{structure}","methods":["GET","HEAD"],"parameters":["structure"],"bindings":{"structure":"id"}},"agent.stagiaires.index":{"uri":"agent\/stagiaires","methods":["GET","HEAD"]},"agent.stagiaires.show":{"uri":"agent\/stagiaires\/{stagiaire}","methods":["GET","HEAD"],"parameters":["stagiaire"],"bindings":{"stagiaire":"id_stagiaire"}},"agent.agent.demandes.affecter":{"uri":"agent\/demandes\/{demande}\/affecter","methods":["POST"],"parameters":["demande"]},"agent.rs.dashboard":{"uri":"agent\/rs\/dashboard","methods":["GET","HEAD"]},"agent.rs.demandes":{"uri":"agent\/rs\/demandes","methods":["GET","HEAD"]},"agent.rs.demandes.show":{"uri":"agent\/rs\/demandes\/{demande}","methods":["GET","HEAD"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.rs.demandes.approve":{"uri":"agent\/rs\/demandes\/{demande}\/approve","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"agent.rs.demandes.reject":{"uri":"agent\/rs\/demandes\/{demande}\/reject","methods":["POST"],"parameters":["demande"],"bindings":{"demande":"id"}},"stagiaire.dashboard":{"uri":"stagiaire\/dashboard","methods":["GET","HEAD"]},"stagiaire.demandes":{"uri":"stagiaire\/demandes","methods":["GET","HEAD"]},"stagiaire.demandes.show":{"uri":"stagiaire\/demandes\/{demande}","methods":["GET","HEAD"],"parameters":["demande"]},"admin.rapport-mensuel":{"uri":"admin\/rapport-mensuel","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
