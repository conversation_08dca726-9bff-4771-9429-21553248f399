@extends('emails.layouts.modern')

@section('title', 'Nouveau thème de stage proposé')
@section('header-title', 'Nouveau Thème Proposé')
@section('header-subtitle', 'Gestion des Stages - Ministère des Finances')

@section('content')
    <!-- Salutation -->
    <div style="margin-bottom: 25px;">
        <h2 style="color: #1e293b; margin: 0 0 12px 0; font-size: 1.3rem; font-weight: 600;">
            Bonjour {{ $agentNom }},
        </h2>
        <p style="color: #475569; margin: 0; line-height: 1.6; font-size: 15px;">
            Un nouveau thème de stage vient d'être proposé dans votre structure et nécessite votre validation.
        </p>
    </div>

    <!-- Message d'information -->
    @include('emails.components.info-box', [
        'title' => 'Nouveau thème en attente',
        'icon' => '📋',
        'color' => '#f59e0b'
    ])
        <p style="margin: 0; font-weight: 500;">
            Un thème de stage a été soumis par <strong style="color: #1e40af;">{{ $proposePar }}</strong> 
            et attend votre approbation.
        </p>
    @endinclude

    <!-- Détails du thème -->
    @include('emails.components.details-table', [
        'title' => 'Détails du thème proposé',
        'icon' => '🎯'
    ])
        <tr>
            <td class="label">📝 Intitulé :</td>
            <td class="value">{{ $theme->intitule }}</td>
        </tr>
        <tr>
            <td class="label">📄 Description :</td>
            <td class="value">{{ $theme->description ?? 'Aucune description fournie' }}</td>
        </tr>
        <tr>
            <td class="label">🏢 Structure :</td>
            <td class="value">{{ $theme->structure->libelle ?? 'Non spécifiée' }}</td>
        </tr>
        <tr>
            <td class="label">👤 Proposé par :</td>
            <td class="value">{{ $proposePar }}</td>
        </tr>
        <tr>
            <td class="label">📅 Date de proposition :</td>
            <td class="value">{{ \Carbon\Carbon::parse($theme->created_at)->format('d/m/Y à H:i') }}</td>
        </tr>
    @endinclude

    <!-- Bouton d'action -->
    @include('emails.components.button', [
        'url' => $urlValidation,
        'text' => 'Valider le thème',
        'icon' => '✅',
        'color' => '#10b981',
        'colorHover' => '#059669'
    ])

    <!-- Instructions -->
    <div style="background-color: #eff6ff; border-radius: 10px; padding: 20px; margin: 25px 0; border: 1px solid #bfdbfe;">
        <h4 style="color: #1e40af; margin: 0 0 12px 0; font-size: 1rem; font-weight: 600;">
            📌 Prochaines étapes
        </h4>
        <ul style="color: #475569; margin: 0; padding-left: 20px; line-height: 1.6;">
            <li>Connectez-vous à votre espace pour examiner le thème en détail</li>
            <li>Validez ou rejetez la proposition avec vos commentaires</li>
            <li>Le proposant sera automatiquement notifié de votre décision</li>
        </ul>
    </div>

    <!-- Message de remerciement -->
    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
        <p style="color: #475569; margin: 0; line-height: 1.6; font-size: 15px;">
            Merci de traiter cette demande dans les meilleurs délais pour permettre une bonne organisation des stages.
        </p>
        <p style="color: #1e293b; margin: 15px 0 0 0; font-weight: 600;">
            Cordialement,<br>
            <span style="color: #3b82f6;">L'équipe Gestion des Stages</span>
        </p>
    </div>
@endsection
