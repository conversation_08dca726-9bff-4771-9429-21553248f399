<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\User;
use App\Models\Structure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;

class AgentRSController extends Controller
{
    /**
     * Affiche le formulaire de création d'un agent RS
     */
    public function create()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Récupérer uniquement les structures de direction générale (sans parent)
        // qui n'ont pas encore de responsable assigné
        $structures = Structure::whereNull('parent_id')
            ->whereNull('responsable_id')
            ->orderBy('libelle')
            ->get();

        return Inertia::render('Admin/AgentRS/Create', [
            'structures' => $structures,
            'notifications' => Auth::user()->notifications()->latest()->take(20)->get() // APPROCHE CHIRURGICALE : Ajout notifications
        ]);
    }

    /**
     * Stocke un nouvel agent RS
     */
    public function store(Request $request)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'telephone' => 'required|string|max:20',
            'date_de_naissance' => 'required|date',
            'sexe' => 'required|in:Homme,Femme',
            'matricule' => 'required|string|unique:agents',
            'fonction' => 'required|string',
            'date_embauche' => 'nullable|date',
            'structure_responsable_id' => [
                'nullable',
                'exists:structures,id',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $structure = Structure::find($value);
                        if ($structure && $structure->parent_id !== null) {
                            $fail('Seules les structures de direction générale peuvent être assignées aux agents RS.');
                        }
                    }
                }
            ],
        ]);

        // Créer l'utilisateur
        $user = User::create([
            'nom' => $validated['nom'],
            'prenom' => $validated['prenom'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'telephone' => $validated['telephone'],
            'date_de_naissance' => $validated['date_de_naissance'],
            'sexe' => $validated['sexe'],
            'role' => 'agent',
            'created_by' => Auth::id(),
        ]);

        // Créer l'agent RS
        $agent = Agent::create([
            'user_id' => $user->id,
            'matricule' => $validated['matricule'],
            'fonction' => $validated['fonction'],
            'role_agent' => 'RS',
            'date_embauche' => $validated['date_embauche'] ?? now(),
            'created_by' => Auth::id(),
        ]);

        // Assigner la structure si fournie
        if (!empty($validated['structure_responsable_id'])) {
            $structure = Structure::find($validated['structure_responsable_id']);
            if ($structure) {
                // Vérifier si la structure n'a pas déjà un responsable
                if ($structure->responsable_id) {
                    return redirect()->back()->with('error', 'Cette structure a déjà un responsable assigné.');
                }
                
                $structure->update(['responsable_id' => $agent->id]);
            }
        }

        return redirect()->route('admin.users.index')->with('success', 'Agent RS créé avec succès.');
    }

    /**
     * Génère un matricule unique
     */
    private function generateUniqueMatricule()
    {
        do {
            $matricule = 'RS' . strtoupper(Str::random(6));
        } while (Agent::where('matricule', $matricule)->exists());

        return $matricule;
    }
}
