<template>
  <Head :title="pageTitle" />

  <ResponsableUniversiteLayout>
    <div class="space-y-6">
      <!-- En-tête -->
      <div>
        <h1 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h1>
        <p class="text-gray-600 mt-1">Gérez vos informations personnelles et de sécurité</p>
      </div>

      <!-- Informations du profil -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
          <h3 class="text-lg font-semibold text-blue-900">Informations personnelles</h3>
          <p class="text-sm text-blue-700 mt-1">Mettez à jour vos informations de profil et votre adresse email.</p>
        </div>
        
        <div class="p-6">
          <UpdateProfileInformationForm
            :must-verify-email="mustVerifyEmail"
            :status="status"
            class="max-w-xl"
          />
        </div>
      </div>

      <!-- Mise à jour du mot de passe -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
          <h3 class="text-lg font-semibold text-blue-900">Sécurité</h3>
          <p class="text-sm text-blue-700 mt-1">Assurez-vous que votre compte utilise un mot de passe long et aléatoire pour rester sécurisé.</p>
        </div>
        
        <div class="p-6">
          <UpdatePasswordForm class="max-w-xl" />
        </div>
      </div>

      <!-- Informations agent -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
          <h3 class="text-lg font-semibold text-blue-900">Informations agent</h3>
          <p class="text-sm text-blue-700 mt-1">Informations relatives à votre rôle de Responsable Université.</p>
        </div>
        
        <div class="p-6">
          <div class="max-w-xl space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Matricule</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-600">
                  {{ agent.matricule }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Fonction</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-600">
                  {{ agent.fonction }}
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Rôle</label>
                <div class="px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-700 font-medium">
                  Responsable Université (RU)
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date d'embauche</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-600">
                  {{ formatDate(agent.date_embauche) }}
                </div>
              </div>
            </div>

            <div v-if="agent.universite_responsable" class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="font-medium text-blue-900 mb-2">Université responsable</h4>
              <div class="text-sm text-blue-700">
                <p class="font-medium">{{ agent.universite_responsable.nom_complet }}</p>
                <p>{{ agent.universite_responsable.sigle }}</p>
                <p class="text-xs mt-1">{{ agent.universite_responsable.localisation }}</p>
              </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div class="flex">
                <svg class="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-yellow-800">Information</h4>
                  <p class="text-sm text-yellow-700 mt-1">
                    Les informations agent (matricule, fonction, université) ne peuvent être modifiées que par un administrateur.
                    Contactez le service administratif si des corrections sont nécessaires.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Suppression du compte -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-red-50">
          <h3 class="text-lg font-semibold text-red-900">Zone de danger</h3>
          <p class="text-sm text-red-700 mt-1">Une fois votre compte supprimé, toutes ses ressources et données seront définitivement effacées.</p>
        </div>
        
        <div class="p-6">
          <DeleteUserForm class="max-w-xl" />
        </div>
      </div>
    </div>
  </ResponsableUniversiteLayout>
</template>

<script setup>
import { Head } from '@inertiajs/vue3'
import ResponsableUniversiteLayout from '@/Layouts/ResponsableUniversiteLayout.vue'
import DeleteUserForm from '@/Pages/Profile/Partials/DeleteUserForm.vue'
import UpdatePasswordForm from '@/Pages/Profile/Partials/UpdatePasswordForm.vue'
import UpdateProfileInformationForm from '@/Pages/Profile/Partials/UpdateProfileInformationForm.vue'

const props = defineProps({
  pageTitle: String,
  mustVerifyEmail: Boolean,
  status: String,
  agent: Object,
})

const formatDate = (date) => {
  if (!date) return '—'
  return new Date(date).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
