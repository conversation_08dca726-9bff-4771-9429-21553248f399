<template>
  <Head title="Responsables RU" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-emerald-50 min-h-screen">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
          <div class="flex items-center gap-4">
            <div class="p-3 bg-blue-600 rounded-xl shadow-lg">
              <UserGroupIcon class="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 class="text-3xl font-bold text-slate-800">Responsables Université (RU)</h1>
              <p class="text-slate-600 mt-1">Gestion des responsables d'universités</p>
            </div>
          </div>
          <Link
            :href="route('admin.responsables-ru.create')"
            class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors shadow-lg"
          >
            <PlusIcon class="w-5 h-5" />
            Nouveau Responsable RU
          </Link>
        </div>

        <!-- Statistiques -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Responsables RU</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ stats.total }}</p>
              </div>
              <div class="p-3 bg-blue-100 rounded-lg">
                <UserGroupIcon class="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>
          
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Avec Université</p>
                <p class="text-3xl font-bold text-emerald-600 mt-2">{{ stats.avec_universite }}</p>
              </div>
              <div class="p-3 bg-emerald-100 rounded-lg">
                <AcademicCapIcon class="w-6 h-6 text-emerald-600" />
              </div>
            </div>
          </div>
          
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Sans Université</p>
                <p class="text-3xl font-bold text-orange-600 mt-2">{{ stats.sans_universite }}</p>
              </div>
              <div class="p-3 bg-orange-100 rounded-lg">
                <ExclamationTriangleIcon class="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        <!-- Filtres et recherche -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Rechercher</label>
              <div class="relative">
                <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Nom, prénom, email, matricule..."
                  class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Statut université</label>
              <select
                v-model="filterUniversite"
                class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Tous</option>
                <option value="avec">Avec université</option>
                <option value="sans">Sans université</option>
              </select>
            </div>
            
            <div class="flex items-end">
              <button
                @click="resetFilters"
                class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors"
              >
                Réinitialiser
              </button>
            </div>
          </div>
        </div>

        <!-- Liste des responsables RU -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">
              Liste des Responsables RU ({{ filteredResponsables.length }})
            </h3>
          </div>
          
          <div v-if="filteredResponsables.length > 0" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Responsable
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Matricule
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Université assignée
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date création
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="responsable in filteredResponsables" :key="responsable.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <UserIcon class="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          {{ responsable.user.prenom }} {{ responsable.user.nom }}
                        </div>
                        <div class="text-sm text-gray-500">{{ responsable.fonction }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {{ responsable.matricule }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div v-if="responsable.universite_responsable">
                      <div class="text-sm font-medium text-gray-900">
                        {{ responsable.universite_responsable.nom_complet }}
                      </div>
                      <div class="text-sm text-gray-500">{{ responsable.universite_responsable.sigle }}</div>
                    </div>
                    <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                      Aucune université
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>{{ responsable.user.email }}</div>
                    <div class="text-gray-500">{{ responsable.user.telephone }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(responsable.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end gap-2">
                      <Link
                        :href="route('admin.responsables-ru.show', responsable.id)"
                        class="text-blue-600 hover:text-blue-900 p-1 rounded"
                        title="Voir détails"
                      >
                        <EyeIcon class="w-4 h-4" />
                      </Link>
                      <Link
                        :href="route('admin.responsables-ru.edit', responsable.id)"
                        class="text-emerald-600 hover:text-emerald-900 p-1 rounded"
                        title="Modifier"
                      >
                        <PencilIcon class="w-4 h-4" />
                      </Link>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div v-else class="text-center py-12">
            <UserGroupIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun responsable RU</h3>
            <p class="mt-1 text-sm text-gray-500">Commencez par créer un nouveau responsable RU.</p>
            <div class="mt-6">
              <Link
                :href="route('admin.responsables-ru.create')"
                class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                <PlusIcon class="w-4 h-4" />
                Nouveau Responsable RU
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import { Head, Link } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import {
  UserGroupIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  UserIcon,
  AcademicCapIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  responsablesRU: Array,
  stats: Object
});

const searchQuery = ref('');
const filterUniversite = ref('');

const filteredResponsables = computed(() => {
  let filtered = props.responsablesRU;

  // Filtre par recherche
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(responsable =>
      responsable.user.nom.toLowerCase().includes(query) ||
      responsable.user.prenom.toLowerCase().includes(query) ||
      responsable.user.email.toLowerCase().includes(query) ||
      responsable.matricule.toLowerCase().includes(query) ||
      (responsable.universite_responsable && 
       responsable.universite_responsable.nom_complet.toLowerCase().includes(query))
    );
  }

  // Filtre par statut université
  if (filterUniversite.value === 'avec') {
    filtered = filtered.filter(responsable => responsable.universite_responsable);
  } else if (filterUniversite.value === 'sans') {
    filtered = filtered.filter(responsable => !responsable.universite_responsable);
  }

  return filtered;
});

const resetFilters = () => {
  searchQuery.value = '';
  filterUniversite.value = '';
};

const formatDate = (dateString) => {
  if (!dateString) return 'Non disponible';
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};
</script>
