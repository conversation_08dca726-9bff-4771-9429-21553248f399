<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Workflow RS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f3f4f6;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 30px;
        }
        .scenario {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #f9fafb;
        }
        .scenario.success {
            border-color: #10b981;
            background: #f0fdf4;
        }
        .scenario.error {
            border-color: #ef4444;
            background: #fef2f2;
        }
        .scenario h3 {
            margin-top: 0;
            color: #374151;
        }
        .details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        .detail {
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #d1d5db;
        }
        .detail strong {
            color: #374151;
        }
        .buttons-demo {
            margin-top: 15px;
            padding: 15px;
            background: #f3f4f6;
            border-radius: 6px;
        }
        .btn {
            padding: 8px 16px;
            margin-right: 10px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
        }
        .btn-accept {
            background: #10b981;
            color: white;
        }
        .btn-reject {
            background: #ef4444;
            color: white;
        }
        .btn-hidden {
            background: #d1d5db;
            color: #6b7280;
            cursor: not-allowed;
            opacity: 0.5;
        }
        .logic-explanation {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        .logic-explanation h3 {
            color: #1d4ed8;
            margin-top: 0;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test du Workflow RS - Affichage des Boutons</h1>
        <p style="text-align: center; color: #6b7280; margin-bottom: 30px;">
            Vérification de la logique d'affichage des boutons "Accepter" et "Rejeter"
        </p>
        
        @foreach($scenarios as $index => $scenario)
        <div class="scenario {{ $scenario['boutons_visibles'] ? 'success' : 'error' }}">
            <h3>{{ $scenario['titre'] }}</h3>
            
            <div class="details">
                <div class="detail">
                    <strong>Statut :</strong><br>
                    <span style="color: {{ $scenario['statut'] === 'En cours' ? '#f59e0b' : '#10b981' }};">
                        {{ $scenario['statut'] }}
                    </span>
                </div>
                <div class="detail">
                    <strong>Maître de stage :</strong><br>
                    <span style="color: {{ $scenario['maitre_stage_affecte'] ? '#10b981' : '#ef4444' }};">
                        {{ $scenario['maitre_stage_affecte'] ? 'Affecté' : 'Non affecté' }}
                    </span>
                </div>
                <div class="detail">
                    <strong>Boutons visibles :</strong><br>
                    <span style="color: {{ $scenario['boutons_visibles'] ? '#10b981' : '#ef4444' }};">
                        {{ $scenario['boutons_visibles'] ? 'OUI' : 'NON' }}
                    </span>
                </div>
            </div>
            
            <div class="buttons-demo">
                <strong>Aperçu des boutons :</strong><br><br>
                @if($scenario['boutons_visibles'])
                    <button class="btn btn-accept">✅ Accepter la demande</button>
                    <button class="btn btn-reject">❌ Rejeter la demande</button>
                    <span style="color: #10b981; font-weight: bold;">← Boutons actifs</span>
                @else
                    <button class="btn btn-hidden" disabled>✅ Accepter la demande</button>
                    <button class="btn btn-hidden" disabled>❌ Rejeter la demande</button>
                    <span style="color: #ef4444; font-weight: bold;">← Boutons cachés</span>
                @endif
            </div>
        </div>
        @endforeach
        
        <div class="logic-explanation">
            <h3>🔧 Logique de correction appliquée</h3>
            <p><strong>Condition d'affichage des boutons :</strong></p>
            <div class="code-block">
v-if="demande.statut === 'En cours' && !maitre_stage_deja_affecte"
            </div>
            
            <p><strong>Corrections apportées :</strong></p>
            <ul>
                <li>✅ <strong>Vue :</strong> Condition simplifiée pour afficher les boutons seulement si statut = "En cours" ET pas de maître de stage</li>
                <li>✅ <strong>Contrôleur :</strong> Le statut reste "Acceptée" après affectation du maître de stage (ne repasse plus à "En cours")</li>
                <li>✅ <strong>Interface :</strong> Rechargement automatique après acceptation pour mettre à jour l'affichage</li>
                <li>✅ <strong>Workflow :</strong> Une fois acceptée et/ou maître affecté, plus de boutons visibles</li>
            </ul>
            
            <p><strong>Workflow corrigé :</strong></p>
            <ol>
                <li><strong>Demande "En cours"</strong> → Boutons "Accepter/Rejeter" visibles</li>
                <li><strong>Clic "Accepter"</strong> → Statut devient "Acceptée" + rechargement page</li>
                <li><strong>Boutons disparaissent</strong> → Plus d'actions possibles</li>
                <li><strong>Affectation maître</strong> → Statut reste "Acceptée" (ne repasse pas à "En cours")</li>
                <li><strong>Workflow terminé</strong> → Interface propre sans boutons inutiles</li>
            </ol>
        </div>
    </div>
</body>
</html>
