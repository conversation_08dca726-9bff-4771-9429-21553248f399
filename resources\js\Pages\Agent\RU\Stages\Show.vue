<template>
  <Head :title="pageTitle" />
  
  <ResponsableUniversiteLayout>
    <div class="space-y-6">
      <!-- En-tête -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h1>
          <p class="text-gray-600 mt-1">{{ universite.nom_complet }}</p>
        </div>
        <div class="flex space-x-3">
          <Link :href="route('agent.ru.stages.index')" 
                class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
            ← Retour à la liste
          </Link>
          <button v-if="stage.evaluation" 
                  @click="printNotes"
                  class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H9.414a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 005.586 9H4a2 2 0 00-2 2v6a2 2 0 002 2h2m3 0h10a2 2 0 002-2v-4a2 2 0 00-2-2H9.414a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 005.586 9H4a2 2 0 00-2 2v6a2 2 0 002 2h2m3 0h10"/>
            </svg>
            <span>Imprimer les notes</span>
          </button>
        </div>
      </div>

      <!-- Informations du stagiaire -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
          <h3 class="text-lg font-semibold text-blue-900 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
            Informations du stagiaire
          </h3>
        </div>
        
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Nom complet</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ stage.stagiaire.user.prenom }} {{ stage.stagiaire.user.nom }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ stage.stagiaire.user.email }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ stage.stagiaire.user.telephone || '—' }}
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Université</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ stage.stagiaire.universite || '—' }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Niveau d'études</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ stage.stagiaire.niveau_etudes || '—' }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Code de suivi</label>
                <div class="px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-700 font-medium">
                  {{ stage.demandeStage?.code_suivi || '—' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Détails du stage -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
          <h3 class="text-lg font-semibold text-blue-900 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            Détails du stage
          </h3>
        </div>
        
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Structure d'accueil</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ stage.structure?.libelle || '—' }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Type de stage</label>
                <div class="px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-700 font-medium">
                  {{ stage.type }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                <div class="px-3 py-2 rounded-lg text-sm font-medium" :class="getStatusClass(stage.statut)">
                  {{ stage.statut }}
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ formatDate(stage.date_debut) }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ formatDate(stage.date_fin) }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Durée</label>
                <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900">
                  {{ calculateDuration(stage.date_debut, stage.date_fin) }} jours
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Maître de stage -->
      <div v-if="stage.affectationMaitreStage" class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
          <h3 class="text-lg font-semibold text-blue-900 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 6V8a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2z"/>
            </svg>
            Maître de stage
          </h3>
        </div>
        
        <div class="p-6">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="text-blue-600 font-semibold text-lg">
                {{ stage.affectationMaitreStage.maitreStage?.prenom?.charAt(0) || 'M' }}{{ stage.affectationMaitreStage.maitreStage?.nom?.charAt(0) || 'S' }}
              </span>
            </div>
            <div class="flex-1">
              <h4 class="text-lg font-semibold text-gray-900">
                {{ stage.affectationMaitreStage.maitreStage?.prenom || 'Prénom' }} {{ stage.affectationMaitreStage.maitreStage?.nom || 'Nom' }}
              </h4>
              <p class="text-gray-600">
                {{ stage.affectationMaitreStage.agentAffectant?.fonction || 'Maître de stage' }}
              </p>
              <p class="text-sm text-gray-500">{{ stage.affectationMaitreStage.maitreStage?.email || 'Email non disponible' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Évaluation -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-blue-50">
          <h3 class="text-lg font-semibold text-blue-900 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
            </svg>
            Évaluation
          </h3>
        </div>
        
        <div class="p-6">
          <div v-if="stage.evaluation">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">{{ stage.evaluation.note_totale }}/20</div>
                <div class="text-sm text-gray-600">Note finale</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-900 mb-2">
                  {{ formatDate(stage.evaluation.created_at) }}
                </div>
                <div class="text-sm text-gray-600">Date d'évaluation</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gray-900 mb-2">
                  {{ stage.evaluation.agent?.user?.prenom }} {{ stage.evaluation.agent?.user?.nom }}
                </div>
                <div class="text-sm text-gray-600">Évaluateur</div>
              </div>
            </div>

            <div v-if="!stage.evaluation.validee_par_ru" class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
                <div>
                  <h4 class="font-medium text-orange-800">Validation en attente</h4>
                  <p class="text-sm text-orange-700">Cette évaluation académique nécessite votre validation avant d'être visible par l'étudiant.</p>
                </div>
              </div>
            </div>

            <div v-if="stage.evaluation.commentaires" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-2">Commentaires du maître de stage :</h4>
              <p class="text-gray-700">{{ stage.evaluation.commentaires }}</p>
            </div>
          </div>
          
          <div v-else class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <p class="text-gray-500">Aucune évaluation disponible</p>
            <p class="text-sm text-gray-400 mt-1">L'évaluation sera disponible une fois que le maître de stage l'aura complétée.</p>
          </div>
        </div>
      </div>
    </div>
  </ResponsableUniversiteLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3'
import ResponsableUniversiteLayout from '@/Layouts/ResponsableUniversiteLayout.vue'

const props = defineProps({
  pageTitle: String,
  universite: Object,
  stage: Object,
})

// Fonctions utilitaires
const formatDate = (date) => {
  if (!date) return '—'
  return new Date(date).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const calculateDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return 0
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end - start)
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

const getStatusClass = (statut) => {
  const classes = {
    'En cours': 'bg-green-100 text-green-800 border border-green-200',
    'Terminé': 'bg-blue-100 text-blue-800 border border-blue-200',
    'Suspendu': 'bg-red-100 text-red-800 border border-red-200',
  }
  return classes[statut] || 'bg-gray-100 text-gray-800 border border-gray-200'
}

const printNotes = () => {
  window.open(route('agent.ru.stages.print-notes', props.stage.id), '_blank')
}
</script>
