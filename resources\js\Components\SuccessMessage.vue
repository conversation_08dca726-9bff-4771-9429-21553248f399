<template>
    <transition
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0 transform translate-y-2"
        enter-to-class="opacity-100 transform translate-y-0"
        leave-active-class="transition ease-in duration-200"
        leave-from-class="opacity-100 transform translate-y-0"
        leave-to-class="opacity-0 transform translate-y-2"
    >
        <div
            v-if="show"
            class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg border border-green-200 overflow-hidden"
        >
            <div class="p-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-3 w-0 flex-1">
                        <p class="text-sm font-medium text-gray-900">
                            {{ title || 'Succès' }}
                        </p>
                        <p class="mt-1 text-sm text-gray-500">
                            {{ message }}
                        </p>
                    </div>
                    <div class="ml-4 flex-shrink-0 flex">
                        <button
                            @click="close"
                            class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            <span class="sr-only">Fermer</span>
                            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <!-- Barre de progression -->
            <div class="bg-green-50 px-4 py-2">
                <div class="bg-green-200 rounded-full h-1">
                    <div 
                        class="bg-green-500 h-1 rounded-full transition-all duration-100 ease-linear"
                        :style="{ width: progressWidth + '%' }"
                    ></div>
                </div>
            </div>
        </div>
    </transition>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: 'Succès'
    },
    message: {
        type: String,
        required: true
    },
    duration: {
        type: Number,
        default: 5000
    },
    autoClose: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['close'])

const progressWidth = ref(100)
let progressInterval = null
let autoCloseTimeout = null

const close = () => {
    clearInterval(progressInterval)
    clearTimeout(autoCloseTimeout)
    emit('close')
}

const startProgress = () => {
    if (!props.autoClose) return
    
    progressWidth.value = 100
    const step = 100 / (props.duration / 100)
    
    progressInterval = setInterval(() => {
        progressWidth.value -= step
        if (progressWidth.value <= 0) {
            clearInterval(progressInterval)
        }
    }, 100)
    
    autoCloseTimeout = setTimeout(() => {
        close()
    }, props.duration)
}

watch(() => props.show, (newValue) => {
    if (newValue) {
        startProgress()
    } else {
        clearInterval(progressInterval)
        clearTimeout(autoCloseTimeout)
    }
})

onMounted(() => {
    if (props.show) {
        startProgress()
    }
})
</script>

<style scoped>
/* Styles additionnels si nécessaire */
</style>
