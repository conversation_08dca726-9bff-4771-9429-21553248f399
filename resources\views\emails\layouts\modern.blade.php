<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>@yield('title', 'Ministère des Finances')</title>
    <style>
        /* Reset styles pour clients email */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        /* Styles responsive */
        @media only screen and (max-width: 600px) {
            .container { width: 100% !important; margin: 0 !important; }
            .content-padding { padding: 20px !important; }
            .header-padding { padding: 25px 20px !important; }
            .header-logo { height: 60px !important; }
            .header-title { font-size: 20px !important; }
            .section-padding { padding: 20px !important; }
            .button { padding: 12px 24px !important; font-size: 14px !important; }
            .details-table td { display: block !important; width: 100% !important; padding: 8px 0 !important; }
            .details-table .label { font-weight: 600 !important; color: #475569 !important; }
        }
        
        /* Styles pour clients Outlook */
        <!--[if mso]>
        <style>
            .container { width: 600px !important; }
            .button { mso-style-priority: 100 !important; }
        </style>
        <![endif]-->
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6;">
    
    <!-- Container principal -->
    <div class="container" style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); overflow: hidden;">
        
        <!-- Header avec logo et titre -->
        <div class="header-padding" style="background: linear-gradient(135deg, #475569 0%, #1e40af 100%); padding: 35px 30px; text-align: center; position: relative;">
            <!-- Barre décorative -->
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #3b82f6, #60a5fa, #3b82f6);"></div>
            
            <!-- Logo -->
            <img src="{{ asset('images/logoministere.png') }}" alt="Ministère des Finances" class="header-logo" style="height: 75px; width: auto; margin-bottom: 15px; filter: brightness(1.1);">
            
            <!-- Titre principal -->
            <h1 class="header-title" style="color: #ffffff; margin: 0 0 8px 0; font-size: 24px; font-weight: 600; letter-spacing: -0.5px;">
                @yield('header-title', 'Gestion des Stages')
            </h1>
            
            <!-- Sous-titre -->
            <p style="color: rgba(255, 255, 255, 0.9); margin: 0; font-size: 14px;">
                @yield('header-subtitle', 'Ministère des Finances - République du Bénin')
            </p>
        </div>

        <!-- Contenu principal -->
        <div class="content-padding" style="padding: 40px 35px;">
            @yield('content')
        </div>

        <!-- Footer -->
        <div style="background-color: #f1f5f9; padding: 30px 35px; text-align: center; border-top: 1px solid #e2e8f0;">
            <!-- Message automatique -->
            <p style="color: #64748b; margin: 0 0 12px 0; font-size: 12px;">
                📧 Ce message est généré automatiquement, merci de ne pas y répondre.
            </p>
            
            <!-- Informations de contact -->
            <p style="color: #64748b; margin: 0 0 15px 0; font-size: 12px;">
                Pour toute question, contactez-nous via votre espace personnel.
            </p>
            
            <!-- Séparateur -->
            <div style="margin: 20px 0; height: 1px; background: linear-gradient(90deg, transparent, #e2e8f0, transparent);"></div>
            
            <!-- Copyright -->
            <p style="color: #94a3b8; margin: 0; font-size: 11px; font-weight: 500;">
                © {{ date('Y') }} Ministère des Finances - République du Bénin
            </p>
            
            <!-- Adresse -->
            <p style="color: #cbd5e1; margin: 5px 0 0 0; font-size: 10px;">
                Cotonou, République du Bénin
            </p>
        </div>
    </div>
    
    <!-- Espacement final -->
    <div style="height: 20px;"></div>
</body>
</html>
