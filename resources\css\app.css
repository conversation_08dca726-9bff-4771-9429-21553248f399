@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS pour l'harmonisation des couleurs */
:root {
  --primary-blue: #1e40af;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1e3a8a;
  --secondary-blue: #60a5fa;
  --accent-blue: #93c5fd;
  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;
}

/* Classes utilitaires pour l'harmonisation */
.sidebar-blue {
  background: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-700) 100%);
}

.header-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.btn-primary {
  background: linear-gradient(135deg, var(--blue-600) 0%, var(--blue-700) 100%);
  color: white;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--blue-700) 0%, var(--blue-800) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.card-modern {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(229, 231, 235, 0.8);
  transition: all 0.2s ease;
}

.card-modern:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Animations fluides */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from, .slide-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--blue-50);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--blue-300);
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--blue-400);
}

/* Focus states harmonisés */
.focus-blue:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--blue-500);
  ring-opacity: 0.5;
  border-color: var(--blue-500);
}
