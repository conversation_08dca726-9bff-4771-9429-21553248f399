<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes d'évaluation - {{ $stage->stagiaire->user->prenom }} {{ $stage->stagiaire->user->nom }}</title>
    <style>
        @page {
            margin: 2cm;
            size: A4;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1e40af;
            padding-bottom: 20px;
        }
        
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        
        .ministry-title {
            font-size: 16pt;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .document-title {
            font-size: 18pt;
            font-weight: bold;
            color: #000;
            margin-top: 20px;
            text-decoration: underline;
        }
        
        .info-section {
            margin: 25px 0;
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #1e40af;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .info-table th,
        .info-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .info-table th {
            background-color: #f1f5f9;
            font-weight: bold;
            width: 30%;
        }
        
        .evaluation-section {
            margin: 30px 0;
            page-break-inside: avoid;
        }
        
        .evaluation-title {
            font-size: 14pt;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 15px;
            border-bottom: 1px solid #1e40af;
            padding-bottom: 5px;
        }
        
        .criteria-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .criteria-table th,
        .criteria-table td {
            padding: 10px;
            text-align: left;
            border: 1px solid #333;
        }
        
        .criteria-table th {
            background-color: #1e40af;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        
        .criteria-table .criteria-name {
            width: 60%;
        }
        
        .criteria-table .criteria-note {
            width: 20%;
            text-align: center;
            font-weight: bold;
        }
        
        .criteria-table .criteria-max {
            width: 20%;
            text-align: center;
        }
        
        .total-row {
            background-color: #f1f5f9;
            font-weight: bold;
            font-size: 13pt;
        }
        
        .comments-section {
            margin: 25px 0;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #fafafa;
        }
        
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 45%;
            text-align: center;
            border-top: 1px solid #333;
            padding-top: 10px;
        }
        
        .footer {
            position: fixed;
            bottom: 1cm;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .font-bold {
            font-weight: bold;
        }
        
        .text-blue {
            color: #1e40af;
        }
    </style>
</head>
<body>
    <!-- En-tête officiel -->
    <div class="header">
        <img src="{{ public_path('images/logo-ministere.png') }}" alt="Logo Ministère" class="logo">
        <div class="ministry-title">
            RÉPUBLIQUE DU BÉNIN<br>
            MINISTÈRE DE L'ÉCONOMIE ET DES FINANCES
        </div>
        <div class="document-title">
            FICHE D'ÉVALUATION DE STAGE
        </div>
    </div>

    <!-- Informations du stagiaire -->
    <div class="info-section">
        <h3 class="evaluation-title">INFORMATIONS DU STAGIAIRE</h3>
        <table class="info-table">
            <tr>
                <th>Nom complet :</th>
                <td>{{ $stage->stagiaire->user->prenom }} {{ $stage->stagiaire->user->nom }}</td>
            </tr>
            <tr>
                <th>Email :</th>
                <td>{{ $stage->stagiaire->user->email }}</td>
            </tr>
            <tr>
                <th>Type de stage :</th>
                <td>{{ $stage->type }}</td>
            </tr>
            @if($stage->type === 'Académique' && $stage->stagiaire->universite)
            <tr>
                <th>Université :</th>
                <td>{{ $stage->stagiaire->universite }}</td>
            </tr>
            @endif
            <tr>
                <th>Structure d'accueil :</th>
                <td>{{ $stage->structure->libelle }}</td>
            </tr>
            <tr>
                <th>Période de stage :</th>
                <td>
                    Du {{ \Carbon\Carbon::parse($stage->date_debut)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                    au {{ \Carbon\Carbon::parse($stage->date_fin)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                    ({{ \Carbon\Carbon::parse($stage->date_debut)->diffInDays(\Carbon\Carbon::parse($stage->date_fin)) }} jours)
                </td>
            </tr>
            <tr>
                <th>Maître de stage :</th>
                <td>
                    @if($stage->affectationMaitreStage && $stage->affectationMaitreStage->maitreStage)
                        {{ $stage->affectationMaitreStage->maitreStage->prenom }} {{ $stage->affectationMaitreStage->maitreStage->nom }}
                        <br><small>{{ $stage->affectationMaitreStage->maitreStage->email }}</small>
                        @if($stage->affectationMaitreStage->agentAffectant && $stage->affectationMaitreStage->agentAffectant->user)
                            <br><small>Fonction : {{ $stage->affectationMaitreStage->agentAffectant->fonction }}</small>
                        @endif
                    @else
                        Non assigné
                    @endif
                </td>
            </tr>
        </table>
    </div>

    <!-- Évaluation détaillée -->
    @if($evaluation)
    <div class="evaluation-section">
        <h3 class="evaluation-title">DÉTAIL DE L'ÉVALUATION</h3>
        
        <table class="criteria-table">
            <thead>
                <tr>
                    <th class="criteria-name">Critère d'évaluation</th>
                    <th class="criteria-note">Note obtenue</th>
                    <th class="criteria-max">Note maximale</th>
                </tr>
            </thead>
            <tbody>
                @if($stage->type === 'Professionnel')
                    <!-- Critères standards pour stages professionnels -->
                    @php
                        $criteresStandards = [
                            'Ponctualité et assiduité',
                            'Qualité du travail fourni',
                            'Capacité d\'adaptation',
                            'Esprit d\'initiative',
                            'Respect des consignes',
                            'Intégration dans l\'équipe',
                            'Communication professionnelle',
                            'Capacité d\'apprentissage',
                            'Autonomie dans le travail',
                            'Contribution aux objectifs'
                        ];
                        $noteParCritere = $evaluation->note_totale / 10; // 10 critères
                    @endphp
                    @foreach($criteresStandards as $index => $critere)
                    <tr>
                        <td>{{ $critere }}</td>
                        <td class="criteria-note">{{ number_format($noteParCritere, 1) }}</td>
                        <td class="criteria-max">2,0</td>
                    </tr>
                    @endforeach
                @else
                    <!-- Critères académiques personnalisés -->
                    @if($evaluation->formatEvaluationUniversite && $evaluation->formatEvaluationUniversite->criteres)
                        @php
                            $criteres = json_decode($evaluation->formatEvaluationUniversite->criteres, true);
                            $nombreCriteres = count($criteres);
                            $noteParCritere = $evaluation->note_totale / $nombreCriteres;
                        @endphp
                        @foreach($criteres as $critere)
                        <tr>
                            <td>{{ $critere }}</td>
                            <td class="criteria-note">{{ number_format($noteParCritere, 1) }}</td>
                            <td class="criteria-max">{{ number_format(20 / $nombreCriteres, 1) }}</td>
                        </tr>
                        @endforeach
                    @else
                        <!-- Critères par défaut si pas de format spécifique -->
                        <tr>
                            <td>Évaluation globale</td>
                            <td class="criteria-note">{{ $evaluation->note_totale }}</td>
                            <td class="criteria-max">20,0</td>
                        </tr>
                    @endif
                @endif
                
                <!-- Ligne total -->
                <tr class="total-row">
                    <td><strong>TOTAL</strong></td>
                    <td class="criteria-note"><strong>{{ $evaluation->note_totale }}</strong></td>
                    <td class="criteria-max"><strong>20,0</strong></td>
                </tr>
            </tbody>
        </table>

        <!-- Commentaires -->
        @if($evaluation->commentaires)
        <div class="comments-section">
            <h4 class="font-bold">Commentaires du maître de stage :</h4>
            <p>{{ $evaluation->commentaires }}</p>
        </div>
        @endif

        <!-- Informations d'évaluation -->
        <div class="info-section">
            <table class="info-table">
                <tr>
                    <th>Date d'évaluation :</th>
                    <td>{{ \Carbon\Carbon::parse($evaluation->created_at)->locale('fr')->isoFormat('DD MMMM YYYY') }}</td>
                </tr>
                <tr>
                    <th>Évaluateur :</th>
                    <td>{{ $evaluation->agent->user->prenom }} {{ $evaluation->agent->user->nom }}</td>
                </tr>
                @if($stage->type === 'Académique')
                <tr>
                    <th>Statut validation RU :</th>
                    <td>
                        @if($evaluation->validee_par_ru)
                            <span class="text-blue font-bold">✓ Validée</span>
                        @else
                            <span style="color: #f59e0b; font-weight: bold;">⏳ En attente de validation</span>
                        @endif
                    </td>
                </tr>
                @endif
            </table>
        </div>
    </div>
    @else
    <div class="evaluation-section">
        <h3 class="evaluation-title">ÉVALUATION</h3>
        <div class="comments-section">
            <p class="text-center" style="color: #ef4444; font-weight: bold;">
                ⚠️ Aucune évaluation disponible pour ce stage
            </p>
        </div>
    </div>
    @endif

    <!-- Signatures -->
    <div class="signature-section" style="margin-top: 50px;">
        <div class="signature-box">
            <p><strong>Le Maître de Stage</strong></p>
            <br><br><br>
            <p>Signature et cachet</p>
        </div>
        <div class="signature-box">
            <p><strong>Le Responsable Université</strong></p>
            <br><br><br>
            <p>Signature et cachet</p>
        </div>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        <p>
            <strong>Ministère de l'Économie et des Finances du Bénin</strong> - 
            Direction de la Planification et de l'Administration Financière (DPAF)<br>
            Document généré le {{ \Carbon\Carbon::now()->locale('fr')->isoFormat('DD MMMM YYYY à HH:mm') }}
        </p>
    </div>
</body>
</html>
