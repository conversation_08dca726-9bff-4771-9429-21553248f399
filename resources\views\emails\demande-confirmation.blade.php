<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmation de votre demande de stage</title>
    <style>
        /* Styles responsive pour clients email */
        @media only screen and (max-width: 600px) {
            .container { width: 100% !important; padding: 10px !important; }
            .header-logo { height: 60px !important; }
            .code-box { font-size: 18px !important; padding: 12px 20px !important; }
            .content-padding { padding: 20px !important; }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f8fafc; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
    <div class="container" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

        <!-- Header avec logo -->
        <div style="background: linear-gradient(135deg, #475569 0%, #1e40af 100%); padding: 30px; text-align: center; border-radius: 0;">
            <img src="{{ asset('images/logoministere.png') }}" alt="Ministère des Finances" class="header-logo" style="height: 80px; width: auto; margin-bottom: 15px;">
            <h1 style="color: #ffffff; margin: 0; font-size: 24px; font-weight: 600;">Confirmation de demande</h1>
            <p style="color: #e2e8f0; margin: 5px 0 0 0; font-size: 14px;">Programme de Stages - Ministère des Finances</p>
        </div>

        <!-- Contenu principal -->
        <div class="content-padding" style="padding: 40px;">
            <!-- Salutation -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #1e293b; margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">
                    Bonjour {{ $user->prenom }} {{ $user->nom }},
                </h2>
                <p style="color: #475569; margin: 0; line-height: 1.6; font-size: 16px;">
                    Nous vous confirmons la bonne réception de votre demande de stage. Votre dossier est maintenant en cours de traitement.
                </p>
            </div>

            <!-- Code de suivi -->
            <div style="text-align: center; margin: 35px 0;">
                <p style="color: #64748b; margin: 0 0 15px 0; font-size: 14px; font-weight: 500;">VOTRE CODE DE SUIVI</p>
                <div class="code-box" style="display: inline-block; background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); border: 2px solid #3b82f6; border-radius: 12px; padding: 20px 30px; font-family: 'Courier New', monospace; font-size: 24px; font-weight: bold; color: #1e40af; letter-spacing: 2px;">
                    {{ $demande->code_suivi }}
                </div>
                <p style="color: #64748b; margin: 10px 0 0 0; font-size: 12px;">Conservez précieusement ce code pour suivre votre demande</p>
            </div>

            <!-- Détails de la demande -->
            <div style="background-color: #f8fafc; border-radius: 12px; padding: 25px; margin: 30px 0; border-left: 4px solid #3b82f6;">
                <h3 style="color: #1e293b; margin: 0 0 20px 0; font-size: 18px; font-weight: 600;">Détails de votre demande</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; color: #64748b; font-weight: 500; width: 40%;">Type de demande :</td>
                        <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $demande->type }}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #64748b; font-weight: 500;">Nature :</td>
                        <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $demande->nature }}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #64748b; font-weight: 500;">Structure demandée :</td>
                        <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">{{ $demande->structure ? $demande->structure->libelle : 'Non spécifiée' }}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #64748b; font-weight: 500;">Période :</td>
                        <td style="padding: 8px 0; color: #1e293b; font-weight: 600;">du {{ \Carbon\Carbon::parse($demande->date_debut)->format('d/m/Y') }} au {{ \Carbon\Carbon::parse($demande->date_fin)->format('d/m/Y') }}</td>
                    </tr>
                </table>
            </div>

            <!-- Bouton d'action -->
            <div style="text-align: center; margin: 35px 0;">
                <a href="{{ $url }}" style="display: inline-block; background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 10px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3); transition: all 0.3s ease;">
                    📋 Suivre mes demandes
                </a>
            </div>

            <!-- Message de remerciement -->
            <div style="margin-top: 30px; padding-top: 25px; border-top: 1px solid #e2e8f0;">
                <p style="color: #475569; margin: 0; line-height: 1.6; font-size: 15px;">
                    Nous vous remercions de votre confiance et vous tiendrons informé de l'évolution de votre demande par email.
                </p>
                <p style="color: #1e293b; margin: 20px 0 0 0; font-weight: 600;">
                    Cordialement,<br>
                    <span style="color: #3b82f6;">{{ config('app.name') }}</span>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #f1f5f9; padding: 25px; text-align: center; border-top: 1px solid #e2e8f0;">
            <p style="color: #64748b; margin: 0 0 10px 0; font-size: 12px;">
                📧 Ce message est généré automatiquement, merci de ne pas y répondre.
            </p>
            <p style="color: #64748b; margin: 0; font-size: 12px;">
                Si vous n'êtes pas à l'origine de cette demande, veuillez nous contacter immédiatement.
            </p>
            <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e2e8f0;">
                <p style="color: #94a3b8; margin: 0; font-size: 11px;">
                    © {{ date('Y') }} Ministère des Finances - République du Bénin
                </p>
            </div>
        </div>
    </div>
</body>
</html>
