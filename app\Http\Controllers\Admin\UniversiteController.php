<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Universite;
use App\Models\Agent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class UniversiteController extends Controller
{
    /**
     * Affiche la liste des universités
     */
    public function index()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $universites = Universite::with(['responsable.user'])
            ->orderBy('nom_complet')
            ->get();

        return Inertia::render('Admin/Universites/Index', [
            'universites' => $universites,
        ]);
    }

    /**
     * Affiche le formulaire de création d'une université
     */
    public function create()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Récupérer les agents RU disponibles pour être responsables
        $responsablesRU = Agent::with('user')
            ->where('role_agent', 'RU')
            ->get();

        return Inertia::render('Admin/Universites/Create', [
            'responsablesRU' => $responsablesRU,
        ]);
    }

    /**
     * Enregistre une nouvelle université
     */
    public function store(Request $request)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $validated = $request->validate([
            'nom_complet' => 'required|string|max:255|unique:universites',
            'sigle' => 'required|string|max:50|unique:universites',
            'description' => 'nullable|string|max:1000',
            'localisation' => 'required|string|max:255',
            'responsable_id' => 'nullable|exists:agents,id',
            'active' => 'boolean',
        ]);

        // Vérifier que le responsable est bien un agent RU (si fourni)
        if (!empty($validated['responsable_id'])) {
            $agent = Agent::find($validated['responsable_id']);
            if (!$agent || $agent->role_agent !== 'RU') {
                return back()->withErrors(['responsable_id' => 'Le responsable sélectionné doit être un agent RU (Responsable Université).']);
            }
        }

        // Définir active à true par défaut si non fourni
        $validated['active'] = $validated['active'] ?? true;

        // Copier nom_complet vers nom_universite pour compatibilité
        $validated['nom_universite'] = $validated['nom_complet'];

        Universite::create($validated);

        return redirect()->route('admin.universites.index')
            ->with('success', 'Université créée avec succès.');
    }

    /**
     * Affiche les détails d'une université
     */
    public function show(Universite $universite)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $universite->load(['responsable.user', 'stagiaires']);

        return Inertia::render('Admin/Universites/Show', [
            'universite' => $universite,
        ]);
    }

    /**
     * Affiche le formulaire d'édition d'une université
     */
    public function edit(Universite $universite)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Récupérer les agents RU disponibles pour être responsables
        $responsablesRU = Agent::with('user')
            ->where('role_agent', 'RU')
            ->get();

        return Inertia::render('Admin/Universites/Edit', [
            'universite' => $universite,
            'responsablesRU' => $responsablesRU,
        ]);
    }

    /**
     * Met à jour une université
     */
    public function update(Request $request, Universite $universite)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $validated = $request->validate([
            'nom_complet' => 'required|string|max:255|unique:universites,nom_complet,' . $universite->id,
            'sigle' => 'required|string|max:50|unique:universites,sigle,' . $universite->id,
            'description' => 'nullable|string|max:1000',
            'localisation' => 'required|string|max:255',
            'responsable_id' => 'nullable|exists:agents,id',
            'active' => 'boolean',
        ]);

        // Vérifier que le responsable est bien un agent RU (si fourni)
        if (!empty($validated['responsable_id'])) {
            $agent = Agent::find($validated['responsable_id']);
            if (!$agent || $agent->role_agent !== 'RU') {
                return back()->withErrors(['responsable_id' => 'Le responsable sélectionné doit être un agent RU (Responsable Université).']);
            }
        }

        // Copier nom_complet vers nom_universite pour compatibilité
        $validated['nom_universite'] = $validated['nom_complet'];

        $universite->update($validated);

        return redirect()->route('admin.universites.index')
            ->with('success', 'Université mise à jour avec succès.');
    }

    /**
     * Supprime une université
     */
    public function destroy(Universite $universite)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Vérifier s'il y a des stagiaires associés
        if ($universite->stagiaires()->count() > 0) {
            $nombreStagiaires = $universite->stagiaires()->count();
            $message = "⚠️ Suppression impossible - Contrainte d'intégrité des données\n\n" .
                      "Cette université ne peut pas être supprimée car elle est actuellement associée à " .
                      ($nombreStagiaires === 1 ? "1 stagiaire actif" : "{$nombreStagiaires} stagiaires actifs") . ".\n\n" .
                      "📋 Actions recommandées :\n" .
                      "• Réassigner les stagiaires vers une autre université\n" .
                      "• Ou attendre la fin des stages en cours\n" .
                      "• Puis réessayer la suppression\n\n" .
                      "💡 Cette mesure protège l'intégrité des données du système ministériel.";

            return back()->withErrors(['delete' => $message]);
        }

        $universite->delete();

        return redirect()->route('admin.universites.index')
            ->with('success', 'Université supprimée avec succès.');
    }

    /**
     * Active/désactive une université
     */
    public function toggleActive(Universite $universite)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $universite->update(['active' => !$universite->active]);

        $status = $universite->active ? 'activée' : 'désactivée';
        
        return back()->with('success', "Université {$status} avec succès.");
    }
}
