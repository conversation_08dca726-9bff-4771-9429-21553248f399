<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Agent extends Model
{
    use HasFactory;

    const ROLE_DPAF = 'DPAF';
    const ROLE_MS = 'MS';
    const ROLE_RS = 'RS';
    const ROLE_RU = 'RU';  // Responsable Université

    protected $fillable = [
        'user_id',
        'matricule',
        'fonction',
        'role_agent',
        'date_embauche',
        'structure_id', // Ajouté pour gérer la relation avec une structure
        'universite_responsable_id', // Pour les agents RU
        'created_by', // Ajouté pour tracer qui a créé l'agent
    ];

    protected $casts = [
        'date_embauche' => 'datetime',
    ];

    /**
     * Get the user that owns the Agent.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created this agent.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the structure that owns the Agent.
     */
    public function structure(): BelongsTo
    {
        return $this->belongsTo(Structure::class);
    }

    /**
     * Vérifier si un agent MS est dans le périmètre de responsabilité d'un RS
     */
    public static function isInRSPerimeter(Agent $rsAgent, Agent $msAgent): bool
    {
        $rsStructure = Structure::where('responsable_id', $rsAgent->id)->first();

        if (!$rsStructure) {
            return false;
        }

        // Récupérer tous les IDs de structures dans le périmètre du RS
        $getAllSubStructureIds = function($parentId) use (&$getAllSubStructureIds) {
            $ids = [$parentId];
            $children = Structure::where('parent_id', $parentId)->pluck('id');
            foreach ($children as $childId) {
                $ids = array_merge($ids, $getAllSubStructureIds($childId));
            }
            return $ids;
        };

        $allStructureIds = $getAllSubStructureIds($rsStructure->id);

        return in_array($msAgent->structure_id, $allStructureIds);
    }

    /**
     * Get all the structures where this agent is the responsable.
     */
    public function structuresResponsable(): HasMany
    {
        return $this->hasMany(Structure::class, 'responsable_id');
    }

    /**
     * Get the universite for which this agent is the responsable (if any).
     * Relation pour les agents RU qui sont responsables d'une université
     */
    public function universiteResponsable(): BelongsTo
    {
        return $this->belongsTo(Universite::class, 'universite_responsable_id');
    }

    /**
     * Get all the universites where this agent is the responsable.
     * Relation inverse pour les agents RU
     */
    public function universitesGerees(): HasMany
    {
        return $this->hasMany(Universite::class, 'responsable_id');
    }

    /**
     * Get the structure for which this agent is the responsable (if any).
     */
    public function structureResponsable(): BelongsTo
    {
        return $this->belongsTo(Structure::class, 'structure_id');
    }

    /**
     * Méthodes utilitaires pour vérifier les rôles
     */
    public function isDPAF(): bool
    {
        return $this->role_agent === self::ROLE_DPAF;
    }

    public function isMS(): bool
    {
        return $this->role_agent === self::ROLE_MS;
    }

    public function isRS(): bool
    {
        return $this->role_agent === self::ROLE_RS;
    }

    public function isRU(): bool
    {
        return $this->role_agent === self::ROLE_RU;
    }

    /**
     * Obtenir tous les rôles disponibles
     */
    public static function getAllRoles(): array
    {
        return [
            self::ROLE_DPAF,
            self::ROLE_MS,
            self::ROLE_RS,
            self::ROLE_RU,
        ];
    }

    // Définir d'autres relations Eloquent ici ultérieurement (maître de stage, etc.)
}