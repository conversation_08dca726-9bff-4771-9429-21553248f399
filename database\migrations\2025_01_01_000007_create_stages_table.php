<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migration consolidée pour la table stages avec toutes les colonnes nécessaires
     * Inclut stagiaire_id, termine_par_ms, date_confirmation_ms
     */
    public function up(): void
    {
        Schema::create('stages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('demande_stage_id')->constrained('demande_stages')->onDelete('cascade');
            $table->foreignId('stagiaire_id')->nullable()->constrained('stagiaires', 'id_stagiaire')->onDelete('set null');
            $table->foreignId('structure_id')->constrained('structures')->onDelete('cascade');
            $table->foreignId('theme_stage_id')->nullable()->constrained('theme_stages')->onDelete('set null');
            $table->date('date_debut')->nullable();
            $table->date('date_fin')->nullable();
            $table->enum('statut', ['En cours', 'Terminé'])->default('En cours');
            $table->text('documents_stage')->nullable();
            $table->float('note')->nullable();
            $table->enum('type', ['Académique', 'Professionnelle'])->default('Académique');
            
            // Colonnes pour la confirmation de fin de stage par le maître de stage
            $table->boolean('termine_par_ms')->default(false);
            $table->timestamp('date_confirmation_ms')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stages');
    }
};
