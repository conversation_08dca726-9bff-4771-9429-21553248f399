<template>
  <Head title="Dashboard Maître de Stage" />

  <MSLayout>
    <!-- Composant Toast pour les notifications -->
    <AdminToast ref="toast" />
    
    <!-- En-tête amélioré sans glassmorphism -->
    <template #header>
      <div class="flex items-center gap-4 mb-2">
        <div class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-600 text-white rounded-2xl w-18 h-18 flex items-center justify-center shadow-xl shadow-blue-500/30">
              <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        </div>
        <div>
          <h1 class="text-3xl font-black bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent leading-tight">
            Tableau de bord
              </h1>
          <p v-if="structureResponsable" class="text-base text-slate-600 mt-1 font-mono bg-slate-200 px-3 py-1 rounded-lg inline-block">
            Responsable de : {{ structureResponsable.libelle }}
            <span v-if="structureResponsable.sigle" class="text-slate-500">({{ structureResponsable.sigle }})</span>
          </p>
        </div>
      </div>
    </template>

    <!-- Arrière-plan grisé -->
    <div class="min-h-screen bg-gradient-to-br from-gray-100 via-slate-100 to-gray-200">
      <div class="py-8">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
          <!-- Message d'erreur amélioré -->
          <div v-if="error" class="mb-8 bg-gradient-to-r from-red-100 to-rose-100 border-2 border-red-300 text-red-800 p-6 rounded-3xl shadow-xl transform transition-all duration-300 hover:scale-[1.02]">
            <div class="flex items-center gap-4">
              <div class="p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-2xl shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
              <div>
                <p class="font-bold text-lg">Erreur</p>
                <p class="mt-1 font-medium">{{ error }}</p>
              </div>
            </div>
        </div>

          <!-- Informations du maître de stage - thème professionnel -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 mb-6 p-6">
            <div class="flex items-center gap-4">
              <div class="bg-gradient-to-br from-blue-600 to-indigo-700 text-white rounded-xl w-16 h-16 flex items-center justify-center shadow-lg">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              </div>
              <div>
                <h2 class="text-2xl font-bold text-gray-900">
                {{ agent?.user?.nom }} {{ agent?.user?.prenom }}
              </h2>
                <p class="text-gray-600 mt-1 font-medium">{{ agent?.fonction }}</p>
                <p class="text-blue-700 font-semibold mt-2 flex items-center gap-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Maître de Stage
              </p>
                <p v-if="structureResponsable" class="text-blue-700 mt-2 flex items-center gap-2 font-medium">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                  Responsable de : <span class="font-semibold">{{ structureResponsable.libelle }}</span>
                  <span v-if="structureResponsable.sigle" class="text-gray-500">({{ structureResponsable.sigle }})</span>
                </p>
            </div>
          </div>
        </div>

          <!-- Statistiques avec couleurs pleines -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
            <!-- Stages en cours - thème bleu -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-200/50 p-4 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl p-3 shadow-lg">
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <p class="text-sm font-semibold text-blue-700 uppercase tracking-wide mb-1">Stages en cours</p>
                  <p class="text-2xl font-bold text-blue-900">{{ stats.stagesEnCours }}</p>
                </div>
              </div>
            </div>

            <!-- Stages terminés - thème vert -->
            <div class="bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl shadow-sm border border-emerald-200/50 p-4 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl p-3 shadow-lg">
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <p class="text-sm font-semibold text-emerald-700 uppercase tracking-wide mb-1">Stages terminés</p>
                  <p class="text-2xl font-bold text-emerald-900">{{ stats.stagesTermines }}</p>
                </div>
              </div>
            </div>

            <!-- Total stagiaires - thème slate -->
            <div class="bg-gradient-to-br from-slate-50 to-gray-50 rounded-xl shadow-sm border border-slate-200/50 p-4 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="bg-gradient-to-br from-slate-500 to-gray-600 rounded-xl p-3 shadow-lg">
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <p class="text-sm font-semibold text-slate-700 uppercase tracking-wide mb-1">Total stagiaires</p>
                  <p class="text-2xl font-bold text-slate-900">{{ stats.totalStagiaires }}</p>
                </div>
              </div>
            </div>
        </div>

          <!-- Filtres pour les stages - Version professionnelle -->
          <div class="bg-white rounded-xl shadow-sm border border-gray-100 mb-6">
            <div class="p-6">
              <div class="flex flex-wrap items-center justify-between gap-4 mb-4">
                <h2 class="text-xl font-semibold text-gray-900">
                Mes stages
              </h2>
                <Link :href="route('agent.ms.stages')"
                  class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200">
                  Voir tous
                </Link>
              </div>

              <div class="flex flex-wrap gap-3">
                <select
                  v-model="filters.statut"
                  class="bg-white border border-gray-300 rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors duration-200 text-gray-700"
                >
                  <option value="">Tous les statuts</option>
                  <option value="En cours">En cours</option>
                  <option value="Terminé">Terminés</option>
                  <option value="En attente">En attente</option>
                </select>
                <input
                  v-model="filters.search"
                  type="text"
                  placeholder="Rechercher un stagiaire..."
                  class="bg-white border border-gray-300 rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors duration-200 text-gray-700 placeholder-gray-400"
                />
                <button
                  @click="resetFilters"
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 font-medium"
                >
                  Réinitialiser
                </button>
              </div>
              </div>
            </div>

          <!-- Tableau des stages - Version professionnelle -->
          <div v-if="filteredStages.length === 0" class="text-center py-12">
            <div class="bg-gray-50 rounded-lg p-8 border border-gray-100">
              <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-gray-600 text-lg font-medium">Aucun stage correspondant aux critères de recherche.</p>
            </div>
          </div>
          <div v-else class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y-2 divide-indigo-200">
                <thead class="bg-gradient-to-r from-indigo-100 to-blue-100">
                  <tr>
                    <th scope="col" class="px-8 py-6 text-left text-sm font-black text-indigo-800 uppercase tracking-wider">Stagiaire</th>
                    <th scope="col" class="px-8 py-6 text-left text-sm font-black text-indigo-800 uppercase tracking-wider">Structure</th>
                    <th scope="col" class="px-8 py-6 text-left text-sm font-black text-indigo-800 uppercase tracking-wider">Période</th>
                    <th scope="col" class="px-8 py-6 text-left text-sm font-black text-indigo-800 uppercase tracking-wider">Statut</th>
                  </tr>
                </thead>
                <tbody class="bg-indigo-50 divide-y-2 divide-indigo-100">
                  <tr v-for="stage in top5DerniersStages" :key="stage.id" 
                      class="hover:bg-gradient-to-r hover:from-indigo-100 hover:to-blue-100 transition-all duration-300 group"
                      :class="{ 'bg-gradient-to-r from-yellow-100 to-amber-100': stage.est_reaffecte }">
                    <td class="px-8 py-6 whitespace-nowrap">
                      <div class="flex items-center">
                        <div>
                          <div class="text-lg font-bold text-slate-900">
                            <template v-if="stage.demandeStage?.stagiaire?.user?.nom">
                              {{ stage.demandeStage.stagiaire.user.nom }} {{ stage.demandeStage.stagiaire.user.prenom }}
                            </template>
                            <template v-else>
                              <span class="italic text-slate-500 font-medium">Nom non disponible</span>
                            </template>
                          </div>
                          <div class="text-base text-slate-600 font-medium">
                            <template v-if="stage.demandeStage?.stagiaire?.user?.email">
                              {{ stage.demandeStage.stagiaire.user.email }}
                            </template>
                            <template v-else>
                              <span class="italic">Email non disponible</span>
                            </template>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap">
                      <div class="text-lg font-bold text-slate-900">{{ stage.structure?.libelle }}</div>
                      <div class="text-base text-slate-600 font-medium">{{ stage.structure?.sigle }}</div>
                      <div v-if="stage.est_reaffecte && stage.reaffectation_info" class="mt-3 bg-gradient-to-r from-yellow-100 to-amber-100 p-4 rounded-2xl border-2 border-yellow-300 shadow-lg">
                        <p class="font-bold text-yellow-800">Réaffecté à:</p>
                        <p class="text-slate-800 font-bold">{{ stage.reaffectation_info.nouveau_ms_prenom }} {{ stage.reaffectation_info.nouveau_ms_nom }}</p>
                        <p class="text-slate-700 font-medium">{{ stage.reaffectation_info.structure_libelle }}</p>
                        <p class="text-slate-600 text-sm mt-1">Le {{ formatDate(stage.reaffectation_info.date_reaffectation) }}</p>
                      </div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap">
                      <div class="text-lg font-bold text-slate-900">
                        {{ formatDate(stage.date_debut) }} - {{ formatDate(stage.date_fin) }}
                      </div>
                      <div class="text-base text-slate-600 font-medium">
                        {{ calculateDuration(stage.date_debut, stage.date_fin) }}
                      </div>
                    </td>
                    <td class="px-8 py-6 whitespace-nowrap">
                      <span class="px-4 py-2 text-base font-bold rounded-2xl shadow-lg"
                        :class="{
                          'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-2 border-blue-300': stage.statut === 'Terminé',
                          'bg-gradient-to-r from-blue-100 to-sky-100 text-blue-800 border-2 border-blue-300': stage.statut === 'En cours',
                          'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 border-2 border-yellow-300': stage.statut === 'En attente',
                        }">
                          {{ stage.statut }}
                        </span>
                      <span v-if="stage.est_reaffecte" class="mt-3 block px-4 py-2 bg-gradient-to-r from-gray-100 to-slate-100 text-slate-800 border-2 border-slate-300 rounded-2xl font-bold text-sm shadow-lg">
                        Réaffecté
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MSLayout>
</template>

<script setup>
import { Head, router, usePage } from '@inertiajs/vue3';
import MSLayout from '@/Layouts/MSLayout.vue';
import { ref, computed, onMounted } from 'vue';
import axios from 'axios';
import AdminToast from '@/Components/AdminToast.vue';
import { Link } from '@inertiajs/vue3';

const props = defineProps({
  stats: Object,
  derniersStages: Array,
  agent: Object,
  structureResponsable: Object,
  error: String
});

// Référence au composant Toast
const toast = ref(null);

// Vérifier les messages flash au chargement de la page
onMounted(() => {
  // Vérifier si des messages flash existent au chargement
  setTimeout(() => {
    const { flash } = usePage().props;
    if (flash) {
      if (flash.success && toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Succès',
          message: flash.success
        });
      }

      if (flash.error && toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur',
          message: flash.error
        });
      }
    }
  }, 100); // Petit délai pour s'assurer que le composant est monté
});

// État pour les filtres
const filters = ref({
  statut: '',
  search: ''
});

// État pour les modals
const showStageDetailsModal = ref(false);
const showContactModal = ref(false);
const showReaffectationModal = ref(false);
const selectedStage = ref(null);
const contactForm = ref({
  subject: '',
  message: ''
});

// État pour la réaffectation
const loading = ref(false);
const errorMsg = ref('');
const maitresStage = ref([]);
const reaffectationForm = ref({
  nouveauMaitreStageId: ''
});

// État pour la boîte de dialogue de confirmation
const showConfirmDialog = ref(false);
const confirmAction = ref(null);
const confirmMessage = ref('');

// Fonction pour formater les dates
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR');
};

// Fonction pour calculer la durée entre deux dates
const calculateDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return 'N/A';

  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 30) {
    return `${diffDays} jour${diffDays > 1 ? 's' : ''}`;
  } else {
    const months = Math.floor(diffDays / 30);
    return `${months} mois`;
  }
};

// Filtrer les stages en fonction des critères de recherche
const filteredStages = computed(() => {
  // S'assurer que derniersStages est un tableau
  const stages = Array.isArray(props.derniersStages) ? props.derniersStages :
                (props.derniersStages ? Object.values(props.derniersStages) : []);

  if (!stages.length) return [];

  return stages.filter(stage => {
    // Filtrer par statut si un statut est sélectionné
    if (filters.value.statut && stage.statut !== filters.value.statut) {
      return false;
    }
    
    // Filtrer par recherche si une recherche est effectuée
    if (filters.value.search) {
      const searchTerm = filters.value.search.toLowerCase();
      const stagiaireNom = stage.demandeStage?.stagiaire?.user?.nom?.toLowerCase() || '';
      const stagiairePrenom = stage.demandeStage?.stagiaire?.user?.prenom?.toLowerCase() || '';
      const stagiaireEmail = stage.demandeStage?.stagiaire?.user?.email?.toLowerCase() || '';
      const structureLibelle = stage.structure?.libelle?.toLowerCase() || '';
      const structureSigle = stage.structure?.sigle?.toLowerCase() || '';
      
      return stagiaireNom.includes(searchTerm) || 
             stagiairePrenom.includes(searchTerm) || 
             stagiaireEmail.includes(searchTerm) ||
             structureLibelle.includes(searchTerm) ||
             structureSigle.includes(searchTerm);
    }
    
    return true;
  });
});

// Remplacer top3StagesEnCours par top5DerniersStages
const top5DerniersStages = computed(() => {
  return filteredStages.value
    .slice()
    .sort((a, b) => new Date(b.date_debut) - new Date(a.date_debut))
    .slice(0, 5);
});

// Réinitialiser les filtres
const resetFilters = () => {
  filters.value = {
    statut: '',
    search: ''
  };
};

// Afficher les détails d'un stage
const viewStageDetails = (stage) => {
  selectedStage.value = stage;
  showStageDetailsModal.value = true;
};

// Contacter un stagiaire
const contactStagiaire = (stage) => {
  selectedStage.value = stage;
  contactForm.value = {
    subject: `Stage - ${stage.structure?.libelle || 'Non spécifié'}`,
    message: ''
  };
  showContactModal.value = true;
};

// Envoyer un message au stagiaire
const sendMessage = async () => {
  let loadingToastId = null;

  try {
    // Afficher un indicateur de chargement
    if (toast.value) {
      loadingToastId = toast.value.addToast({
        type: 'info',
        title: 'Envoi en cours',
        message: 'Envoi du message en cours...',
        duration: 0 // Ne pas disparaître automatiquement
      });
    }

    // Envoyer le message via la route web
    const response = await axios.post(route('agent.ms.stages.send-message'), {
      stage_id: selectedStage.value.id,
      subject: contactForm.value.subject,
      message: contactForm.value.message
    });

    // Vérifier la réponse
    if (response.data.success) {
      // Fermer la modal
      showContactModal.value = false;

      // Réinitialiser le formulaire
      contactForm.value = {
        subject: '',
        message: ''
      };

      // Afficher un toast de succès
      if (toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Message envoyé',
          message: `Message envoyé à ${selectedStage.value.demandeStage?.stagiaire?.user?.email} avec succès !`
        });
      }
    } else {
      throw new Error(response.data.message || 'Une erreur est survenue lors de l\'envoi du message.');
    }
  } catch (error) {
    console.error('Erreur lors de l\'envoi du message:', error);

    // Afficher un toast d'erreur
    if (toast.value) {
      toast.value.addToast({
        type: 'error',
        title: 'Erreur',
        message: error.response?.data?.message || error.message || 'Une erreur est survenue lors de l\'envoi du message.'
      });
    }
  } finally {
    // Supprimer le toast de chargement dans tous les cas
    if (loadingToastId !== null && toast.value) {
      setTimeout(() => {
        toast.value.removeToast(loadingToastId);
      }, 100); // Petit délai pour s'assurer que la suppression fonctionne
    }
  }
};

// Mettre à jour le statut d'un stage
const updateStageStatus = (stage, newStatus) => {
  // Utiliser la boîte de dialogue de confirmation personnalisée
  showConfirm(`Êtes-vous sûr de vouloir marquer ce stage comme ${newStatus} ?`, () => {
    // Dans une vraie implémentation, vous feriez une requête API ici
    router.post(route('agent.ms.update-stage-status', { stage: stage.id }), {
      statut: newStatus
    }, {
      onSuccess: () => {
        // Afficher un toast de succès
        if (toast.value) {
          toast.value.addToast({
            type: 'success',
            title: 'Succès',
            message: `Le statut du stage a été mis à jour avec succès.`
          });
        }

        // Fermer la modal si elle est ouverte
        if (showStageDetailsModal.value) {
          showStageDetailsModal.value = false;
        }

        // Recharger la page pour voir les changements
        router.reload();
      },
      onError: (errors) => {
        console.error('Erreur lors de la mise à jour du statut du stage:', errors);

        // Afficher un toast d'erreur
        if (toast.value) {
          toast.value.addToast({
            type: 'error',
            title: 'Erreur',
            message: 'Une erreur est survenue lors de la mise à jour du statut du stage.'
          });
        }
      }
    });
  });
};

// Ouvrir le modal de réaffectation
const openReaffectationModal = (stage) => {
  selectedStage.value = stage;
  reaffectationForm.value.nouveauMaitreStageId = '';
  errorMsg.value = '';
  showReaffectationModal.value = true;

  // Ne charger les maîtres de stage que si le stage n'a pas été réaffecté
  if (!stage.est_reaffecte) {
    loadMaitresStage(stage.id);
  }
};

// Charger les maîtres de stage des sous-structures
const loadMaitresStage = async (stageId) => {
  loading.value = true;
  errorMsg.value = '';
  maitresStage.value = [];

  try {
    const response = await axios.get(route('agent.ms.stages.maitres-stage-substructures', stageId));

    if (response.data.success) {
      maitresStage.value = response.data.maitresStage;
  } else {
      errorMsg.value = response.data.message || 'Une erreur est survenue lors du chargement des maîtres de stage.';
    }
  } catch (error) {
    console.error('Erreur lors du chargement des maîtres de stage:', error);
    errorMsg.value = error.response?.data?.message || 'Une erreur est survenue lors du chargement des maîtres de stage.';
  } finally {
    loading.value = false;
  }
};

// Fonction pour afficher la boîte de dialogue de confirmation
const showConfirm = (message, action) => {
  confirmMessage.value = message;
  confirmAction.value = action;
  showConfirmDialog.value = true;
};

// Fonction pour exécuter l'action de confirmation
const executeConfirmAction = () => {
  if (confirmAction.value) {
    confirmAction.value();
  }
  showConfirmDialog.value = false;
};

// Réaffecter le stage
const reaffecterStage = () => {
  if (!reaffectationForm.value.nouveauMaitreStageId) {
    errorMsg.value = 'Veuillez sélectionner un maître de stage.';
    // Afficher un toast d'erreur
    if (toast.value) {
      toast.value.addToast({
        type: 'error',
        title: 'Erreur',
        message: 'Veuillez sélectionner un maître de stage.'
      });
    }
    return;
  }

  // Utiliser la boîte de dialogue de confirmation personnalisée
  showConfirm('Êtes-vous sûr de vouloir réaffecter ce stage ? Cette action est irréversible.', () => {
    router.post(route('agent.ms.stages.reaffecter', selectedStage.value.id), {
      nouveau_maitre_stage_id: reaffectationForm.value.nouveauMaitreStageId
    }, {
      onSuccess: () => {
        // Afficher un toast de succès
        if (toast.value) {
          toast.value.addToast({
            type: 'success',
            title: 'Succès',
            message: 'Le stage a été réaffecté avec succès.'
          });
        }

        // Fermer les modals
        showReaffectationModal.value = false;
        showStageDetailsModal.value = false;

        // Recharger la page pour voir les changements
        router.reload();
      },
      onError: (errors) => {
        console.error('Erreur lors de la réaffectation du stage:', errors);
        errorMsg.value = 'Une erreur est survenue lors de la réaffectation du stage.';

        // Afficher un toast d'erreur
        if (toast.value) {
          toast.value.addToast({
            type: 'error',
            title: 'Erreur',
            message: 'Une erreur est survenue lors de la réaffectation du stage.'
          });
        }
      }
    });
  });
};
</script>