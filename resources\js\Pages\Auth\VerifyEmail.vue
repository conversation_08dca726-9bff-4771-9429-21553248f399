<script setup>
import { computed, ref } from 'vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';

const props = defineProps({
    status: {
        type: String,
    },
    email: {
        type: String,
    }
});

const form = useForm({});
const isLoading = ref(false);
const logoUrl = '/images/logoministere.png';

const submit = () => {
    isLoading.value = true;
    form.post(route('verification.send'), {
        onFinish: () => {
            isLoading.value = false;
        },
    });
};

const verificationLinkSent = computed(
    () => props.status === 'verification-link-sent',
);
</script>

<template>
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-slate-100/50 to-blue-50">
        <!-- Arrière-plan décoratif -->
        <div class="fixed inset-0 overflow-hidden pointer-events-none">
            <div class="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-slate-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse"></div>
            <div class="absolute bottom-1/4 -left-32 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-slate-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div class="relative z-10 min-h-screen flex flex-col justify-center items-center px-4 py-8">
            <div class="max-w-lg w-full">
                <Head title="Vérification d'email" />

                <!-- Logo et titre modernisés -->
                <div class="mb-8 text-center">
                    <div class="flex justify-center mb-6">
                        <div class="relative">
                            <div class="absolute inset-0 bg-white/20 backdrop-blur-xl rounded-3xl blur-sm"></div>
                            <div class="relative bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
                                <img :src="logoUrl" alt="MINISTERE DE L'ECONOMIE ET DES FINANCES" class="h-20 w-auto mx-auto transition-transform duration-300 hover:scale-105" />
                            </div>
                        </div>
                    </div>
                    <h1 class="text-3xl font-bold text-slate-800 mb-2">Vérification d'email</h1>
                    <p class="text-slate-600 font-medium">Programme de Stages - Ministère des Finances</p>
                    <p class="text-slate-500 text-sm mt-1">RÉPUBLIQUE DU BÉNIN</p>
                </div>

                <!-- Carte principale modernisée -->
                <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-white/20 transition-all duration-300 hover:shadow-3xl">
                    <!-- Icône d'email modernisée -->
                    <div class="flex justify-center mb-6">
                        <div class="relative">
                            <div class="absolute inset-0 bg-blue-500/20 rounded-full blur-xl"></div>
                            <div class="relative p-4 rounded-full bg-gradient-to-br from-blue-500/20 to-slate-500/20 backdrop-blur-xl border border-blue-400/30">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Message principal modernisé -->
                    <div class="mb-8 text-center space-y-4">
                        <h2 class="text-xl font-semibold text-slate-800">
                            Merci pour votre inscription !
                        </h2>
                        <p class="text-slate-600 leading-relaxed">
                            Avant de commencer votre parcours dans le Programme de Stages, veuillez vérifier votre adresse email en cliquant sur le lien que nous venons de vous envoyer.
                        </p>
                        <div class="bg-slate-50/50 rounded-2xl p-4 border border-slate-200/50">
                            <p class="text-slate-600 text-sm">
                                Si vous n'avez pas reçu l'email, vérifiez votre dossier spam ou cliquez sur le bouton ci-dessous pour recevoir un nouveau lien.
                            </p>
                        </div>
                    </div>

                    <!-- Message de succès modernisé -->
                    <div
                        v-if="verificationLinkSent"
                        class="mb-6 p-4 rounded-2xl bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 backdrop-blur-xl"
                    >
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="p-1 rounded-full bg-green-500/20">
                                    <svg class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">
                                    Un nouveau lien de vérification a été envoyé à votre adresse email.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire modernisé -->
                    <form @submit.prevent="submit">
                        <div class="space-y-4">
                            <button
                                type="submit"
                                class="w-full flex justify-center items-center py-4 px-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 transition-all duration-300 transform hover:scale-[1.02]"
                                :class="{ 'opacity-75 cursor-not-allowed': form.processing || isLoading }"
                                :disabled="form.processing || isLoading"
                            >
                                <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>{{ isLoading ? 'Envoi en cours...' : 'Renvoyer l\'email de vérification' }}</span>
                            </button>

                            <Link
                                :href="route('logout')"
                                method="post"
                                as="button"
                                class="w-full text-center py-3 px-6 border border-slate-300 rounded-2xl shadow-sm text-slate-700 bg-white/50 backdrop-blur-xl hover:bg-slate-50 focus:outline-none focus:ring-4 focus:ring-slate-500/20 transition-all duration-300"
                            >
                                Se déconnecter
                            </Link>
                        </div>
                    </form>
                </div>

                <!-- Aide supplémentaire modernisée -->
                <div class="mt-8 text-center">
                    <div class="bg-white/50 backdrop-blur-xl rounded-2xl p-4 border border-white/20">
                        <p class="text-sm text-slate-600">
                            Besoin d'aide ?
                            <a href="#" class="font-medium text-blue-600 hover:text-blue-800 hover:underline transition duration-200">
                                Contactez notre support
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Animation pour le bouton */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Animation pour le loading */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>