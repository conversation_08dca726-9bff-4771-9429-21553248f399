<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle évaluation académique à valider</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #10b981;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        .ministry-title {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin: 5px 0;
            text-transform: uppercase;
        }
        .content {
            margin: 25px 0;
        }
        .greeting {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .message {
            font-size: 14px;
            line-height: 1.8;
            margin-bottom: 20px;
            text-align: justify;
        }
        .urgent-box {
            background-color: #fef3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .urgent-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        .stage-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px dotted #dee2e6;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
            flex: 1;
        }
        .detail-value {
            color: #6c757d;
            flex: 2;
            text-align: right;
        }
        .evaluation-section {
            background-color: #e8f5e8;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .evaluation-title {
            font-size: 18px;
            font-weight: bold;
            color: #065f46;
            margin-bottom: 15px;
            text-align: center;
        }
        .criteria-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin: 15px 0;
        }
        .criteria-item {
            display: contents;
        }
        .criteria-label {
            font-weight: 500;
            color: #374151;
            padding: 8px 0;
            border-bottom: 1px solid #d1fae5;
        }
        .criteria-score {
            font-weight: bold;
            color: #065f46;
            text-align: right;
            padding: 8px 0;
            border-bottom: 1px solid #d1fae5;
        }
        .total-score {
            background-color: #10b981;
            color: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
        }
        .comment-section {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .comment-title {
            font-weight: bold;
            color: #374151;
            margin-bottom: 10px;
        }
        .comment-text {
            font-style: italic;
            color: #6b7280;
            line-height: 1.6;
        }
        .action-box {
            background-color: #dbeafe;
            border-left: 4px solid #3b82f6;
            padding: 20px;
            margin: 25px 0;
            border-radius: 5px;
        }
        .action-title {
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .action-button {
            display: inline-block;
            background-color: #10b981;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 10px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        .signature {
            margin-top: 25px;
            font-style: italic;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="{{ asset('images/logo-ministere.png') }}" alt="Logo Ministère" class="logo">
            <div class="ministry-title">République du Bénin</div>
            <div class="ministry-title">Ministère de l'Économie et des Finances</div>
            <div class="ministry-title">Secrétariat Général</div>
        </div>

        <div class="content">
            <div class="greeting">
                Bonjour {{ $ruAgent->user->prenom }} {{ $ruAgent->user->nom }},
            </div>

            <div class="urgent-box">
                <div class="urgent-title">⚠️ Action requise - Validation d'évaluation académique</div>
                <div>
                    Une nouvelle évaluation de stage académique nécessite votre validation avant d'être visible 
                    par l'étudiant concerné.
                </div>
            </div>

            <div class="stage-details">
                <h3 style="color: #374151; margin-bottom: 15px;">📋 Informations du stage</h3>
                <div class="detail-row">
                    <span class="detail-label">Étudiant :</span>
                    <span class="detail-value">{{ $stagiaire->user->prenom }} {{ $stagiaire->user->nom }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Université :</span>
                    <span class="detail-value">{{ $stagiaire->universite->nom_complet ?? 'Non spécifiée' }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Structure d'accueil :</span>
                    <span class="detail-value">{{ $stage->structure->libelle }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Période :</span>
                    <span class="detail-value">{{ \Carbon\Carbon::parse($stage->date_debut)->locale('fr')->isoFormat('DD MMMM YYYY') }} - {{ \Carbon\Carbon::parse($stage->date_fin)->locale('fr')->isoFormat('DD MMMM YYYY') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Maître de stage :</span>
                    <span class="detail-value">{{ $evaluation->agent->user->prenom }} {{ $evaluation->agent->user->nom }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Date d'évaluation :</span>
                    <span class="detail-value">{{ \Carbon\Carbon::parse($evaluation->date_evaluation)->locale('fr')->isoFormat('DD MMMM YYYY [à] HH:mm') }}</span>
                </div>
            </div>

            <div class="evaluation-section">
                <div class="evaluation-title">📊 Détail de l'évaluation</div>
                
                @if($evaluation->formatEvaluationUniversite)
                    <div style="text-align: center; margin-bottom: 15px; color: #065f46;">
                        <strong>Format personnalisé : {{ $evaluation->formatEvaluationUniversite->nombre_criteres }} critères × {{ $evaluation->formatEvaluationUniversite->points_par_critere }} points</strong>
                    </div>
                    
                    <div class="criteria-grid">
                        @foreach($evaluation->formatEvaluationUniversite->criteres as $index => $critere)
                            <div class="criteria-item">
                                <div class="criteria-label">{{ $critere }}</div>
                                <div class="criteria-score">{{ $evaluation->criteres_personnalises[$index] ?? 0 }}/{{ $evaluation->formatEvaluationUniversite->points_par_critere }}</div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div style="text-align: center; margin-bottom: 15px; color: #065f46;">
                        <strong>Format standard ministériel (10 critères × 2 points)</strong>
                    </div>
                    
                    <div class="criteria-grid">
                        <div class="criteria-item">
                            <div class="criteria-label">Ponctualité</div>
                            <div class="criteria-score">{{ $evaluation->ponctualite }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Motivation</div>
                            <div class="criteria-score">{{ $evaluation->motivation }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Capacité d'apprentissage</div>
                            <div class="criteria-score">{{ $evaluation->capacite_apprendre }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Qualité du travail</div>
                            <div class="criteria-score">{{ $evaluation->qualite_travail }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Rapidité d'exécution</div>
                            <div class="criteria-score">{{ $evaluation->rapidite_execution }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Jugement</div>
                            <div class="criteria-score">{{ $evaluation->jugement }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Esprit de motivation</div>
                            <div class="criteria-score">{{ $evaluation->esprit_motivation }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Esprit de collaboration</div>
                            <div class="criteria-score">{{ $evaluation->esprit_collaboration }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Sens des responsabilités</div>
                            <div class="criteria-score">{{ $evaluation->sens_responsabilite }}/2</div>
                        </div>
                        <div class="criteria-item">
                            <div class="criteria-label">Communication</div>
                            <div class="criteria-score">{{ $evaluation->communication }}/2</div>
                        </div>
                    </div>
                @endif

                <div class="total-score">
                    Note finale : {{ $evaluation->note_totale }}/20
                </div>

                @if($evaluation->commentaire_general)
                    <div class="comment-section">
                        <div class="comment-title">💬 Commentaire du maître de stage :</div>
                        <div class="comment-text">{{ $evaluation->commentaire_general }}</div>
                    </div>
                @endif
            </div>

            <div class="action-box">
                <div class="action-title">🎯 Action requise</div>
                <div>
                    Veuillez vous connecter à votre espace Responsable Université pour valider cette évaluation 
                    et permettre à l'étudiant de consulter ses notes.
                </div>
                <a href="{{ route('agent.ru.dashboard') }}" class="action-button">
                    Accéder à mon espace RU
                </a>
            </div>

            <div class="signature">
                Cordialement,<br>
                <strong>Le Système de Gestion des Stages</strong><br>
                Ministère de l'Économie et des Finances
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Ministère de l'Économie et des Finances</strong><br>
                Rue du Gouverneur Général Reste - 01 BP 302 Cotonou<br>
                Tél: (+229) 21 30 11 22 | Email: <EMAIL>
            </p>
            <p style="margin-top: 15px; font-style: italic;">
                Cet email a été envoyé automatiquement, merci de ne pas y répondre.
            </p>
        </div>
    </div>
</body>
</html>
