<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test du Système d'Évaluation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-left: 4px solid #10b981;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #10b981;
        }
        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
            margin-top: 5px;
        }
        .section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .section-header {
            background: #f3f4f6;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .section-title {
            font-size: 1.25rem;
            font-weight: bold;
            color: #374151;
            margin: 0;
        }
        .section-content {
            padding: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        .workflow-test {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .workflow-title {
            font-weight: bold;
            color: #374151;
            margin-bottom: 10px;
        }
        .workflow-description {
            color: #6b7280;
            margin-bottom: 15px;
        }
        .workflow-steps {
            background: #f9fafb;
            padding: 15px;
            border-radius: 6px;
        }
        .workflow-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .workflow-steps li {
            margin-bottom: 5px;
            color: #374151;
        }
        .test-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background: #2563eb;
        }
        .btn-success {
            background: #10b981;
            color: white;
        }
        .btn-success:hover {
            background: #059669;
        }
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        .btn-warning:hover {
            background: #d97706;
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .alert-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            color: #1e40af;
        }
        .alert-success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test du Système d'Évaluation Différencié</h1>
            <p>Validation complète du système d'évaluation pour stages académiques et professionnels</p>
        </div>

        <!-- Statistiques générales -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ $stats['total_evaluations'] }}</div>
                <div class="stat-label">Total évaluations</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['evaluations_professionnelles'] }}</div>
                <div class="stat-label">Évaluations professionnelles</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['evaluations_academiques'] }}</div>
                <div class="stat-label">Évaluations académiques</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['evaluations_validees_ru'] }}</div>
                <div class="stat-label">Validées par RU</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['evaluations_en_attente_ru'] }}</div>
                <div class="stat-label">En attente RU</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['moyenne_generale'] }}/20</div>
                <div class="stat-label">Moyenne générale</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['universites_avec_format'] }}</div>
                <div class="stat-label">Universités avec format</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['agents_ru'] }}</div>
                <div class="stat-label">Agents RU</div>
            </div>
        </div>

        <!-- Stages professionnels -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">📋 Stages Professionnels (Format Standard)</h2>
            </div>
            <div class="section-content">
                <div class="alert alert-info">
                    <strong>Format standard :</strong> 10 critères × 2 points = 20 points total<br>
                    <strong>Visibilité :</strong> Note immédiatement visible au stagiaire après évaluation MS
                </div>
                
                @if(count($testData['stages_professionnels']) > 0)
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Stagiaire</th>
                                <th>Structure</th>
                                <th>Évaluation</th>
                                <th>Note</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($testData['stages_professionnels'] as $stage)
                            <tr>
                                <td>{{ $stage['id'] }}</td>
                                <td>{{ $stage['stagiaire'] }}</td>
                                <td>{{ $stage['structure'] }}</td>
                                <td>
                                    <span class="badge {{ $stage['evaluation_existante'] === 'Oui' ? 'badge-success' : 'badge-warning' }}">
                                        {{ $stage['evaluation_existante'] }}
                                    </span>
                                </td>
                                <td>{{ $stage['note'] }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <p class="text-gray-500">Aucun stage professionnel trouvé.</p>
                @endif
            </div>
        </div>

        <!-- Stages académiques -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">🎓 Stages Académiques (Format Personnalisé)</h2>
            </div>
            <div class="section-content">
                <div class="alert alert-info">
                    <strong>Formats disponibles :</strong> 10×2, 5×4, ou 2×10 points = 20 points total<br>
                    <strong>Visibilité :</strong> Note visible au stagiaire SEULEMENT après validation RU
                </div>
                
                @if(count($testData['stages_academiques']) > 0)
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Stagiaire</th>
                                <th>Structure</th>
                                <th>Université</th>
                                <th>Évaluation</th>
                                <th>Note</th>
                                <th>Validée RU</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($testData['stages_academiques'] as $stage)
                            <tr>
                                <td>{{ $stage['id'] }}</td>
                                <td>{{ $stage['stagiaire'] }}</td>
                                <td>{{ $stage['structure'] }}</td>
                                <td>{{ $stage['universite'] }}</td>
                                <td>
                                    <span class="badge {{ $stage['evaluation_existante'] === 'Oui' ? 'badge-success' : 'badge-warning' }}">
                                        {{ $stage['evaluation_existante'] }}
                                    </span>
                                </td>
                                <td>{{ $stage['note'] }}</td>
                                <td>
                                    <span class="badge {{ $stage['validee_ru'] === 'Oui' ? 'badge-success' : 'badge-warning' }}">
                                        {{ $stage['validee_ru'] }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <p class="text-gray-500">Aucun stage académique trouvé.</p>
                @endif
            </div>
        </div>

        <!-- Formats d'évaluation par université -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">🏛️ Formats d'Évaluation par Université</h2>
            </div>
            <div class="section-content">
                @if(count($testData['formats_universites']) > 0)
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Université</th>
                                <th>Critères</th>
                                <th>Points/Critère</th>
                                <th>Total</th>
                                <th>Actif</th>
                                <th>Intitulés</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($testData['formats_universites'] as $format)
                            <tr>
                                <td>{{ $format['id'] }}</td>
                                <td>{{ $format['universite'] }}</td>
                                <td>{{ $format['nombre_criteres'] }}</td>
                                <td>{{ $format['points_par_critere'] }}</td>
                                <td>{{ $format['total_points'] }}</td>
                                <td>
                                    <span class="badge {{ $format['actif'] === 'Oui' ? 'badge-success' : 'badge-warning' }}">
                                        {{ $format['actif'] }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ implode(', ', $format['criteres']) }}</small>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <p class="text-gray-500">Aucun format d'évaluation défini.</p>
                @endif
            </div>
        </div>

        <!-- Tests de workflow -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">🔄 Tests de Workflow</h2>
            </div>
            <div class="section-content">
                @foreach($workflowTests as $test)
                <div class="workflow-test">
                    <div class="workflow-title">{{ $test['test'] }}</div>
                    <div class="workflow-description">{{ $test['description'] }}</div>
                    <div class="workflow-steps">
                        <strong>Étapes à suivre :</strong>
                        <ol>
                            @foreach($test['etapes'] as $etape)
                            <li>{{ $etape }}</li>
                            @endforeach
                        </ol>
                    </div>
                    <div class="badge badge-info">{{ $test['statut'] }}</div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Boutons de test -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">🧪 Tests Automatisés</h2>
            </div>
            <div class="section-content">
                <div class="alert alert-info">
                    <strong>Attention :</strong> Ces tests créent des données de démonstration dans la base de données.
                </div>
                
                <div class="test-buttons">
                    <button onclick="testCreateFormat()" class="btn btn-primary">
                        Créer Format Test
                    </button>
                    <button onclick="testEvaluationAcademique()" class="btn btn-success">
                        Créer Évaluation Académique Test
                    </button>
                    <button onclick="testValidationRU()" class="btn btn-warning">
                        Tester Validation RU
                    </button>
                </div>
                
                <div id="test-results" style="margin-top: 20px;"></div>
            </div>
        </div>
    </div>

    <script>
        async function testCreateFormat() {
            try {
                const response = await fetch('/test-create-format', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                });
                const result = await response.json();
                showResult('Format créé avec succès', result, 'success');
            } catch (error) {
                showResult('Erreur lors de la création du format', error, 'error');
            }
        }

        async function testEvaluationAcademique() {
            try {
                const response = await fetch('/test-evaluation-academique', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                });
                const result = await response.json();
                showResult('Évaluation académique créée avec succès', result, 'success');
            } catch (error) {
                showResult('Erreur lors de la création de l\'évaluation', error, 'error');
            }
        }

        async function testValidationRU() {
            try {
                const response = await fetch('/test-validation-ru', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                });
                const result = await response.json();
                showResult('Validation RU effectuée avec succès', result, 'success');
            } catch (error) {
                showResult('Erreur lors de la validation RU', error, 'error');
            }
        }

        function showResult(message, data, type) {
            const resultsDiv = document.getElementById('test-results');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-warning';
            
            resultsDiv.innerHTML = `
                <div class="alert ${alertClass}">
                    <strong>${message}</strong><br>
                    <pre style="margin-top: 10px; font-size: 0.8rem;">${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        }
    </script>
</body>
</html>
