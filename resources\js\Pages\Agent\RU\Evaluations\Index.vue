<template>
  <ResponsableUniversiteLayout>
    <Head title="Gestion des Évaluations" />

    <!-- En-tête -->
    <template #header>
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Gestion des Évaluations</h1>
          <p class="mt-2 text-gray-600">
            Validation des évaluations de stages académiques - {{ universite?.nom_complet }}
          </p>
        </div>
        <div class="flex items-center gap-4">
          <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
            {{ evaluations.data?.length || 0 }} évaluation(s)
          </div>
        </div>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Statistiques -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total</p>
              <p class="text-2xl font-semibold text-gray-900">{{ evaluations.total || 0 }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-3 bg-yellow-100 rounded-lg">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">En attente</p>
              <p class="text-2xl font-semibold text-gray-900">{{ evaluationsEnAttente }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Validées</p>
              <p class="text-2xl font-semibold text-gray-900">{{ evaluationsValidees }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Moyenne</p>
              <p class="text-2xl font-semibold text-gray-900">{{ moyenneGenerale }}/20</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filtres -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex flex-wrap gap-4">
          <div class="flex-1 min-w-64">
            <label class="block text-sm font-medium text-gray-700 mb-2">Rechercher</label>
            <input v-model="filtres.recherche" 
                   type="text" 
                   placeholder="Nom du stagiaire, structure..."
                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Statut</label>
            <select v-model="filtres.statut" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
              <option value="">Tous</option>
              <option value="en_attente">En attente</option>
              <option value="validee">Validée</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Période</label>
            <select v-model="filtres.periode" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500">
              <option value="">Toutes</option>
              <option value="semaine">Cette semaine</option>
              <option value="mois">Ce mois</option>
              <option value="trimestre">Ce trimestre</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Liste des évaluations -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Évaluations de stages académiques</h3>
        </div>

        <div v-if="evaluationsFiltrees.length === 0" class="p-8 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune évaluation</h3>
          <p class="mt-1 text-sm text-gray-500">Aucune évaluation ne correspond aux critères de recherche.</p>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div v-for="evaluation in evaluationsFiltrees" :key="evaluation.id" 
               class="p-6 hover:bg-gray-50 transition-colors">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-4">
                  <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center">
                      <span class="text-emerald-600 font-semibold text-lg">
                        {{ evaluation.stage?.stagiaire?.user?.prenom?.charAt(0) }}{{ evaluation.stage?.stagiaire?.user?.nom?.charAt(0) }}
                      </span>
                    </div>
                  </div>
                  <div class="flex-1">
                    <h4 class="text-lg font-semibold text-gray-900">
                      {{ evaluation.stage?.stagiaire?.user?.prenom }} {{ evaluation.stage?.stagiaire?.user?.nom }}
                    </h4>
                    <p class="text-sm text-gray-600">
                      {{ evaluation.stage?.structure?.libelle }}
                    </p>
                    <div class="flex items-center gap-4 mt-2">
                      <span class="text-sm text-gray-500">
                        Évalué le {{ formatDate(evaluation.date_evaluation) }}
                      </span>
                      <span class="text-sm font-medium text-emerald-600">
                        Note : {{ evaluation.note_totale }}/20
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center gap-3">
                <!-- Statut -->
                <span v-if="evaluation.validee_par_ru" 
                      class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                  Validée
                </span>
                <span v-else 
                      class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full">
                  En attente
                </span>

                <!-- Actions -->
                <div class="flex gap-2">
                  <Link :href="route('agent.ru.evaluations.show', evaluation.id)"
                        class="btn btn-primary btn-sm">
                    Consulter
                  </Link>
                  <button v-if="!evaluation.validee_par_ru"
                          @click="ouvrirModalValidation(evaluation)"
                          class="btn btn-success btn-sm">
                    Valider
                  </button>
                  <a :href="route('agent.ru.evaluations.pdf', evaluation.id)"
                     target="_blank"
                     class="btn btn-secondary btn-sm">
                    PDF
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="evaluations.links" class="px-6 py-4 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700">
              Affichage de {{ evaluations.from }} à {{ evaluations.to }} sur {{ evaluations.total }} résultats
            </div>
            <div class="flex gap-2">
              <Link v-for="link in evaluations.links" :key="link.label"
                    :href="link.url"
                    v-html="link.label"
                    :class="[
                      'px-3 py-2 text-sm border rounded-lg',
                      link.active ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    ]">
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de validation -->
    <Modal :show="modalValidation.show" @close="fermerModalValidation">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
          Valider l'évaluation
        </h3>
        <p class="text-gray-600 mb-4">
          Êtes-vous sûr de vouloir valider cette évaluation ? 
          Le stagiaire pourra alors voir sa note.
        </p>
        
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Commentaire RU (optionnel)
          </label>
          <textarea v-model="modalValidation.commentaire" 
                    rows="3" 
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Commentaire du responsable université..."></textarea>
        </div>

        <div class="flex justify-end gap-3">
          <button @click="fermerModalValidation" class="btn btn-secondary">
            Annuler
          </button>
          <button @click="validerEvaluation" :disabled="modalValidation.loading" class="btn btn-success">
            <span v-if="modalValidation.loading">Validation...</span>
            <span v-else>Valider</span>
          </button>
        </div>
      </div>
    </Modal>
  </ResponsableUniversiteLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import ResponsableUniversiteLayout from '@/Layouts/ResponsableUniversiteLayout.vue'
import Modal from '@/Components/Modal.vue'
import axios from 'axios'

const props = defineProps({
  evaluations: Object,
  universite: Object
})

// État du composant
const filtres = ref({
  recherche: '',
  statut: '',
  periode: ''
})

const modalValidation = ref({
  show: false,
  evaluation: null,
  commentaire: '',
  loading: false
})

// Computed
const evaluationsFiltrees = computed(() => {
  let result = props.evaluations.data || []
  
  if (filtres.value.recherche) {
    const terme = filtres.value.recherche.toLowerCase()
    result = result.filter(evaluation =>
      evaluation.stage?.stagiaire?.user?.nom?.toLowerCase().includes(terme) ||
      evaluation.stage?.stagiaire?.user?.prenom?.toLowerCase().includes(terme) ||
      evaluation.stage?.structure?.libelle?.toLowerCase().includes(terme)
    )
  }
  
  if (filtres.value.statut) {
    result = result.filter(evaluation => {
      if (filtres.value.statut === 'validee') return evaluation.validee_par_ru
      if (filtres.value.statut === 'en_attente') return !evaluation.validee_par_ru
      return true
    })
  }
  
  return result
})

const evaluationsEnAttente = computed(() => {
  return (props.evaluations.data || []).filter(evaluation => !evaluation.validee_par_ru).length
})

const evaluationsValidees = computed(() => {
  return (props.evaluations.data || []).filter(evaluation => evaluation.validee_par_ru).length
})

const moyenneGenerale = computed(() => {
  const evaluations = props.evaluations.data || []
  if (evaluations.length === 0) return '0.0'

  const total = evaluations.reduce((sum, evaluation) => sum + evaluation.note_totale, 0)
  return (total / evaluations.length).toFixed(1)
})

// Méthodes
const formatDate = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}

const ouvrirModalValidation = (evaluation) => {
  modalValidation.value = {
    show: true,
    evaluation,
    commentaire: '',
    loading: false
  }
}

const fermerModalValidation = () => {
  modalValidation.value = {
    show: false,
    evaluation: null,
    commentaire: '',
    loading: false
  }
}

const validerEvaluation = async () => {
  modalValidation.value.loading = true
  
  try {
    await axios.post(route('agent.ru.evaluations.valider', modalValidation.value.evaluation.id), {
      commentaire_ru: modalValidation.value.commentaire
    })
    
    // Recharger la page pour mettre à jour les données
    router.reload()
    fermerModalValidation()
  } catch (error) {
    console.error('Erreur lors de la validation:', error)
    alert('Erreur lors de la validation de l\'évaluation')
  } finally {
    modalValidation.value.loading = false
  }
}
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 text-center;
}

.btn-sm {
  @apply px-3 py-1 text-sm;
}

.btn-primary {
  @apply bg-emerald-600 text-white hover:bg-emerald-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700;
}
</style>
