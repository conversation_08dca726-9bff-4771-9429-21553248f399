<script setup>
import { ref, watch, onMounted, computed } from 'vue';

import { Head, useForm, usePage, router } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';
import axios from 'axios';


const props = defineProps({
  agents: Array, // Tableau simple d'agents
});

const page = usePage();

// Computed properties pour les statistiques
const agentsAvecStructure = computed(() => {
  return props.agents.filter(agent => agent.structure_supervised).length;
});

const agentsSansStructure = computed(() => {
  return props.agents.filter(agent => !agent.structure_supervised).length;
});

// Fonctions helper pour l'affichage des noms
const getFullName = (user) => {
  if (!user) return 'Non défini';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  return `${prenom} ${nom}`.trim() || 'Non défini';
};

const getInitials = (user) => {
  if (!user) return 'N';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  const prenomInitial = prenom.charAt(0).toUpperCase();
  const nomInitial = nom.charAt(0).toUpperCase();
  return `${prenomInitial}${nomInitial}` || 'N';
};

const toast = ref(null);
const showModal = ref(false);
const showDeleteModal = ref(false);
const agentToDelete = ref(null);
const editingId = ref(null);
const step = ref(1);

const passwordError = ref('');

// Ajout pour gérer les structures disponibles
const availableStructures = ref([]);

// ✅ Correction ici : déclaration déplacée au-dessus du watch
const form = useForm({
  nom: '',
  prenom: '',
  email: '',
  telephone: '',
  date_de_naissance: '',
  sexe: '',
  matricule: '',
  fonction: '',
  password: '',
  password_confirmation: '',
  date_embauche: '',
  role_agent: '',
  structure_id: null, // Renommé pour plus de clarté
});

// Charger les structures disponibles si le rôle est RS
watch(() => form.role_agent, async (newRole) => {
  if (newRole === 'RS') {
    await loadAvailableStructures();
  } else {
    form.structure_id = null;
  }
});

// Surveiller les messages flash et les afficher automatiquement
onMounted(() => {
  setTimeout(() => {
    const { flash } = page.props;
    if (flash) {
      if (flash.success && toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Succès',
          message: flash.success,
        });
      }
      
      if (flash.error && toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur',
          message: flash.error,
        });
      }
    }
  }, 100);
});

// Ajout d'une fonction pour charger les structures disponibles
async function loadAvailableStructures() {
  try {
    // CORRECTION : Construction manuelle de l'URL pour éviter les problèmes de route()
    const baseUrl = '/admin/structures/available';
    const url = editingId.value
      ? `${baseUrl}?agent_id=${editingId.value}`
      : baseUrl;

    const response = await axios.get(url);
    availableStructures.value = response.data;
  } catch (error) {
    console.error('Erreur lors du chargement des structures disponibles :', error);
    if (toast.value) {
      toast.value.addToast({
        type: 'error',
        title: 'Erreur',
        message: 'Impossible de charger les structures disponibles'
      });
    }
  }
}

function openModal(agent = null) {
  step.value = 1;
  passwordError.value = '';

  if (agent) {
    form.nom = agent.user?.nom || '';
    form.prenom = agent.user?.prenom || '';
    form.email = agent.user?.email || '';
    form.telephone = agent.user?.telephone || '';
    form.date_de_naissance = agent.user?.date_de_naissance || '';
    form.sexe = agent.user?.sexe || '';
    form.matricule = agent.matricule;
    form.fonction = agent.fonction;
    form.date_embauche = agent.date_embauche;
    form.password = '';
    form.password_confirmation = '';
    form.role_agent = agent.role_agent;
    form.structure_id = agent.structure_id;
    editingId.value = agent.id;

    // Charger les structures disponibles si l'agent est RS
    if (agent.role_agent === 'RS') {
      loadAvailableStructures();
    }
  } else {
    form.reset();
    // Définir automatiquement le rôle comme RS pour les nouveaux agents
    form.role_agent = 'RS';
    editingId.value = null;
    // Charger les structures disponibles pour les nouveaux agents RS
    loadAvailableStructures();
  }
  showModal.value = true;
}

function closeModal() {
  showModal.value = false;
  form.reset();
  editingId.value = null;
  step.value = 1;
  passwordError.value = '';
}

function openDeleteModal(agent) {
  agentToDelete.value = agent;
  showDeleteModal.value = true;
}

function closeDeleteModal() {
  showDeleteModal.value = false;
  agentToDelete.value = null;
}

function confirmDelete() {
  if (!agentToDelete.value) return;
  
  destroy(agentToDelete.value.id);
  closeDeleteModal();
}

function validatePasswords() {
  if (editingId.value && !form.password) {
    return true;
  }
  
  if (form.password !== form.password_confirmation) {
    passwordError.value = 'Les mots de passe ne correspondent pas';
    return false;
  }
  
  if (!editingId.value && !form.password) {
    passwordError.value = 'Le mot de passe est obligatoire';
    return false;
  }
  
  if (form.password && form.password.length < 8) {
    passwordError.value = 'Le mot de passe doit contenir au moins 8 caractères';
    return false;
  }
  
  passwordError.value = '';
  return true;
}

function nextStep() {
  if (step.value === 1) {
    if (!validatePasswords()) {
      return;
    }
    step.value = 2;
  }
}

function submit() {
  if (!validatePasswords()) {
    step.value = 1;
    if (toast.value) {
      toast.value.addToast({
        type: 'error',
        title: 'Erreur de validation',
        message: passwordError.value || 'Veuillez vérifier les mots de passe saisis'
      });
    }
    return;
  }

  if (editingId.value) {
    form.put(route('admin.agents.update', editingId.value), {
      preserveScroll: true,
      onSuccess: () => {
        closeModal();

        // Message de succès professionnel pour la mise à jour
        if (toast.value) {
          toast.value.addToast({
            type: 'success',
            title: 'Agent mis à jour avec succès',
            message: `Les informations de ${form.prenom} ${form.nom} ont été mises à jour dans le système.`,
            duration: 5000
          });
        }
      },
      onError: (errors) => {
        handleFormErrors(errors);
      },
    });
  } else {
    form.post(route('admin.agents.store'), {
      preserveScroll: true,
      onSuccess: () => {
        closeModal();

        // Message de succès professionnel pour la création
        if (toast.value) {
          toast.value.addToast({
            type: 'success',
            title: 'Nouvel agent créé avec succès',
            message: `${form.prenom} ${form.nom} a été ajouté comme Responsable de Structure.`,
            duration: 5000
          });
        }
      },
      onError: (errors) => {
        handleFormErrors(errors);
      },
    });
  }
}

function handleFormErrors(errors) {
  if (toast.value) {
    // Compter le nombre total d'erreurs
    const errorCount = Object.keys(errors).length;
    const errorMessages = Object.values(errors);

    if (errorCount === 1) {
      // Une seule erreur - afficher le message spécifique
      const firstError = errorMessages[0];
      const message = Array.isArray(firstError) ? firstError[0] : firstError;

      toast.value.addToast({
        type: 'error',
        title: 'Erreur de validation',
        message: message,
        duration: 6000
      });
    } else if (errorCount > 1) {
      // Plusieurs erreurs - afficher un message général avec le nombre
      toast.value.addToast({
        type: 'error',
        title: `${errorCount} erreurs détectées`,
        message: 'Veuillez corriger les champs marqués en rouge et réessayer.',
        duration: 8000
      });
    }
  }

  // Retourner à l'étape appropriée selon le type d'erreur
  const stepOneFields = ['nom', 'prenom', 'email', 'telephone', 'date_de_naissance', 'sexe', 'password'];
  const hasStepOneError = stepOneFields.some(field => errors[field]);

  if (hasStepOneError && step.value === 2) {
    step.value = 1;
    if (toast.value) {
      toast.value.addToast({
        type: 'info',
        title: 'Retour à l\'étape 1',
        message: 'Des erreurs ont été détectées dans les informations personnelles.',
        duration: 4000
      });
    }
  }
}

function destroy(id) {
  // Trouver l'agent à supprimer pour afficher son nom dans le message
  const agent = props.agents.data.find(a => a.id === id);
  const agentName = agent ? `${agent.prenom} ${agent.nom}` : 'l\'agent';

  router.delete(route('admin.agents.destroy', id), {
    onSuccess: () => {
      if (toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Agent supprimé avec succès',
          message: `${agentName} a été retiré du système de gestion.`,
          duration: 5000
        });
      }
    },
    onError: () => {
      if (toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur lors de la suppression',
          message: 'Impossible de supprimer cet agent. Veuillez réessayer.',
          duration: 6000
        });
      }
    },
  });
}
</script>

<template>
  <Head title="Gestion des agents" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header professionnel -->
        <div class="flex items-center gap-4 mb-8">
          <div class="p-3 bg-blue-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-slate-800">Agents RS</h1>
            <p class="text-slate-600 mt-1">Gestion des Responsables de Structure</p>
          </div>
        </div>

        <AdminToast ref="toast" />

        <!-- Statistiques professionnelles -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-blue-100 rounded-xl">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Total Agents RS</p>
                <p class="text-2xl font-bold text-slate-800">{{ agents.length }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-green-100 rounded-xl">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Avec Structure</p>
                <p class="text-2xl font-bold text-slate-800">{{ agentsAvecStructure }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-amber-100 rounded-xl">
                <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Sans Structure</p>
                <p class="text-2xl font-bold text-slate-800">{{ agentsSansStructure }}</p>
              </div>
            </div>
          </div>
        </div>
        <!-- Interface principale -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <!-- Header avec actions -->
          <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
            <div class="flex justify-between items-center">
              <div>
                <h2 class="text-xl font-bold text-slate-800">Liste des Agents RS</h2>
                <p class="text-sm text-slate-600 mt-1">{{ agents.length }} agent(s) responsable(s) de structure</p>
              </div>
              <button
                @click="openModal()"
                class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-semibold flex items-center gap-2 transition-colors shadow-lg"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                Ajouter un Agent RS
              </button>
            </div>
          </div>

          <!-- État vide -->
          <div v-if="agents.length === 0" class="text-center py-16">
            <div class="w-20 h-20 mx-auto bg-slate-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-slate-800 mb-2">Aucun agent RS enregistré</h3>
            <p class="text-slate-600 mb-6">Commencez par créer votre premier responsable de structure.</p>
            <button
              @click="openModal()"
              class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-semibold flex items-center gap-2 mx-auto transition-colors"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              Créer un Agent RS
            </button>
          </div>
          <!-- Tableau responsive avec scroll horizontal -->
          <div v-else class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="min-w-full">
                <thead class="bg-slate-100">
                  <tr>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[250px]">Agent</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[200px]">Contact</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[120px]">Matricule</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[200px]">Structure Supervisée</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[150px]">Fonction</th>
                    <th class="px-6 py-4 text-center text-sm font-bold text-slate-700 min-w-[150px]">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-200">
                  <tr v-for="agent in agents" :key="agent.id" class="hover:bg-slate-50 transition-colors">
                    <td class="px-6 py-4">
                      <div class="flex items-center gap-4">
                        <div class="flex-shrink-0">
                          <div class="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
                            <span class="text-sm font-bold text-blue-600">
                              {{ getInitials(agent.user) }}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div class="text-sm font-bold text-slate-800">{{ getFullName(agent.user) }}</div>
                          <div class="text-sm text-slate-600 mt-1">{{ agent.fonction || 'Non défini' }}</div>
                        </div>
                      </div>
                    </td>

                    <td class="px-6 py-4">
                      <div>
                        <div class="text-sm font-semibold text-slate-800">{{ agent.user?.email || '-' }}</div>
                        <div class="text-sm text-slate-600 mt-1">{{ agent.user?.telephone || '-' }}</div>
                      </div>
                    </td>

                    <td class="px-6 py-4">
                      <span class="inline-flex items-center px-3 py-1 rounded-lg text-xs font-semibold bg-slate-100 text-slate-700">
                        {{ agent.matricule || 'N/A' }}
                      </span>
                    </td>

                    <td class="px-6 py-4">
                      <div v-if="agent.structure_supervised" class="flex items-center gap-3">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                          <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                          </svg>
                        </div>
                        <div>
                          <div class="text-sm font-semibold text-slate-800">{{ agent.structure_supervised.sigle }}</div>
                          <div class="text-xs text-slate-500">{{ agent.structure_supervised.libelle }}</div>
                        </div>
                      </div>
                      <div v-else class="flex items-center gap-3">
                        <div class="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center">
                          <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                          </svg>
                        </div>
                        <div>
                          <div class="text-sm font-semibold text-amber-700">Non assigné</div>
                          <div class="text-xs text-slate-500">Aucune structure supervisée</div>
                        </div>
                      </div>
                    </td>

                    <td class="px-6 py-4">
                      <div class="text-sm text-slate-700">
                        {{ agent.fonction || 'Non défini' }}
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <div class="flex justify-center gap-3">
                        <button
                          @click="openModal(agent)"
                          class="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg font-semibold flex items-center gap-2 transition-colors"
                          title="Modifier l'agent"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                          </svg>
                          Modifier
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!-- Modale multi-étapes améliorée - Parfaitement centrée -->
        <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-gray-200">
              <!-- En-tête de la modale - Fixe -->
              <div class="sticky top-0 bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 px-6 py-5 flex justify-between items-center rounded-t-xl z-10">
                <div class="flex items-center space-x-4">
                  <div class="bg-white bg-opacity-20 p-3 rounded-lg backdrop-blur-sm">
                    <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-white">
                      {{ editingId ? 'Modification d\'un agent' : 'Création d\'un nouvel agent' }}
                    </h3>
                    <p class="text-blue-100 text-sm mt-1">
                      {{ editingId ? 'Mettre à jour les informations de l\'agent' : 'Ajouter un responsable de structure au système' }}
                    </p>
                  </div>
                </div>
                <button @click="closeModal" class="text-white hover:text-blue-200 transition-colors p-2 hover:bg-white hover:bg-opacity-10 rounded-lg">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

            <!-- Barre de progression moderne -->
            <div class="px-6 pt-6 pb-4 bg-gray-50 border-b border-gray-200">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div :class="[
                    'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300',
                    step === 1 ? 'bg-blue-600 border-blue-600 text-white shadow-lg' :
                    step > 1 ? 'bg-green-500 border-green-500 text-white' : 'bg-white border-gray-300 text-gray-500'
                  ]">
                    <svg v-if="step > 1" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span v-else class="font-semibold">1</span>
                  </div>
                  <div>
                    <p :class="[
                      'font-medium transition-colors',
                      step === 1 ? 'text-blue-600' : step > 1 ? 'text-green-600' : 'text-gray-500'
                    ]">Informations personnelles</p>
                    <p class="text-xs text-gray-500">Identité, contact et sécurité</p>
                  </div>
                </div>

                <!-- Ligne de connexion -->
                <div class="flex-1 mx-4">
                  <div class="h-0.5 bg-gray-200 rounded-full overflow-hidden">
                    <div :class="[
                      'h-full transition-all duration-500 rounded-full',
                      step === 2 ? 'bg-blue-500' : 'bg-gray-200'
                    ]" :style="{ width: step === 2 ? '100%' : '0%' }"></div>
                  </div>
                </div>

                <div class="flex items-center space-x-3">
                  <div :class="[
                    'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300',
                    step === 2 ? 'bg-blue-600 border-blue-600 text-white shadow-lg' : 'bg-white border-gray-300 text-gray-500'
                  ]">
                    <span class="font-semibold">2</span>
                  </div>
                  <div>
                    <p :class="[
                      'font-medium transition-colors',
                      step === 2 ? 'text-blue-600' : 'text-gray-500'
                    ]">Informations professionnelles</p>
                    <p class="text-xs text-gray-500">Fonction et responsabilités</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Contenu scrollable du formulaire -->
            <div class="flex-1 overflow-y-auto px-6 py-6 max-h-[60vh]">
              <form @submit.prevent="submit" class="space-y-8">
              <!-- Étape 1 : Informations personnelles -->
              <div v-if="step === 1" class="space-y-6">
                <!-- Section Identité -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Identité
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Nom *</label>
                      <input v-model="form.nom" type="text" placeholder="Nom de famille"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.nom }"
                        required />
                      <div v-if="form.errors.nom" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.nom }}
                      </div>
                    </div>

                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Prénom *</label>
                      <input v-model="form.prenom" type="text" placeholder="Prénom(s)"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.prenom }"
                        required />
                      <div v-if="form.errors.prenom" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.prenom }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Section Contact -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Informations de contact
                  </h3>
                  <div class="space-y-4">
                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Adresse email *</label>
                      <input v-model="form.email" type="email" placeholder="<EMAIL>"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.email }"
                        required />
                      <div v-if="form.errors.email" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.email }}
                      </div>
                    </div>

                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Numéro de téléphone *</label>
                      <input v-model="form.telephone" type="tel" placeholder="+229 XX XX XX XX"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.telephone }"
                        required />
                      <div v-if="form.errors.telephone" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.telephone }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Section Informations personnelles -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Informations personnelles
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Date de naissance *</label>
                      <input v-model="form.date_de_naissance" type="date"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.date_de_naissance }"
                        required />
                      <div v-if="form.errors.date_de_naissance" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.date_de_naissance }}
                      </div>
                    </div>

                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Genre *</label>
                      <select v-model="form.sexe"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.sexe }"
                        required>
                        <option value="">Sélectionner le genre</option>
                        <option value="Homme">Masculin</option>
                        <option value="Femme">Féminin</option>
                      </select>
                      <div v-if="form.errors.sexe" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.sexe }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Section Sécurité -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Sécurité du compte
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">
                        Mot de passe {{ !editingId ? '*' : '' }}
                      </label>
                      <input v-model="form.password" type="password" placeholder="Saisir le mot de passe"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.password || passwordError }"
                        :required="!editingId" />
                      <div v-if="form.errors.password" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.password }}
                      </div>
                      <div v-if="editingId && !form.errors.password" class="text-gray-500 text-xs">
                        Laissez vide pour conserver le mot de passe actuel
                      </div>
                    </div>

                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">
                        Confirmation {{ !editingId || form.password ? '*' : '' }}
                      </label>
                      <input v-model="form.password_confirmation" type="password" placeholder="Confirmer le mot de passe"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': passwordError }"
                        :required="!editingId || form.password" />
                      <div v-if="passwordError" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ passwordError }}
                      </div>
                      <div v-if="!passwordError && !editingId" class="text-gray-500 text-xs">
                        Minimum 8 caractères requis
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Étape 2 : Informations professionnelles -->
              <div v-if="step === 2" class="space-y-6">
                <!-- Section Identification professionnelle -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"></path>
                    </svg>
                    Identification professionnelle
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Matricule *</label>
                      <input v-model="form.matricule" type="text" placeholder="Ex: MAT-2024-001"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.matricule }"
                        required />
                      <div v-if="form.errors.matricule" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.matricule }}
                      </div>
                    </div>

                    <div class="space-y-1">
                      <label class="block text-sm font-medium text-gray-700">Date d'embauche</label>
                      <input v-model="form.date_embauche" type="date"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.date_embauche }" />
                      <div v-if="form.errors.date_embauche" class="text-red-600 text-sm flex items-center gap-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ form.errors.date_embauche }}
                      </div>
                    </div>
                  </div>

                  <div class="mt-4 space-y-1">
                    <label class="block text-sm font-medium text-gray-700">Fonction *</label>
                    <input v-model="form.fonction" type="text" placeholder="Ex: Directeur, Chef de service, Responsable..."
                      class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      :class="{ 'border-red-300 focus:border-red-500 focus:ring-red-500': form.errors.fonction }"
                      required />
                    <div v-if="form.errors.fonction" class="text-red-600 text-sm flex items-center gap-1">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      {{ form.errors.fonction }}
                    </div>
                  </div>
                </div>

                <!-- Section Rôle et responsabilités -->
                <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    Rôle et responsabilités
                  </h3>

                  <div v-if="editingId" class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start gap-3">
                      <div class="bg-blue-100 rounded-full p-2 flex-shrink-0">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </div>
                      <div>
                        <p class="text-sm font-semibold text-blue-800">Rôle actuel de l'agent</p>
                        <p class="text-sm text-blue-700 mt-1">{{ form.role_agent === 'RS' ? 'Responsable de Structure (RS)' : form.role_agent }}</p>
                        <p class="text-xs text-blue-600 mt-2">Cet agent dispose des privilèges de supervision d'une structure organisationnelle.</p>
                      </div>
                    </div>
                  </div>

                  <div v-else class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-start gap-3">
                      <div class="bg-green-100 rounded-full p-2 flex-shrink-0">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </div>
                      <div>
                        <p class="text-sm font-semibold text-green-800">Rôle automatiquement assigné</p>
                        <p class="text-sm text-green-700 mt-1">Responsable de Structure (RS)</p>
                        <p class="text-xs text-green-600 mt-2">En tant qu'administrateur système, tous les agents que vous créez sont automatiquement désignés comme Responsables de Structure avec les privilèges de supervision appropriés.</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Sélection de la structure pour les Responsables de Structure -->
                <div v-if="form.role_agent === 'RS'" class="space-y-2">
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    <span class="flex items-center gap-2">
                      <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                      </svg>
                      Structure à superviser
                    </span>
                  </label>
                  <select
                    v-model="form.structure_id"
                    class="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    required
                  >
                    <option value="">Choisir une structure à superviser</option>
                    <option
                      v-for="structure in availableStructures"
                      :key="structure.id"
                      :value="structure.id"
                      class="py-2"
                    >
                      {{ structure.libelle }}
                      {{ structure.responsable_id === editingId ? ' (Supervision actuelle)' : '' }}
                    </option>
                  </select>

                  <!-- Message informatif professionnel -->
                  <div v-if="availableStructures.length === 0 && !editingId" class="bg-amber-50 border border-amber-200 rounded-lg p-3">
                    <div class="flex items-start gap-2">
                      <svg class="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                      </svg>
                      <div>
                        <p class="text-sm font-medium text-amber-800">Aucune structure disponible</p>
                        <p class="text-sm text-amber-700 mt-1">Toutes les structures disposent déjà d'un responsable assigné. Veuillez contacter l'administrateur système pour plus d'informations.</p>
                      </div>
                    </div>
                  </div>

                  <!-- Message d'aide -->
                  <div v-else-if="availableStructures.length > 0" class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div class="flex items-start gap-2">
                      <svg class="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div>
                        <p class="text-sm font-medium text-blue-800">Structures disponibles</p>
                        <p class="text-sm text-blue-700 mt-1">Sélectionnez la structure que cet agent supervisera en tant que responsable.</p>
                      </div>
                    </div>
                  </div>

                  <div v-if="form.errors.structure_id" class="text-red-600 text-sm flex items-center gap-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ form.errors.structure_id }}
                  </div>
                </div>
              </div>

              </form>
            </div>

            <!-- Boutons de navigation fixes en bas -->
            <div class="sticky bottom-0 bg-white border-t border-gray-200 px-6 py-5 rounded-b-xl">
              <div class="flex justify-between items-center">
                <button type="button" @click="step > 1 ? step-- : closeModal()"
                  class="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 flex items-center gap-2 font-medium border border-gray-200 hover:border-gray-300">
                  <svg v-if="step > 1" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6 6 18M6 6l12 12" />
                  </svg>
                  {{ step > 1 ? 'Étape précédente' : 'Annuler' }}
                </button>

                <div class="flex items-center gap-3">
                  <button v-if="step < 2" type="button" @click="nextStep()"
                    class="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center gap-2 font-semibold shadow-lg">
                    Étape suivante
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m9 18 6-6-6-6" />
                    </svg>
                  </button>

                  <button v-else type="button" @click="submit()"
                    class="px-8 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center gap-2 font-semibold shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="form.processing">
                    <svg v-if="form.processing" class="animate-spin h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                    <span v-if="editingId">{{ form.processing ? 'Mise à jour en cours...' : 'Mettre à jour l\'agent' }}</span>
                    <span v-else>{{ form.processing ? 'Création en cours...' : 'Créer l\'agent' }}</span>
                    <svg v-if="!form.processing" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                      viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round">
                      <path d="M5 13l4 4L19 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            </div>
          </div>
        </div>

        <!-- Modal de confirmation de suppression -->
        <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
            <div class="px-6 py-4 bg-red-50 border-b border-red-100">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-red-100 rounded-full p-2 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-red-800">Supprimer l'agent</h3>
              </div>
            </div>
            
            <div class="px-6 py-4">
              <p class="text-gray-700 mb-4">
                Voulez-vous vraiment supprimer l'agent "{{ agentToDelete?.user?.prenom || '' }} {{ agentToDelete?.user?.nom || '' }}" ?<br>
                Cette action est irréversible.
              </p>
              
              <div class="flex justify-end space-x-3">
                <button 
                  @click="closeDeleteModal" 
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Annuler
                </button>
                <button 
                  @click="confirmDelete" 
                  class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

    <!-- Composant Toast pour les notifications -->
    <AdminToast ref="toast" />
  </AdminLayout>
</template>