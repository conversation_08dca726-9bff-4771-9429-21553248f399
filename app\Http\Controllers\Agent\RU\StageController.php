<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use App\Models\Stage;
use App\Models\Universite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class StageController extends Controller
{
    /**
     * Affiche la liste des stages des étudiants de l'université
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        // Construire la requête des stages (UNIQUEMENT ACADÉMIQUES pour RU)
        $query = Stage::with([
            'stagiaire.user',
            'structure',
            'evaluation.agent.user',
            'demandeStage'
        ])
        ->whereHas('stagiaire', function ($q) use ($universite) {
            $q->where('universite_id', $universite->id);
        })
        ->where('type', 'Académique'); // RU ne gère que les stages académiques

        // Filtres
        if ($request->filled('statut')) {
            $query->where('statut', $request->statut);
        }

        // Suppression du filtre type car RU ne gère que les stages académiques

        if ($request->filled('periode')) {
            $periode = $request->periode;
            if ($periode === 'en_cours') {
                $query->where('date_debut', '<=', now())
                      ->where('date_fin', '>=', now());
            } elseif ($periode === 'termines') {
                $query->where('date_fin', '<', now());
            } elseif ($periode === 'futurs') {
                $query->where('date_debut', '>', now());
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('stagiaire.user', function ($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                  ->orWhere('prenom', 'like', "%{$search}%");
            });
        }

        // Tri
        $sortField = $request->get('sort', 'date_debut');
        $sortDirection = $request->get('direction', 'desc');
        
        if ($sortField === 'nom') {
            $query->join('stagiaires', 'stages.stagiaire_id', '=', 'stagiaires.id_stagiaire')
                  ->join('users', 'stagiaires.user_id', '=', 'users.id')
                  ->orderBy('users.nom', $sortDirection)
                  ->select('stages.*');
        } elseif ($sortField === 'note') {
            $query->leftJoin('evaluations', 'stages.id', '=', 'evaluations.stage_id')
                  ->orderBy('evaluations.note_totale', $sortDirection)
                  ->select('stages.*');
        } else {
            $query->orderBy($sortField, $sortDirection);
        }

        $stages = $query->paginate(15)->withQueryString();

        // Statistiques (UNIQUEMENT stages académiques)
        $stats = [
            'total' => Stage::whereHas('stagiaire', function ($q) use ($universite) {
                $q->where('universite_id', $universite->id);
            })->where('type', 'Académique')->count(),
            'en_cours' => Stage::whereHas('stagiaire', function ($q) use ($universite) {
                $q->where('universite_id', $universite->id);
            })->where('type', 'Académique')->where('statut', 'En cours')->count(),
            'termines' => Stage::whereHas('stagiaire', function ($q) use ($universite) {
                $q->where('universite_id', $universite->id);
            })->where('type', 'Académique')->where('statut', 'Terminé')->count(),
            'evalues' => Stage::whereHas('stagiaire', function ($q) use ($universite) {
                $q->where('universite_id', $universite->id);
            })->where('type', 'Académique')->whereHas('evaluation')->count(),
        ];

        return Inertia::render('Agent/RU/Stages/Index', [
            'pageTitle' => 'Stages des étudiants',
            'universite' => $universite,
            'stages' => $stages,
            'stats' => $stats,
            'filters' => $request->only(['statut', 'periode', 'search', 'sort', 'direction']),
        ]);
    }

    /**
     * Affiche les détails d'un stage
     */
    public function show(Stage $stage)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        // Vérifier que le stage appartient à un étudiant de cette université
        if ($stage->stagiaire->universite_id !== $universite->id) {
            return redirect()->route('agent.ru.stages.index')
                ->with('error', 'Accès non autorisé à ce stage.');
        }

        // Vérifier que le stage est de type académique (RU ne gère que les stages académiques)
        if ($stage->type !== 'Académique') {
            return redirect()->route('agent.ru.stages.index')
                ->with('error', 'Accès non autorisé. Les RU ne gèrent que les stages académiques.');
        }

        // Charger les relations
        $stage->load([
            'stagiaire.user',
            'structure',
            'evaluation.agent.user',
            'evaluation.formatEvaluationUniversite',
            'affectationMaitreStage.maitreStage',
            'affectationMaitreStage.agentAffectant.user',
            'demandeStage'
        ]);

        return Inertia::render('Agent/RU/Stages/Show', [
            'pageTitle' => 'Détails du stage',
            'universite' => $universite,
            'stage' => $stage,
        ]);
    }

    /**
     * Génère un PDF des notes d'un stage
     */
    public function printNotes(Stage $stage)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return response()->json(['error' => 'Accès non autorisé.'], 403);
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return response()->json(['error' => 'Aucune université assignée.'], 404);
        }

        // Vérifier que le stage appartient à un étudiant de cette université
        if ($stage->stagiaire->universite_id !== $universite->id) {
            return response()->json(['error' => 'Accès non autorisé à ce stage.'], 403);
        }

        // Vérifier que le stage est de type académique (RU ne gère que les stages académiques)
        if ($stage->type !== 'Académique') {
            return response()->json(['error' => 'Accès non autorisé. Les RU ne gèrent que les stages académiques.'], 403);
        }

        // Vérifier qu'il y a une évaluation
        if (!$stage->evaluation) {
            return response()->json(['error' => 'Aucune évaluation disponible pour ce stage.'], 404);
        }

        // Charger les relations nécessaires
        $stage->load([
            'stagiaire.user',
            'structure',
            'evaluation.agent.user',
            'evaluation.formatEvaluationUniversite',
            'affectationMaitreStage.maitreStage',
            'affectationMaitreStage.agentAffectant.user',
            'demandeStage'
        ]);

        // Générer le PDF (à implémenter selon vos besoins)
        $pdf = \PDF::loadView('pdf.stage-notes', [
            'stage' => $stage,
            'universite' => $universite,
            'evaluation' => $stage->evaluation
        ]);

        return $pdf->download("notes-stage-{$stage->stagiaire->user->nom}-{$stage->stagiaire->user->prenom}.pdf");
    }
}
