<template>
  <Head title="Créer une Université" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-emerald-50 min-h-screen">
      <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center gap-4 mb-8">
          <div class="p-3 bg-emerald-600 rounded-xl shadow-lg">
            <AcademicCapIcon class="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-slate-800">Créer une Université</h1>
            <p class="text-slate-600 mt-1">Ajouter un nouvel établissement universitaire</p>
          </div>
        </div>

        <!-- Formulaire -->
        <div class="bg-white rounded-2xl shadow-xl border border-slate-200 overflow-hidden">
          <div class="px-6 py-4 bg-gradient-to-r from-emerald-600 to-green-600 text-white">
            <h2 class="text-xl font-semibold">Informations de l'Université</h2>
            <p class="text-emerald-100 text-sm mt-1">Remplissez tous les champs requis</p>
          </div>

          <form @submit.prevent="submit" class="p-6">
            <div class="space-y-8">
              <!-- Informations générales -->
              <div>
                <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                  Informations générales
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Nom complet *</label>
                    <input 
                      v-model="form.nom_complet" 
                      type="text" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors" 
                      placeholder="Ex: Université d'Abomey-Calavi"
                      required 
                    />
                    <div v-if="form.errors.nom_complet" class="text-red-500 text-sm mt-1">{{ form.errors.nom_complet }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Sigle *</label>
                    <input 
                      v-model="form.sigle" 
                      type="text" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors" 
                      placeholder="Ex: UAC"
                      required 
                    />
                    <div v-if="form.errors.sigle" class="text-red-500 text-sm mt-1">{{ form.errors.sigle }}</div>
                  </div>

                  <div class="md:col-span-2">
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Localisation *</label>
                    <input 
                      v-model="form.localisation" 
                      type="text" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors" 
                      placeholder="Ex: Abomey-Calavi, Atlantique"
                      required 
                    />
                    <div v-if="form.errors.localisation" class="text-red-500 text-sm mt-1">{{ form.errors.localisation }}</div>
                  </div>

                  <div class="md:col-span-2">
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Description</label>
                    <textarea 
                      v-model="form.description" 
                      rows="4"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors" 
                      placeholder="Description de l'université, ses spécialités, son histoire..."
                    ></textarea>
                    <div v-if="form.errors.description" class="text-red-500 text-sm mt-1">{{ form.errors.description }}</div>
                  </div>
                </div>
              </div>

              <!-- Responsable et statut -->
              <div>
                <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                  Responsable et statut
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">
                      Responsable Université (RU)
                    </label>
                    <select
                      v-model="form.responsable_id"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    >
                      <option value="">Sélectionner un responsable (optionnel)</option>
                      <option
                        v-for="responsable in responsablesRU"
                        :key="responsable.id"
                        :value="responsable.id"
                      >
                        {{ responsable.user.prenom }} {{ responsable.user.nom }} ({{ responsable.user.email }})
                      </option>
                    </select>
                    <div v-if="form.errors.responsable_id" class="text-red-500 text-sm mt-1">{{ form.errors.responsable_id }}</div>
                    <p class="text-sm text-slate-500 mt-1">
                      <span class="text-blue-600 font-medium">Optionnel :</span> Seuls les agents avec le rôle RU peuvent être assignés
                    </p>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Statut</label>
                    <div class="flex items-center space-x-4">
                      <label class="flex items-center">
                        <input 
                          v-model="form.active" 
                          type="radio" 
                          :value="true"
                          class="text-emerald-600 focus:ring-emerald-500 border-gray-300"
                        />
                        <span class="ml-2 text-sm text-slate-700">Active</span>
                      </label>
                      <label class="flex items-center">
                        <input 
                          v-model="form.active" 
                          type="radio" 
                          :value="false"
                          class="text-red-600 focus:ring-red-500 border-gray-300"
                        />
                        <span class="ml-2 text-sm text-slate-700">Inactive</span>
                      </label>
                    </div>
                    <div v-if="form.errors.active" class="text-red-500 text-sm mt-1">{{ form.errors.active }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-8 border-t border-slate-200">
              <Link 
                :href="route('admin.universites.index')" 
                class="inline-flex items-center gap-2 px-6 py-3 border border-slate-300 rounded-lg text-slate-700 hover:bg-slate-50 font-medium transition-colors"
              >
                <ArrowLeftIcon class="w-5 h-5" />
                Retour à la liste
              </Link>

              <button 
                type="submit" 
                :disabled="form.processing"
                class="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white px-8 py-3 rounded-lg font-semibold transition-colors shadow-lg hover:shadow-xl"
              >
                <span v-if="form.processing" class="flex items-center gap-2">
                  <svg class="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Création...
                </span>
                <span v-else class="flex items-center gap-2">
                  <PlusIcon class="w-5 h-5" />
                  Créer l'Université
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import {
  AcademicCapIcon,
  ArrowLeftIcon,
  PlusIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  responsablesRU: Array,
});

const form = useForm({
  nom_complet: '',
  sigle: '',
  description: '',
  localisation: '',
  responsable_id: '',
  active: true,
});

function submit() {
  form.post(route('admin.universites.store'));
}
</script>
