<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\DemandeStage;
use App\Models\Structure;

class StructureDemandeAssigneeMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $demande;
    public $structure;
    public $stagiaire;

    /**
     * Create a new message instance.
     */
    public function __construct(DemandeStage $demande, Structure $structure)
    {
        $this->demande = $demande;
        $this->structure = $structure;
        $this->stagiaire = $demande->stagiaire->user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Nouvelle demande de stage assignée - ' . $this->demande->code_suivi,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.structure-demande-assignee',
            with: [
                'demande' => $this->demande,
                'structure' => $this->structure,
                'stagiaire' => $this->stagiaire,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
