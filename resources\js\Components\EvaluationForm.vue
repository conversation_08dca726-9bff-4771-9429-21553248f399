<template>
  <div class="evaluation-form">
    <!-- Choix du format pour stages académiques -->
    <div v-if="stage.type === 'academique' && !formatChoisi" class="format-selection mb-8">
      <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
        <h3 class="text-xl font-bold text-blue-900 mb-4 flex items-center gap-3">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Format d'évaluation pour {{ universite?.nom_complet }}
        </h3>
        
        <!-- Format existant -->
        <div v-if="formatExistant" class="mb-6 p-4 bg-white rounded-xl border border-emerald-200">
          <h4 class="font-semibold text-emerald-800 mb-2">Format existant trouvé :</h4>
          <div class="flex items-center gap-4 text-sm">
            <span class="px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full">
              {{ formatExistant.nombre_criteres }} critères
            </span>
            <span class="px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full">
              {{ formatExistant.points_par_critere }} points chacun
            </span>
            <span class="px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full">
              Total : {{ formatExistant.nombre_criteres * formatExistant.points_par_critere }} points
            </span>
          </div>
          <div class="mt-3">
            <button @click="utiliserFormatExistant" class="btn btn-primary">
              Utiliser ce format
            </button>
            <button @click="creerNouveauFormat" class="btn btn-secondary ml-2">
              Créer un nouveau format
            </button>
          </div>
        </div>

        <!-- Création nouveau format -->
        <div v-if="!formatExistant || modeCreation" class="nouveau-format">
          <h4 class="font-semibold text-blue-800 mb-4">Créer un nouveau format d'évaluation :</h4>
          
          <!-- Choix du type de format -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div v-for="format in formatsDisponibles" :key="`${format.nombre_criteres}-${format.points_par_critere}`"
                 @click="selectionnerFormat(format)"
                 class="format-option p-4 border-2 rounded-xl cursor-pointer transition-all"
                 :class="formatSelectionne?.nombre_criteres === format.nombre_criteres ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ format.nombre_criteres }}</div>
                <div class="text-sm text-gray-600">critères</div>
                <div class="text-lg font-semibold mt-2">{{ format.points_par_critere }} pts chacun</div>
                <div class="text-xs text-gray-500">Total : {{ format.total }} pts</div>
              </div>
            </div>
          </div>

          <!-- Saisie des intitulés des critères -->
          <div v-if="formatSelectionne" class="criteres-input">
            <h5 class="font-semibold mb-3">Définir les intitulés des critères :</h5>
            <div class="space-y-3">
              <div v-for="(critere, index) in nouveauxCriteres" :key="index" class="flex items-center gap-3">
                <span class="w-8 h-8 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-bold">
                  {{ index + 1 }}
                </span>
                <input v-model="nouveauxCriteres[index]" 
                       :placeholder="`Critère ${index + 1} (${formatSelectionne.points_par_critere} points)`"
                       class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              </div>
            </div>
            <div class="mt-4">
              <button @click="sauvegarderFormat" :disabled="!peutSauvegarder" class="btn btn-primary">
                Sauvegarder le format
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Formulaire d'évaluation -->
    <div v-if="formatChoisi" class="evaluation-content">
      <!-- En-tête -->
      <div class="bg-gradient-to-r from-emerald-600 to-green-600 text-white p-6 rounded-t-2xl">
        <h2 class="text-2xl font-bold">Évaluation de stage</h2>
        <p class="mt-2 opacity-90">{{ stage.stagiaire?.user?.prenom }} {{ stage.stagiaire?.user?.nom }}</p>
        <p class="text-sm opacity-75">{{ stage.type === 'professionnel' ? 'Format standard ministériel' : `Format ${universite?.sigle}` }}</p>
      </div>

      <!-- Critères d'évaluation -->
      <div class="bg-white p-6 space-y-6">
        <!-- Stage professionnel : critères standards -->
        <div v-if="stage.type === 'professionnel'" class="criteres-standards">
          <div v-for="(critere, key) in criteresStandards" :key="key" class="critere-item">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ critere.label }} ({{ critere.max }} points)
            </label>
            <div class="flex items-center gap-4">
              <input v-model.number="evaluation[key]" 
                     type="number" 
                     :min="0" 
                     :max="critere.max"
                     step="0.5"
                     class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500">
              <div class="flex-1 bg-gray-200 rounded-full h-2">
                <div class="bg-emerald-500 h-2 rounded-full transition-all duration-300" 
                     :style="`width: ${(evaluation[key] / critere.max) * 100}%`"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Stage académique : critères personnalisés -->
        <div v-else class="criteres-personnalises">
          <div v-for="(critere, index) in formatChoisi.criteres" :key="index" class="critere-item">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ critere }} ({{ formatChoisi.points_par_critere }} points)
            </label>
            <div class="flex items-center gap-4">
              <input v-model.number="evaluationPersonnalisee[index]" 
                     type="number" 
                     :min="0" 
                     :max="formatChoisi.points_par_critere"
                     step="0.5"
                     class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500">
              <div class="flex-1 bg-gray-200 rounded-full h-2">
                <div class="bg-emerald-500 h-2 rounded-full transition-all duration-300" 
                     :style="`width: ${(evaluationPersonnalisee[index] / formatChoisi.points_par_critere) * 100}%`"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Note totale -->
        <div class="note-totale bg-gray-50 p-4 rounded-xl">
          <div class="flex justify-between items-center">
            <span class="text-lg font-semibold">Note totale :</span>
            <span class="text-2xl font-bold text-emerald-600">{{ noteCalculee }} / 20</span>
          </div>
        </div>

        <!-- Commentaire général -->
        <div class="commentaire">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Commentaire général
          </label>
          <textarea v-model="evaluation.commentaire_general" 
                    rows="4" 
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                    placeholder="Commentaire sur la performance globale du stagiaire..."></textarea>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-4 pt-4 border-t">
          <button @click="$emit('cancel')" class="btn btn-secondary">
            Annuler
          </button>
          <button @click="soumettre" :disabled="!peutSoumettre" class="btn btn-primary">
            Enregistrer l'évaluation
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import axios from 'axios'

const props = defineProps({
  stage: Object,
  evaluationExistante: Object
})

const emit = defineEmits(['submit', 'cancel'])

// État du composant
const formatChoisi = ref(null)
const formatExistant = ref(null)
const universite = ref(null)
const formatsDisponibles = ref([])
const formatSelectionne = ref(null)
const nouveauxCriteres = ref([])
const modeCreation = ref(false)

// Données d'évaluation
const evaluation = ref({
  ponctualite: 0,
  motivation: 0,
  capacite_apprendre: 0,
  qualite_travail: 0,
  rapidite_execution: 0,
  jugement: 0,
  esprit_motivation: 0,
  esprit_collaboration: 0,
  sens_responsabilite: 0,
  communication: 0,
  commentaire_general: ''
})

const evaluationPersonnalisee = ref([])

// Critères standards pour stages professionnels
const criteresStandards = {
  ponctualite: { label: 'Ponctualité', max: 2 },
  motivation: { label: 'Motivation', max: 2 },
  capacite_apprendre: { label: 'Capacité d\'apprentissage', max: 2 },
  qualite_travail: { label: 'Qualité du travail', max: 2 },
  rapidite_execution: { label: 'Rapidité d\'exécution', max: 2 },
  jugement: { label: 'Jugement', max: 2 },
  esprit_motivation: { label: 'Esprit de motivation', max: 2 },
  esprit_collaboration: { label: 'Esprit de collaboration', max: 2 },
  sens_responsabilite: { label: 'Sens des responsabilités', max: 2 },
  communication: { label: 'Communication', max: 2 }
}

// Computed
const noteCalculee = computed(() => {
  if (props.stage.type === 'professionnel') {
    return Object.values(evaluation.value).reduce((sum, val) => {
      return sum + (typeof val === 'number' ? val : 0)
    }, 0)
  } else {
    return evaluationPersonnalisee.value.reduce((sum, val) => sum + (val || 0), 0)
  }
})

const peutSauvegarder = computed(() => {
  return formatSelectionne.value && 
         nouveauxCriteres.value.every(c => c && c.trim().length > 0)
})

const peutSoumettre = computed(() => {
  return noteCalculee.value > 0 && noteCalculee.value <= 20
})

// Méthodes
const chargerFormatEvaluation = async () => {
  if (props.stage.type === 'academique') {
    try {
      const response = await axios.get(`/agent/ms/stages/${props.stage.id}/format-evaluation`)
      formatExistant.value = response.data.format_existant
      universite.value = response.data.universite
      formatsDisponibles.value = response.data.formats_disponibles
    } catch (error) {
      console.error('Erreur lors du chargement du format:', error)
    }
  } else {
    formatChoisi.value = { type: 'standard' }
  }
}

const selectionnerFormat = (format) => {
  formatSelectionne.value = format
  nouveauxCriteres.value = Array(format.nombre_criteres).fill('')
}

const utiliserFormatExistant = () => {
  formatChoisi.value = formatExistant.value
  evaluationPersonnalisee.value = Array(formatExistant.value.nombre_criteres).fill(0)
}

const creerNouveauFormat = () => {
  modeCreation.value = true
}

const sauvegarderFormat = async () => {
  try {
    const response = await axios.post(`/agent/ms/stages/${props.stage.id}/format-evaluation`, {
      nombre_criteres: formatSelectionne.value.nombre_criteres,
      points_par_critere: formatSelectionne.value.points_par_critere,
      criteres: nouveauxCriteres.value
    })
    
    formatChoisi.value = response.data.format
    evaluationPersonnalisee.value = Array(formatChoisi.value.nombre_criteres).fill(0)
  } catch (error) {
    console.error('Erreur lors de la sauvegarde du format:', error)
  }
}

const soumettre = () => {
  const data = {
    note_totale: noteCalculee.value,
    commentaire_general: evaluation.value.commentaire_general
  }

  if (props.stage.type === 'professionnel') {
    Object.assign(data, evaluation.value)
  } else {
    data.format_evaluation_universite_id = formatChoisi.value.id
    data.criteres_personnalises = evaluationPersonnalisee.value
  }

  emit('submit', data)
}

// Lifecycle
onMounted(() => {
  chargerFormatEvaluation()
  
  // Charger l'évaluation existante si elle existe
  if (props.evaluationExistante) {
    if (props.stage.type === 'professionnel') {
      Object.assign(evaluation.value, props.evaluationExistante)
    } else {
      evaluationPersonnalisee.value = props.evaluationExistante.criteres_personnalises || []
      formatChoisi.value = props.evaluationExistante.formatEvaluationUniversite
    }
  }
})
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
}

.btn-primary {
  @apply bg-emerald-600 text-white hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}

.critere-item {
  @apply p-4 border border-gray-200 rounded-lg;
}

.format-option:hover {
  @apply transform scale-105;
}
</style>
