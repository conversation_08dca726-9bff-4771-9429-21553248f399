<script setup>
import { computed } from 'vue';

const props = defineProps({
  demande: {
    type: Object,
    required: true
  },
  showDownloadButtons: {
    type: Boolean,
    default: true
  }
});

// Fonction pour obtenir l'icône selon le type de document
const getDocumentIcon = (type) => {
  switch (type) {
    case 'lettre_cv':
      return props.demande.type === 'Académique' 
        ? 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
        : 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z';
    case 'diplomes':
      return 'M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z';
    case 'visage':
      return 'M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z';
    default:
      return 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
  }
};

// Fonction pour obtenir le nom du document selon le type et le type de demande
const getDocumentName = (type) => {
  switch (type) {
    case 'lettre_cv':
      return props.demande.type === 'Académique' ? 'Lettre de recommandation' : 'CV et Lettre de motivation';
    case 'diplomes':
      return 'Diplômes';
    case 'visage':
      return 'Photo d\'identité';
    default:
      return 'Document';
  }
};

// Fonction pour obtenir les classes CSS selon le type de document
const getDocumentClasses = (type) => {
  switch (type) {
    case 'lettre_cv':
      return props.demande.type === 'Académique'
        ? {
            bg: 'from-blue-100 via-indigo-100 to-blue-100',
            border: 'border-blue-200',
            iconBg: 'from-blue-500 via-blue-600 to-indigo-600',
            btnBg: 'from-blue-600 to-indigo-600',
            btnHover: 'hover:from-blue-700 hover:to-indigo-700'
          }
        : {
            bg: 'from-emerald-100 via-green-100 to-emerald-100',
            border: 'border-emerald-200',
            iconBg: 'from-emerald-500 via-emerald-600 to-green-600',
            btnBg: 'from-emerald-600 to-green-600',
            btnHover: 'hover:from-emerald-700 hover:to-green-700'
          };
    case 'diplomes':
      return {
        bg: 'from-amber-100 via-orange-100 to-amber-100',
        border: 'border-amber-200',
        iconBg: 'from-amber-500 via-amber-600 to-orange-600',
        btnBg: 'from-amber-600 to-orange-600',
        btnHover: 'hover:from-amber-700 hover:to-orange-700'
      };
    case 'visage':
      return {
        bg: 'from-purple-100 via-pink-100 to-purple-100',
        border: 'border-purple-200',
        iconBg: 'from-purple-500 via-purple-600 to-pink-600',
        btnBg: 'from-purple-600 to-pink-600',
        btnHover: 'hover:from-purple-700 hover:to-pink-700'
      };
    default:
      return {
        bg: 'from-slate-100 via-gray-100 to-slate-100',
        border: 'border-slate-200',
        iconBg: 'from-slate-500 via-slate-600 to-gray-600',
        btnBg: 'from-slate-600 to-gray-600',
        btnHover: 'hover:from-slate-700 hover:to-gray-700'
      };
  }
};

// Documents du demandeur principal
const principalDocuments = computed(() => {
  const docs = [];

  // Lettre de recommandation (Académique) ou CV (Professionnel)
  if (props.demande.lettre_cv_path) {
    docs.push({
      type: 'lettre_cv',
      path: props.demande.lettre_cv_path,
      name: getDocumentName('lettre_cv'),
      icon: getDocumentIcon('lettre_cv'),
      classes: getDocumentClasses('lettre_cv')
    });
  }

  // Diplômes (Professionnel uniquement)
  if (props.demande.type === 'Professionnel' && props.demande.diplomes_path) {
    docs.push({
      type: 'diplomes',
      path: props.demande.diplomes_path,
      name: getDocumentName('diplomes'),
      icon: getDocumentIcon('diplomes'),
      classes: getDocumentClasses('diplomes')
    });
  }

  // Photo d'identité
  if (props.demande.visage_path) {
    docs.push({
      type: 'visage',
      path: props.demande.visage_path,
      name: getDocumentName('visage'),
      icon: getDocumentIcon('visage'),
      classes: getDocumentClasses('visage')
    });
  }

  return docs;
});

// Documents des membres du groupe
const membersDocuments = computed(() => {
  if (props.demande.nature !== 'Groupe' || !props.demande.membres) {
    return [];
  }

  return props.demande.membres.map(membre => {
    const docs = [];

    // Lettre de recommandation (Académique) ou CV (Professionnel)
    if (membre.lettre_cv_path) {
      docs.push({
        type: 'lettre_cv',
        path: membre.lettre_cv_path,
        name: getDocumentName('lettre_cv'),
        icon: getDocumentIcon('lettre_cv'),
        classes: getDocumentClasses('lettre_cv')
      });
    }

    // Diplômes (Professionnel uniquement)
    if (props.demande.type === 'Professionnel' && membre.diplomes_path) {
      docs.push({
        type: 'diplomes',
        path: membre.diplomes_path,
        name: getDocumentName('diplomes'),
        icon: getDocumentIcon('diplomes'),
        classes: getDocumentClasses('diplomes')
      });
    }

    // Photo d'identité
    if (membre.visage_path) {
      docs.push({
        type: 'visage',
        path: membre.visage_path,
        name: getDocumentName('visage'),
        icon: getDocumentIcon('visage'),
        classes: getDocumentClasses('visage')
      });
    }

    return {
      membre,
      documents: docs
    };
  });
});

// Vérifier si des documents existent
const hasDocuments = computed(() => {
  return principalDocuments.value.length > 0 || membersDocuments.value.some(m => m.documents.length > 0);
});
</script>

<template>
  <div v-if="hasDocuments" class="bg-white rounded-3xl shadow-xl border-2 border-slate-200/50 overflow-hidden">
    <!-- En-tête -->
    <div class="px-6 py-6 border-b-2 border-slate-100 bg-gradient-to-r from-green-50 via-emerald-50 to-teal-50">
      <div class="flex items-center gap-3">
        <div class="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <div>
          <h2 class="text-xl font-bold text-slate-800">Documents attachés</h2>
          <p class="text-sm text-emerald-700 font-medium">
            {{ demande.nature === 'Groupe' ? 'Demande de groupe' : 'Demande individuelle' }} - {{ demande.type }}
          </p>
        </div>
      </div>
    </div>

    <div class="p-6 space-y-8">
      <!-- Documents du demandeur principal -->
      <div>
        <div class="flex items-center gap-3 mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
            <span class="text-white font-bold text-base">
              {{ demande.stagiaire?.user?.prenom?.charAt(0) }}{{ demande.stagiaire?.user?.nom?.charAt(0) }}
            </span>
          </div>
          <div>
            <h3 class="text-lg font-bold text-slate-800">
              {{ demande.stagiaire?.user?.nom }} {{ demande.stagiaire?.user?.prenom }}
            </h3>
            <p class="text-sm text-slate-600 font-medium">Demandeur principal</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="doc in principalDocuments" :key="doc.type"
               :class="`group bg-gradient-to-r ${doc.classes.bg} rounded-2xl p-5 border-2 ${doc.classes.border} hover:shadow-xl transition-all duration-300 transform hover:scale-105`">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div :class="`p-3 bg-gradient-to-br ${doc.classes.iconBg} rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300`">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="doc.icon" />
                  </svg>
                </div>
                <div>
                  <h4 class="font-bold text-slate-800 text-sm">{{ doc.name }}</h4>
                  <p class="text-xs text-slate-600">Disponible</p>
                </div>
              </div>
              <a v-if="showDownloadButtons" :href="'/storage/' + doc.path" target="_blank"
                 :class="`inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r ${doc.classes.btnBg} text-white rounded-lg ${doc.classes.btnHover} transition-all duration-300 shadow-lg hover:shadow-xl font-bold text-xs transform hover:scale-105`">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Télécharger
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Documents des membres du groupe -->
      <div v-if="demande.nature === 'Groupe' && membersDocuments.length > 0" class="space-y-6">
        <div class="border-t-2 border-slate-200 pt-6">
          <h3 class="text-lg font-bold text-slate-800 mb-6 flex items-center gap-2">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Membres du groupe ({{ membersDocuments.length }})
          </h3>
        </div>

        <div v-for="memberData in membersDocuments" :key="memberData.membre.id" class="space-y-4">
          <div class="flex items-center gap-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 via-purple-600 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
              <span class="text-white font-bold text-sm">
                {{ memberData.membre.user?.prenom?.charAt(0) }}{{ memberData.membre.user?.nom?.charAt(0) }}
              </span>
            </div>
            <div>
              <h4 class="text-base font-bold text-slate-800">
                {{ memberData.membre.user?.nom }} {{ memberData.membre.user?.prenom }}
              </h4>
              <p class="text-xs text-slate-600">Membre du groupe</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ml-13">
            <div v-for="doc in memberData.documents" :key="doc.type"
                 :class="`group bg-gradient-to-r ${doc.classes.bg} rounded-2xl p-4 border-2 ${doc.classes.border} hover:shadow-xl transition-all duration-300 transform hover:scale-105`">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div :class="`p-2 bg-gradient-to-br ${doc.classes.iconBg} rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300`">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="doc.icon" />
                    </svg>
                  </div>
                  <div>
                    <h5 class="font-bold text-slate-800 text-sm">{{ doc.name }}</h5>
                    <p class="text-xs text-slate-600">Disponible</p>
                  </div>
                </div>
                <a v-if="showDownloadButtons" :href="'/storage/' + doc.path" target="_blank"
                   :class="`inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r ${doc.classes.btnBg} text-white rounded-lg ${doc.classes.btnHover} transition-all duration-300 shadow-lg hover:shadow-xl font-bold text-xs transform hover:scale-105`">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Télécharger
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
