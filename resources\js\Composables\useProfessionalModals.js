import { ref } from 'vue'

// État global pour les modals
const confirmModal = ref({
  show: false,
  type: 'info',
  title: '',
  subtitle: '',
  message: '',
  details: '',
  confirmText: 'Confirmer',
  cancelText: 'Annuler',
  isDestructive: false,
  onConfirm: null,
  onCancel: null
})

const alertModal = ref({
  show: false,
  type: 'info',
  title: '',
  subtitle: '',
  message: '',
  details: '',
  suggestions: [],
  primaryActionText: 'Compris',
  secondaryActionText: 'Plus tard',
  showSecondaryAction: false,
  onPrimaryAction: null,
  onSecondaryAction: null
})

export function useProfessionalModals() {
  
  // === MODALS DE CONFIRMATION ===
  
  /**
   * Affiche un modal de confirmation professionnel
   */
  const showConfirm = (options) => {
    return new Promise((resolve) => {
      confirmModal.value = {
        show: true,
        type: options.type || 'info',
        title: options.title,
        subtitle: options.subtitle || '',
        message: options.message,
        details: options.details || '',
        confirmText: options.confirmText || 'Confirmer',
        cancelText: options.cancelText || 'Annuler',
        isDestructive: options.isDestructive || false,
        onConfirm: () => {
          confirmModal.value.show = false
          resolve(true)
        },
        onCancel: () => {
          confirmModal.value.show = false
          resolve(false)
        }
      }
    })
  }

  /**
   * Modal de confirmation pour suppression d'université
   */
  const confirmDeleteUniversite = (universiteNom) => {
    return showConfirm({
      type: 'danger',
      title: 'Suppression d\'université',
      subtitle: 'Action irréversible',
      message: `Voulez-vous vraiment supprimer l'université "${universiteNom}" ?`,
      details: 'Cette action supprimera définitivement l\'université et toutes ses données associées. Les étudiants liés à cette université devront être réassignés.',
      confirmText: 'Supprimer définitivement',
      cancelText: 'Conserver l\'université',
      isDestructive: true
    })
  }

  /**
   * Modal de confirmation pour suppression de notification
   */
  const confirmDeleteNotification = () => {
    return showConfirm({
      type: 'warning',
      title: 'Suppression de notification',
      message: 'Voulez-vous vraiment supprimer cette notification ?',
      details: 'Cette action est irréversible. La notification sera définitivement supprimée de votre liste.',
      confirmText: 'Supprimer',
      cancelText: 'Conserver',
      isDestructive: true
    })
  }

  /**
   * Modal de confirmation pour validation d'évaluation
   */
  const confirmValidateEvaluation = () => {
    return showConfirm({
      type: 'success',
      title: 'Validation d\'évaluation',
      subtitle: 'Notification automatique',
      message: 'Voulez-vous valider cette évaluation ?',
      details: 'Une fois validée, l\'étudiant recevra automatiquement ses notes par email et pourra les consulter dans son espace personnel.',
      confirmText: 'Valider et notifier',
      cancelText: 'Reporter la validation'
    })
  }

  /**
   * Modal de confirmation pour approbation/refus de demande
   */
  const confirmDemandeAction = (action, demandeCode) => {
    const isApproval = action === 'approve'
    return showConfirm({
      type: isApproval ? 'success' : 'warning',
      title: isApproval ? 'Approbation de demande' : 'Refus de demande',
      subtitle: `Demande ${demandeCode}`,
      message: `Voulez-vous ${isApproval ? 'approuver' : 'refuser'} cette demande de stage ?`,
      details: isApproval 
        ? 'Le stagiaire sera automatiquement notifié par email et pourra procéder aux étapes suivantes.'
        : 'Le stagiaire sera notifié du refus par email avec les motifs indiqués.',
      confirmText: isApproval ? 'Approuver la demande' : 'Refuser la demande',
      cancelText: 'Annuler l\'action'
    })
  }

  /**
   * Modal de confirmation pour fin de stage
   */
  const confirmFinStage = () => {
    return showConfirm({
      type: 'info',
      title: 'Confirmation de fin de stage',
      message: 'Voulez-vous confirmer la fin de ce stage ?',
      details: 'Cette action marquera le stage comme terminé et déclenchera les procédures de clôture (évaluation finale, génération d\'attestation, etc.).',
      confirmText: 'Confirmer la fin',
      cancelText: 'Maintenir le stage actif'
    })
  }

  // === MODALS D'ALERTE ===

  /**
   * Affiche un modal d'alerte professionnel
   */
  const showAlert = (options) => {
    return new Promise((resolve) => {
      alertModal.value = {
        show: true,
        type: options.type || 'info',
        title: options.title,
        subtitle: options.subtitle || '',
        message: options.message,
        details: options.details || '',
        suggestions: options.suggestions || [],
        primaryActionText: options.primaryActionText || 'Compris',
        secondaryActionText: options.secondaryActionText || 'Plus tard',
        showSecondaryAction: options.showSecondaryAction || false,
        onPrimaryAction: () => {
          alertModal.value.show = false
          resolve('primary')
        },
        onSecondaryAction: () => {
          alertModal.value.show = false
          resolve('secondary')
        }
      }
    })
  }

  /**
   * Alerte pour motif de refus manquant
   */
  const alertMotifRefusRequired = () => {
    return showAlert({
      type: 'warning',
      title: 'Motif de refus requis',
      message: 'Veuillez indiquer un motif de refus avant de continuer.',
      details: 'Le motif de refus est obligatoire pour informer le demandeur des raisons de la décision et l\'aider à améliorer sa prochaine demande.',
      suggestions: [
        'Soyez précis et constructif dans votre motif',
        'Indiquez les éléments manquants ou à corriger',
        'Proposez des améliorations si possible'
      ],
      primaryActionText: 'Compris, je vais ajouter un motif'
    })
  }

  /**
   * Alerte d'erreur de validation
   */
  const alertValidationError = (message) => {
    return showAlert({
      type: 'error',
      title: 'Erreur de validation',
      message: message || 'Une erreur de validation s\'est produite.',
      suggestions: [
        'Vérifiez que tous les champs requis sont remplis',
        'Assurez-vous que les formats de données sont corrects',
        'Contactez l\'administrateur si le problème persiste'
      ],
      primaryActionText: 'Corriger les erreurs'
    })
  }

  /**
   * Alerte d'erreur réseau
   */
  const alertNetworkError = () => {
    return showAlert({
      type: 'error',
      title: 'Erreur de connexion',
      message: 'Impossible de se connecter au serveur.',
      details: 'Vérifiez votre connexion internet et réessayez. Si le problème persiste, contactez l\'administrateur système.',
      suggestions: [
        'Vérifiez votre connexion internet',
        'Actualisez la page et réessayez',
        'Contactez le support technique si nécessaire'
      ],
      primaryActionText: 'Réessayer',
      secondaryActionText: 'Signaler le problème',
      showSecondaryAction: true
    })
  }

  // Fonctions pour fermer les modals
  const closeConfirmModal = () => {
    confirmModal.value.show = false
  }

  const closeAlertModal = () => {
    alertModal.value.show = false
  }

  return {
    // États des modals
    confirmModal,
    alertModal,
    
    // Fonctions génériques
    showConfirm,
    showAlert,
    
    // Fonctions de confirmation spécialisées
    confirmDeleteUniversite,
    confirmDeleteNotification,
    confirmValidateEvaluation,
    confirmDemandeAction,
    confirmFinStage,
    
    // Fonctions d'alerte spécialisées
    alertMotifRefusRequired,
    alertValidationError,
    alertNetworkError,
    
    // Fonctions de fermeture
    closeConfirmModal,
    closeAlertModal
  }
}
