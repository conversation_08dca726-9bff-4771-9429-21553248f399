<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Affectation de votre maître de stage</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        .content {
            padding: 30px;
        }
        .info-box {
            background-color: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .contact-box {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .details-table th {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .footer {
            background-color: #f3f4f6;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="{{ asset('images/logo-ministere.png') }}" alt="Ministère de l'Économie et des Finances" class="logo">
            <h1>🎉 Félicitations !</h1>
            <p>Votre maître de stage a été affecté</p>
        </div>

        <div class="content">
            <p>Bonjour {{ $stagiaire->prenom }},</p>

            <div class="info-box">
                <h3>✅ Excellente nouvelle !</h3>
                <p>Nous avons le plaisir de vous informer que votre demande de stage <strong>{{ $stage->demandeStage->code_suivi }}</strong> a été acceptée et qu'un maître de stage vous a été affecté.</p>
            </div>

            <h2>Détails de votre affectation</h2>
            
            <table class="details-table">
                <tr>
                    <th>Code de suivi</th>
                    <td><strong>{{ $stage->demandeStage->code_suivi }}</strong></td>
                </tr>
                <tr>
                    <th>Structure d'accueil</th>
                    <td>{{ $stage->structure->libelle }}</td>
                </tr>
                <tr>
                    <th>Période de stage</th>
                    <td>
                        Du {{ \Carbon\Carbon::parse($stage->date_debut)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                        au {{ \Carbon\Carbon::parse($stage->date_fin)->locale('fr')->isoFormat('DD MMMM YYYY') }}
                        <br><small>({{ \Carbon\Carbon::parse($stage->date_debut)->diffInDays(\Carbon\Carbon::parse($stage->date_fin)) }} jours)</small>
                    </td>
                </tr>
                <tr>
                    <th>Type de stage</th>
                    <td>{{ $stage->type }}</td>
                </tr>
                <tr>
                    <th>Statut</th>
                    <td><span style="color: #059669; font-weight: bold;">{{ $stage->statut }}</span></td>
                </tr>
            </table>

            <div class="contact-box">
                <h3>👨‍💼 Votre maître de stage</h3>
                <table class="details-table">
                    <tr>
                        <th>Nom complet</th>
                        <td><strong>{{ $maitreStage->prenom }} {{ $maitreStage->nom }}</strong></td>
                    </tr>
                    <tr>
                        <th>Fonction</th>
                        <td>{{ $affectation->agent->fonction }}</td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td><a href="mailto:{{ $maitreStage->email }}">{{ $maitreStage->email }}</a></td>
                    </tr>
                    @if($maitreStage->telephone)
                    <tr>
                        <th>Téléphone</th>
                        <td>{{ $maitreStage->telephone }}</td>
                    </tr>
                    @endif
                    <tr>
                        <th>Date d'affectation</th>
                        <td>{{ \Carbon\Carbon::parse($affectation->created_at)->locale('fr')->isoFormat('DD MMMM YYYY') }}</td>
                    </tr>
                </table>
            </div>

            <div class="info-box">
                <h4>📋 Instructions importantes</h4>
                <ul>
                    <li><strong>Premier contact :</strong> Contactez votre maître de stage avant le début du stage</li>
                    <li><strong>Ponctualité :</strong> Présentez-vous à l'heure le premier jour</li>
                    <li><strong>Documents :</strong> Apportez une pièce d'identité et vos documents académiques</li>
                    <li><strong>Tenue vestimentaire :</strong> Adoptez une tenue professionnelle appropriée</li>
                    <li><strong>Engagement :</strong> Respectez les règles et horaires de la structure</li>
                </ul>
            </div>

            <div class="contact-box">
                <h4>📞 Informations de contact</h4>
                <p><strong>Structure :</strong> {{ $stage->structure->libelle }}</p>
                @if($stage->structure->adresse)
                <p><strong>Adresse :</strong> {{ $stage->structure->adresse }}</p>
                @endif
                @if($stage->structure->telephone)
                <p><strong>Téléphone :</strong> {{ $stage->structure->telephone }}</p>
                @endif
            </div>

            <p>Nous vous souhaitons un excellent stage et une expérience enrichissante au sein du Ministère de l'Économie et des Finances du Bénin.</p>

            <div style="text-align: center;">
                <a href="{{ route('stagiaire.stages.show', $stage->id) }}" class="btn">
                    Voir les détails de mon stage
                </a>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Ministère de l'Économie et des Finances du Bénin</strong><br>
                Direction de la Planification et de l'Administration Financière (DPAF)<br>
                Système de gestion des stages
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                Pour toute question, contactez votre maître de stage ou écrivez à : <EMAIL>
            </p>
        </div>
    </div>
</body>
</html>
