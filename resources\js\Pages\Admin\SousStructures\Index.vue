<template>
  <AdminLayout>
    <template #header>
      <div class="flex items-center gap-3 mb-2">
        <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-12 h-12 flex items-center justify-center shadow-lg">
          <BuildingOffice2Icon class="w-6 h-6" />
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800">
            Sous-structures
          </h1>
          <p class="text-sm text-gray-600 mt-1">Vue d'ensemble des sous-structures (lecture seule)</p>
        </div>
      </div>
    </template>

    <AdminToast ref="toastRef" />

    <div class="space-y-6">
      <!-- Statistiques -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100">
              <BuildingOffice2Icon class="w-6 h-6 text-purple-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Sous-structures</p>
              <p class="text-2xl font-bold text-gray-900">{{ sousStructures.length }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100">
              <UserIcon class="w-6 h-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Avec Responsable</p>
              <p class="text-2xl font-bold text-gray-900">{{ structuresAvecResponsable }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100">
              <ExclamationTriangleIcon class="w-6 h-6 text-orange-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Sans Responsable</p>
              <p class="text-2xl font-bold text-gray-900">{{ structuresSansResponsable }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100">
              <BuildingOfficeIcon class="w-6 h-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Structures Parent</p>
              <p class="text-2xl font-bold text-gray-900">{{ uniqueParents }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Liste des sous-structures -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h3 class="text-lg font-semibold text-gray-900">Liste des Sous-structures</h3>
          <p class="text-sm text-gray-600 mt-1">Vue d'ensemble - Gérées par les agents RS</p>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Structure</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Structure Parent</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsable</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sous-structures</th>
                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-100">
              <tr v-for="structure in sousStructures" :key="structure.id" class="hover:bg-gray-50 transition">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                        <BuildingOffice2Icon class="w-5 h-5 text-purple-600" />
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ structure.sigle }}</div>
                      <div class="text-sm text-gray-500">{{ structure.libelle }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {{ structure.parent?.sigle || 'N/A' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                  <div v-if="structure.responsable">
                    <div class="font-medium">{{ structure.responsable.user.prenom }} {{ structure.responsable.user.nom }}</div>
                    <div class="text-gray-500">{{ structure.responsable.user.email }}</div>
                  </div>
                  <span v-else class="text-gray-400 italic">Non assigné</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                  {{ structure.type_structure || 'Non défini' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {{ structure.children?.length || 0 }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <Link :href="route('admin.sous-structures.show', structure.id)" 
                        class="text-purple-600 hover:text-purple-900 font-medium flex items-center justify-center gap-1" 
                        title="Voir les détails">
                    <EyeIcon class="w-4 h-4" />
                    Voir
                  </Link>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-if="sousStructures.length === 0" class="text-center py-12">
          <BuildingOffice2Icon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune sous-structure</h3>
          <p class="mt-1 text-sm text-gray-500">Aucune sous-structure n'a été créée pour le moment.</p>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';
import { Link } from '@inertiajs/vue3';
import { computed, ref } from 'vue';
import {
  BuildingOffice2Icon,
  BuildingOfficeIcon,
  UserIcon,
  EyeIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  sousStructures: Array
});

const toastRef = ref(null);

const structuresAvecResponsable = computed(() => {
  return props.sousStructures.filter(s => s.responsable).length;
});

const structuresSansResponsable = computed(() => {
  return props.sousStructures.filter(s => !s.responsable).length;
});

const uniqueParents = computed(() => {
  const parents = new Set();
  props.sousStructures.forEach(structure => {
    if (structure.parent) {
      parents.add(structure.parent.id);
    }
  });
  return parents.size;
});
</script>
