<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Stage;
use App\Models\Structure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class StageController extends Controller
{
    /**
     * Afficher la liste de tous les stages (vue globale DPAF)
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            
            // Vérifier que l'utilisateur est un agent DPAF
            if ($user->role !== 'Agent' || !$user->agent) {
                return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
            }

            // Construire la requête de base pour TOUS les stages
            $query = Stage::with([
                'structure', 
                'demandeStage.stagiaire.user',
                'affectationsMaitreStage' => function($query) {
                    $query->orderBy('date_affectation', 'desc');
                },
                'affectationsMaitreStage.maitreStage',
                'affectationsMaitreStage.agentAffectant.user'
            ]);

            // Appliquer les filtres
            if ($request->filled('status') && $request->status !== 'all') {
                $query->where('statut', $request->status);
            }

            if ($request->filled('structure') && $request->structure !== 'all') {
                $query->where('structure_id', $request->structure);
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->whereHas('demandeStage.stagiaire.user', function($q) use ($search) {
                    $q->where('nom', 'like', "%{$search}%")
                      ->orWhere('prenom', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            // Ordonner par date de début décroissante
            $query->orderBy('date_debut', 'desc');

            // Paginer les résultats
            $stages = $query->paginate(15);

            // Transformer les données pour l'affichage
            $stages->getCollection()->transform(function ($stage) {
                // Récupérer l'affectation la plus récente
                $affectationRecente = $stage->affectationsMaitreStage->first();
                
                $stage->affectation_recente = $affectationRecente ? [
                    'id' => $affectationRecente->id,
                    'date_affectation' => $affectationRecente->date_affectation,
                    'statut' => $affectationRecente->statut,
                    'maitre_stage' => $affectationRecente->maitreStage ? [
                        'id' => $affectationRecente->maitreStage->id,
                        'nom' => $affectationRecente->maitreStage->nom,
                        'prenom' => $affectationRecente->maitreStage->prenom,
                        'email' => $affectationRecente->maitreStage->email,
                    ] : null,
                    'agent_affectant' => $affectationRecente->agentAffectant ? [
                        'id' => $affectationRecente->agentAffectant->id,
                        'nom' => $affectationRecente->agentAffectant->user->nom,
                        'prenom' => $affectationRecente->agentAffectant->user->prenom,
                    ] : null,
                ] : null;
                
                return $stage;
            });

            // Récupérer toutes les structures pour le filtre
            $structures = Structure::orderBy('libelle')->get();

            return Inertia::render('Agent/Stages/Index', [
                'stages' => $stages,
                'structures' => $structures,
                'filters' => $request->only(['status', 'structure', 'search', 'page'])
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement des stages DPAF', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            
            return redirect()->back()->with('error', 'Une erreur est survenue lors du chargement des stages.');
        }
    }

    /**
     * Afficher les détails d'un stage
     */
    public function show(Stage $stage)
    {
        try {
            $user = Auth::user();
            
            // Vérifier que l'utilisateur est un agent DPAF
            if ($user->role !== 'Agent' || !$user->agent) {
                return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
            }
            
            // Charger les relations nécessaires
            $stage->load([
                'structure', 
                'demandeStage.stagiaire.user',
                'affectationsMaitreStage' => function($query) {
                    $query->orderBy('date_affectation', 'desc');
                },
                'affectationsMaitreStage.maitreStage',
                'affectationsMaitreStage.agentAffectant.user',
                'themeStage',
                'evaluation',
                'demandeStage.membres.user',
            ]);

            return Inertia::render('Agent/Stages/Show', [
                'stage' => $stage,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Erreur lors du chargement du détail du stage DPAF', [
                'error' => $e->getMessage(),
                'stage_id' => $stage->id,
                'user_id' => Auth::id()
            ]);
            
            return redirect()->back()->with('error', 'Une erreur est survenue lors du chargement du stage.');
        }
    }
}
