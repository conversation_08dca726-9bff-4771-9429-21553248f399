<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Migration consolidée pour la table structures avec toutes les colonnes nécessaires
     * Inclut les colonnes d'organigramme et la référence au responsable
     */
    public function up(): void
    {
        Schema::create('structures', function (Blueprint $table) {
            $table->id();
            $table->string('sigle')->nullable();
            $table->string('libelle')->nullable();
            $table->text('description')->nullable();
            $table->boolean('active')->default(true);
            
            // Colonnes d'organigramme
            $table->foreignId('parent_id')->nullable()->constrained('structures')->onDelete('cascade');
            $table->integer('niveau')->default(0);
            $table->integer('ordre')->default(0);
            $table->string('type_structure')->nullable()->comment('Direction, Service, Département, etc.');
            
            // Responsable de la structure (référence vers agents)
            $table->foreignId('responsable_id')->nullable()->constrained('agents')->onDelete('set null');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('structures');
    }
};
