<script setup>
import { ref, provide } from 'vue';
import { Link, usePage } from '@inertiajs/vue3';
import RealNotificationBell from '@/Components/RealNotificationBell.vue';


const sidebarExpanded = ref(true);
const showUserMenu = ref(false);
const user = usePage().props.auth?.user;
const logoUrl = '/images/logoministere.png';
provide('sidebarExpanded', sidebarExpanded);

const toggleSidebar = () => {
    sidebarExpanded.value = !sidebarExpanded.value;
};

const toggleUserMenu = () => {
    showUserMenu.value = !showUserMenu.value;
};

const closeUserMenu = () => {
    showUserMenu.value = false;
};

const menuItems = [
    {
        name: 'Tableau de bord',
        route: 'stagiaire.dashboard',
        icon: 'M3 5a2 2 0 012-2h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5z',
        active: ['stagiaire.dashboard']
    },
    {
        name: 'Me<PERSON> Demand<PERSON>',
        route: 'mes.demandes',
        icon: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z',
        active: ['mes.demandes']
    },
    {
        name: 'Mes Stages',
        route: 'stagiaire.stages',
        icon: 'M20.584 9.75A2.253 2.253 0 0018.25 7.5h-1.5V6a3 3 0 00-3-3H7.5a3 3 0 00-3 3v1.5h-1.5c-1.241 0-2.25 1.009-2.25 2.25v7.5A2.253 2.253 0 004.25 19.5h15a2.253 2.253 0 002.25-2.25v-7.5zM16.5 7.5h-9V6a1.5 1.5 0 011.5-1.5h4.5a1.5 1.5 0 011.5 1.5v1.5zm-.919 9.374a1.5 1.5 0 01-2.121 0l-.303-.303a1.5 1.5 0 012.121-2.121l.303.303a1.5 1.5 0 010 2.121zm2.121-2.121a1.5 1.5 0 010 2.121l-.303.303a1.5 1.5 0 01-2.121-2.121l.303-.303a1.5 1.5 0 012.121 0z',
        active: ['stagiaire.stages', 'stagiaire.stages.show']
    },
    {
        name: 'Recherche par code',
        route: 'recherche.code',
        icon: 'M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z',
        active: ['recherche.code']
    }
];

const isActive = (routeNames) => {
    return routeNames.some(routeName => route().current(routeName));
};



const props = defineProps({
  notifications: {
    type: Array,
    default: () => []
  }
});
</script>

<template>
    <div class="flex h-screen stagiaire-bg">
        <!-- SIDEBAR -->
        <aside 
            :class="[
                'bg-gradient-to-b from-blue-600 to-blue-700 text-white transition-all duration-300 ease-in-out flex flex-col shadow-lg z-30',
                sidebarExpanded ? 'w-64' : 'w-20'
            ]"
        >
            <!-- Header du Sidebar -->
            <div class="p-4 border-b border-blue-500/30">
                <div class="flex items-center justify-between">
                    <Link :href="route('dashboard')" class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center shadow-sm">
                            <span class="text-gray-600 font-bold text-lg">GS</span>
                        </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" key="logo-text" class="transition-all duration-300">
                            <h1 class="text-lg font-bold text-white">GestionStages</h1>
                            <p class="text-xs text-blue-200">Ministère des Finances</p>
                        </div>
                        </transition>
                    </Link>
                    <button 
                        @click="toggleSidebar"
                        class="p-2 rounded-lg hover:bg-blue-500/30 transition-colors duration-200"
                    >
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 p-4 space-y-2">
                
                <Link
                    v-for="item in menuItems"
                    :key="item.route"
                    :href="route(item.route)"
                    :class="[
                        'flex items-center rounded-xl text-sm font-medium transition-all duration-200 group relative',
                        sidebarExpanded ? 'px-4 py-3' : 'justify-center py-3 mx-2',
                        isActive(item.active)
                            ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/10'
                            : 'text-blue-100 hover:bg-white/10 hover:text-white hover:shadow-md'
                    ]"
                >
                    <!-- Indicateur actif -->
                    <div
                        v-if="isActive(item.active)"
                        class="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full"
                    ></div>

                    <svg class="w-5 h-5 flex-shrink-0 transition-all duration-200"
                         :class="isActive(item.active) ? 'text-white' : 'text-blue-200 group-hover:text-white'"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                    </svg>
                    <transition name='fade' mode='out-in'>
                    <span
                        v-if="sidebarExpanded"
                        class="ml-3 transition-all duration-300 font-medium"
                          :key="`label-${item.route}`"
                    >
                        {{ item.name }}
                    </span>
                    </transition>

                    <!-- Badge pour les éléments actifs -->
                    <div
                        v-if="isActive(item.active) && sidebarExpanded"
                        class="ml-auto w-2 h-2 bg-white rounded-full opacity-80"
                    ></div>
                </Link>
            </nav>

            <!-- User Profile Section avec menu personnalisé -->
            <div class="p-4 border-t border-blue-500/30 relative">
                <div class="relative">
                    <!-- Bouton du profil utilisateur -->
                    <button 
                        @click="toggleUserMenu"
                        :class="[
                            'w-full flex items-center rounded-lg text-sm font-medium text-blue-100 bg-blue-500/20 transition-colors hover:bg-blue-500/30',
                            sidebarExpanded ? 'px-3 py-3' : 'justify-center py-3'
                        ]"
                    >
                    <div class="relative flex-shrink-0">
                        <img
                            v-if="user && user.avatar"
                            :src="'/storage/' + user.avatar"
                            alt="Photo de profil"
                            class="w-8 h-8 rounded-full object-cover border-2 border-blue-300"
                        />
                            <div v-else class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold border-2 border-blue-300">
                            {{ user?.nom?.charAt(0) || 'U' }}
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-blue-600 rounded-full"></div>
                    </div>
                        <transition name='fade' mode='out-in'>
                          <div v-if="sidebarExpanded" class="ml-3 text-left flex-1" key="user-info">
                        <div class="font-medium text-white text-sm">{{ user?.nom || 'Utilisateur' }}</div>
                        <div class="text-xs text-blue-200 truncate">Stagiaire</div>
                    </div>
                        </transition>
                        <transition name='fade' mode='out-in'>
                          <svg v-if="sidebarExpanded" class="w-4 h-4 text-blue-200 ml-2 transition-transform" :class="{ 'rotate-180': showUserMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                          </svg>
                        </transition>
                    </button>

                    <!-- Menu contextuel personnalisé -->
                    <transition name="menu-fade">
                        <div 
                            v-if="showUserMenu" 
                            class="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                        >
                            <!-- Info utilisateur -->
                            <div class="px-4 py-3 border-b border-gray-100">
                                <div class="font-medium text-base text-gray-800">{{ user?.nom || 'Utilisateur' }}</div>
                                <div class="font-medium text-sm text-gray-500">{{ user?.email || 'Email non disponible' }}</div>
                            </div>
                            
                            <!-- Options du menu -->
                            <div class="py-1">
                                <Link
                                    :href="route('profile.edit')"
                                    @click="closeUserMenu"
                                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                                >
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Mon Profil
                                </Link>
                                <Link
                                    :href="route('logout')"
                                    method="post"
                                    as="button"
                                    @click="closeUserMenu"
                                    class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                >
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                    </svg>
                                    Se Déconnecter
                                </Link>
                            </div>
                        </div>
                    </transition>

                    <!-- Overlay pour fermer le menu en cliquant ailleurs -->
                    <div 
                        v-if="showUserMenu" 
                        @click="closeUserMenu"
                        class="fixed inset-0 z-40"
                    ></div>
                </div>
            </div>
        </aside>

        <!-- MAIN CONTENT -->
        <div class="flex-1 flex flex-col">
            <!-- Top Header modernisé et professionnel -->
            <header class="bg-white shadow-sm border-b border-gray-200 z-20">
                <div class="flex items-center justify-between px-8 py-6">
                    <div class="flex items-center space-x-6">
                        <!-- Logo avec container moderne -->
                        <div class="flex-shrink-0">
                            <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-xl border border-blue-200/50 shadow-sm">
                        <img
                            :src="logoUrl"
                            alt="Logo du Ministère"
                            class="h-12 w-auto object-contain"
                        />
                            </div>
                        </div>
                        
                        <!-- Séparateur visuel -->
                        <div class="hidden md:block w-px h-12 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                        
                        <!-- Informations textuelles avec hiérarchie claire -->
                        <div class="hidden md:block space-y-1">
                            <h1 class="text-xl font-semibold text-gray-900 tracking-tight">
                                Programme de Stages
                            </h1>
                            <div class="flex items-center space-x-2">
                                <p class="text-sm font-medium text-blue-600">Ministère des Finances</p>
                                <span class="text-gray-300">•</span>
                                <p class="text-sm text-gray-500">République du Bénin</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Section droite avec indicateurs -->
                    <div class="flex items-center space-x-4">


                        <!-- Badge statut pour desktop -->
                        <div class="hidden lg:flex items-center space-x-3">
                            <div class="flex items-center space-x-2 bg-green-50 text-green-700 px-3 py-2 rounded-full text-sm font-medium border border-green-200">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span>Espace Stagiaire</span>
                            </div>
                        </div>
                        
                        <!-- Badge mobile/tablet -->
                        <div class="lg:hidden bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <span></span>
                            </div>
                        </div>
                        
                        <!-- Indicateur de version/environnement (optionnel) -->
                        <div class="hidden xl:block text-xs text-gray-400 font-mono bg-gray-50 px-2 py-1 rounded border">
                            v2.1.0
                        </div>
                    </div>
                </div>
                
                <!-- Barre de progression ou indicateur supplémentaire (optionnel) -->
                <div class="h-1 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700"></div>
            </header>

        <!-- Cloche de notifications réelle -->
        <RealNotificationBell
            user-role="stagiaire"
            theme="blue"
            :notifications="notifications"
        />

            <!-- Page Heading (si présent) -->
            <header
                v-if="$slots.header"
                class="bg-white border-b border-gray-200"
            >
                <div class="px-6 py-6">
                    <slot name="header" />
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <slot />
            </main>
        </div>
    </div>
</template>

<style>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.menu-fade-enter-active, .menu-fade-leave-active {
  transition: all 0.2s ease;
}
.menu-fade-enter-from, .menu-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.stagiaire-bg {
  position: relative;
  min-height: 100vh;
}
.stagiaire-bg::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/images/bg-stagiaire.jpg') center/cover no-repeat;
  opacity: 0.12;
  z-index: -1;
  pointer-events: none;
}

/* Animation pour le panel de notifications */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}
</style>