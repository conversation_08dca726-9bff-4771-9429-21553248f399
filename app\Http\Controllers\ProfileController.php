<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\JsonResponse;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        $user = $request->user();

        // Déterminer le layout approprié selon le rôle
        $layout = $this->getLayoutForUser($user);

        return Inertia::render('Profile/Edit', [
            'mustVerifyEmail' => $user instanceof MustVerifyEmail,
            'status' => session('status'),
            'user' => $user,
            'layout' => $layout,
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse|JsonResponse
    {
        $user = $request->user();
        $data = $request->validated();

        \Log::info('Update Profile Request', [
            'has_file' => $request->hasFile('avatar'),
            'all_data' => $request->all(),
            'validated_data' => $data
        ]);

        if ($request->hasFile('avatar')) {
            \Log::info('Processing avatar file');
            
            // Suppression de l'ancien avatar s'il existe
            if ($user->avatar) {
                \Log::info('Deleting old avatar: ' . $user->avatar);
                Storage::disk('public')->delete($user->avatar);
            }

            // Upload du nouvel avatar
            $path = $request->file('avatar')->store('avatars', 'public');
            \Log::info('New avatar path: ' . $path);
            $data['avatar'] = $path;
        }

        $user->fill($data);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();

        \Log::info('User updated', [
            'user_id' => $user->id,
            'avatar' => $user->avatar
        ]);

        if ($request->wantsJson()) {
            return response()->json([
                'message' => 'Profile updated successfully',
                'avatar' => $user->avatar,
                'avatar_url' => $user->avatar ? asset('storage/' . $user->avatar) : null
            ]);
        }

        return Redirect::route('profile.edit')->with('success', 'Profil mis à jour avec succès.');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        // Suppression de l'avatar lors de la suppression du compte
        if ($user->avatar) {
            Storage::disk('public')->delete($user->avatar);
        }

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }

    /**
     * Remove user avatar.
     */
    public function removeAvatar(Request $request): RedirectResponse
    {
        $user = $request->user();

        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
            $user->update(['avatar' => null]);
        }

        return Redirect::route('profile.edit')->with('status', 'avatar-removed');
    }

    /**
     * Déterminer le layout approprié selon le rôle de l'utilisateur
     */
    private function getLayoutForUser($user): string
    {
        if ($user->role === 'admin') {
            return 'AdminLayout';
        } elseif ($user->role === 'Agent') { // CORRECTION: 'Agent' avec majuscule
            // Vérifier le rôle de l'agent
            $agent = $user->agent;
            if ($agent) {
                if ($agent->role_agent === 'RS') {
                    return 'RSLayout';
                } elseif ($agent->role_agent === 'MS') {
                    return 'MSLayout';
                }
            }
            return 'AdminLayout'; // Fallback
        } elseif ($user->role === 'stagiaire') {
            return 'Stagiaire';
        }

        return 'AdminLayout'; // Fallback par défaut
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', Password::defaults(), 'confirmed'],
        ], [
            'current_password.required' => 'Le mot de passe actuel est requis.',
            'current_password.current_password' => 'Le mot de passe actuel est incorrect.',
            'password.required' => 'Le nouveau mot de passe est requis.',
            'password.confirmed' => 'La confirmation du mot de passe ne correspond pas.',
            'password.min' => 'Le mot de passe doit contenir au moins :min caractères.',
        ]);

        $request->user()->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('status', 'password-updated');
    }
}
