<template>
  <Head title="Agents MS" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header professionnel -->
        <div class="flex items-center gap-4 mb-8">
          <div class="p-3 bg-green-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-slate-800">Agents MS</h1>
            <p class="text-slate-600 mt-1">Consultation des Maîtres de Stage (lecture seule)</p>
          </div>
        </div>

        <AdminToast ref="toastRef" />

        <!-- Statistiques professionnelles -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-green-100 rounded-xl">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Total Agents MS</p>
                <p class="text-2xl font-bold text-slate-800">{{ agents.length }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-blue-100 rounded-xl">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Agents Actifs</p>
                <p class="text-2xl font-bold text-slate-800">{{ agentsActifs }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-purple-100 rounded-xl">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Structures</p>
                <p class="text-2xl font-bold text-slate-800">{{ uniqueStructures }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Filtres -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6 mb-8">
          <h3 class="text-lg font-bold text-slate-800 mb-4">Filtres et recherche</h3>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-semibold text-slate-700 mb-2">Structure</label>
              <select v-model="filters.structure" class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="all">Toutes les structures</option>
                <option v-for="structure in uniqueStructuresList" :key="structure.id" :value="structure.id">
                  {{ structure.sigle }} - {{ structure.libelle }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-semibold text-slate-700 mb-2">Créé par</label>
              <select v-model="filters.created_by" class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="all">Tous les créateurs</option>
                <option v-for="creator in uniqueCreators" :key="creator.id" :value="creator.id">
                  {{ getFullName(creator) }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-semibold text-slate-700 mb-2">Recherche</label>
              <input
                v-model="filters.search"
                type="text"
                placeholder="Nom, email, matricule..."
                class="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
            </div>

            <div class="flex items-end gap-2">
              <button @click="resetFilters" class="px-4 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-lg font-semibold transition-colors">
                Réinitialiser
              </button>
            </div>
          </div>
        </div>

        <!-- Interface principale -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <!-- Header avec actions -->
          <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
            <div>
              <h2 class="text-xl font-bold text-slate-800">Liste des Agents MS</h2>
              <p class="text-sm text-slate-600 mt-1">{{ filteredAgents.length }} maître(s) de stage affiché(s) sur {{ agents.length }} - Consultation uniquement</p>
            </div>
          </div>

          <!-- État vide -->
          <div v-if="agents.length === 0" class="text-center py-16">
            <div class="w-20 h-20 mx-auto bg-slate-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-slate-800 mb-2">Aucun agent MS enregistré</h3>
            <p class="text-slate-600">Les agents MS sont créés par les Responsables de Structure.</p>
          </div>

          <!-- Tableau responsive avec scroll horizontal -->
          <div v-else class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="min-w-full">
                <thead class="bg-slate-100">
                  <tr>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[250px]">Agent</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[120px]">Matricule</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[200px]">Structure de Direction Générale</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[200px]">Sous-structure</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[120px]">Responsable</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[180px]">Créé par</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[150px]">Date création</th>
                    <th class="px-6 py-4 text-center text-sm font-bold text-slate-700 min-w-[120px]">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-200">
                  <tr v-for="agent in filteredAgents" :key="agent.id" class="hover:bg-blue-50 transition-colors duration-200">
                    <td class="px-6 py-4">
                      <div class="flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          {{ getInitials(agent.user) }}
                        </div>
                        <div>
                          <div class="text-sm font-bold text-slate-800">{{ getFullName(agent.user) }}</div>
                          <div class="text-sm text-slate-600">{{ agent.user?.email || 'Email non renseigné' }}</div>
                        </div>
                      </div>
                    </td>

                    <td class="px-6 py-4">
                      <span class="inline-flex items-center px-3 py-1 rounded-lg text-xs font-semibold bg-slate-100 text-slate-700">
                        {{ agent.matricule || 'N/A' }}
                      </span>
                    </td>

                    <!-- Structure de Direction Générale -->
                    <td class="px-6 py-4">
                      <div v-if="getStructureDirectionGenerale(agent)">
                        <div class="text-sm font-semibold text-blue-600">{{ getStructureDirectionGenerale(agent).sigle }}</div>
                        <div class="text-xs text-slate-500">{{ getStructureDirectionGenerale(agent).libelle }}</div>
                      </div>
                      <div v-else class="text-sm text-slate-500 italic">Non identifiée</div>
                    </td>

                    <!-- Sous-structure -->
                    <td class="px-6 py-4">
                      <div v-if="agent.structure">
                        <div class="text-sm font-semibold text-green-600">{{ agent.structure.sigle }}</div>
                        <div class="text-xs text-slate-500">{{ agent.structure.libelle }}</div>
                      </div>
                      <div v-else class="text-sm text-slate-500 italic">Non assignée</div>
                    </td>

                    <!-- Responsable -->
                    <td class="px-6 py-4">
                      <div class="flex items-center gap-2">
                        <div :class="[
                          'w-2 h-2 rounded-full',
                          isResponsableSousStructure(agent) ? 'bg-green-500' : 'bg-slate-400'
                        ]"></div>
                        <span class="text-sm font-medium" :class="[
                          isResponsableSousStructure(agent) ? 'text-green-700' : 'text-slate-600'
                        ]">
                          {{ isResponsableSousStructure(agent) ? 'Oui' : 'Non' }}
                        </span>
                      </div>
                    </td>

                    <td class="px-6 py-4">
                      <div v-if="agent.creator">
                        <div class="text-sm font-semibold text-slate-800">{{ getFullName(agent.creator) }}</div>
                        <div class="text-xs text-slate-500">{{ agent.creator.role || 'Responsable' }}</div>
                      </div>
                      <div v-else class="text-sm text-slate-500 italic">Non renseigné</div>
                    </td>

                    <td class="px-6 py-4">
                      <div class="text-sm text-slate-700">
                        {{ formatDate(agent.created_at) || 'Non renseigné' }}
                      </div>
                    </td>

                    <td class="px-6 py-4">
                      <div class="flex justify-center">
                        <Link
                          :href="route('admin.agents-ms.show', agent.id)"
                          class="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg font-semibold flex items-center gap-2 transition-colors"
                          title="Voir les détails"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                          </svg>
                          Voir
                        </Link>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';
import { computed, ref } from 'vue';
import {
  UserIcon,
  EyeIcon,
  CheckCircleIcon,
  BuildingOfficeIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  agents: Array
});

const toastRef = ref(null);

// État des filtres
const filters = ref({
  structure: 'all',
  created_by: 'all',
  search: ''
});

// Computed properties pour les statistiques
const agentsActifs = computed(() => {
  return props.agents.filter(agent => agent.user).length;
});

const uniqueStructures = computed(() => {
  const structures = new Set();
  props.agents.forEach(agent => {
    if (agent.structure) {
      structures.add(agent.structure.id);
    }
  });
  return structures.size;
});

// Listes uniques pour les filtres
const uniqueStructuresList = computed(() => {
  const structures = new Map();
  props.agents.forEach(agent => {
    if (agent.structure) {
      structures.set(agent.structure.id, agent.structure);
    }
  });
  return Array.from(structures.values()).sort((a, b) => a.sigle.localeCompare(b.sigle));
});

const uniqueCreators = computed(() => {
  const creators = new Map();
  props.agents.forEach(agent => {
    if (agent.creator) {
      creators.set(agent.creator.id, agent.creator);
    }
  });
  return Array.from(creators.values()).sort((a, b) => getFullName(a).localeCompare(getFullName(b)));
});

// Agents filtrés
const filteredAgents = computed(() => {
  let filtered = [...props.agents];

  // Filtrage par structure
  if (filters.value.structure !== 'all') {
    filtered = filtered.filter(agent =>
      agent.structure && agent.structure.id == filters.value.structure
    );
  }

  // Filtrage par créateur
  if (filters.value.created_by !== 'all') {
    filtered = filtered.filter(agent =>
      agent.creator && agent.creator.id == filters.value.created_by
    );
  }

  // Filtrage par recherche
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase();
    filtered = filtered.filter(agent => {
      const fullName = getFullName(agent.user).toLowerCase();
      const email = (agent.user?.email || '').toLowerCase();
      const matricule = (agent.matricule || '').toLowerCase();

      return fullName.includes(searchTerm) ||
             email.includes(searchTerm) ||
             matricule.includes(searchTerm);
    });
  }

  return filtered;
});

// Fonctions helper pour l'affichage des noms
const getFullName = (user) => {
  if (!user) return 'Non défini';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  return `${prenom} ${nom}`.trim() || 'Non défini';
};

const getInitials = (user) => {
  if (!user) return 'N';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  const prenomInitial = prenom.charAt(0).toUpperCase();
  const nomInitial = nom.charAt(0).toUpperCase();
  return `${prenomInitial}${nomInitial}` || 'N';
};

const resetFilters = () => {
  filters.value = {
    structure: 'all',
    created_by: 'all',
    search: ''
  };
};

const formatDate = (dateString) => {
  if (!dateString) return null;
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Fonction pour obtenir la structure de direction générale
const getStructureDirectionGenerale = (agent) => {
  if (!agent.structure) return null;

  let currentStructure = agent.structure;
  // Remonter jusqu'à la structure racine (Direction Générale)
  while (currentStructure.parent) {
    currentStructure = currentStructure.parent;
  }
  return currentStructure;
};

// Fonction pour vérifier si l'agent est responsable de sa sous-structure
const isResponsableSousStructure = (agent) => {
  if (!agent.structure || !agent.structuresResponsable) return false;
  return agent.structuresResponsable.some(structure => structure.id === agent.structure.id);
};
</script>
