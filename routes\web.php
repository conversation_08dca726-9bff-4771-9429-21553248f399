<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\StructureController;
use App\Http\Controllers\AgentController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DemandeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\StagiaireController;
use App\Http\Controllers\Api\EmailController;
use App\Http\Controllers\Api\StagiaireMessageController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use App\Http\Controllers\Admin\RapportController;

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
});

Route::middleware(['auth', 'verified', 'force.session.expiry'])->group(function () {
    // Redirection du dashboard principal vers le dashboard approprié
Route::get('/dashboard', function () {
    $user = Auth::user();

        if ($user->role === 'admin') {
            return redirect()->route('admin.dashboard');
        }

        if ($user->agent) {
            // Si l'agent est un RS, rediriger vers le dashboard RS
            if ($user->agent->role_agent === 'RS') {
                return redirect()->route('agent.rs.dashboard');
            }

            // Si l'agent est un RU, rediriger vers le dashboard RU
            if ($user->agent->role_agent === 'RU') {
                return redirect()->route('agent.ru.dashboard');
            }

            // Si l'agent est un MS, rediriger vers le dashboard MS
            if ($user->agent->role_agent === 'MS') {
                return redirect()->route('agent.ms.dashboard');
            }
        }

        if ($user->role === 'stagiaire') {
            return redirect()->route('stagiaire.dashboard');
        }

        // Redirection par défaut
        return redirect()->route('login');
    })->name('dashboard');

    // Routes de profil
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::delete('/profile/avatar', [ProfileController::class, 'removeAvatar'])->name('profile.avatar.remove');

    // Route pour le changement de mot de passe
    Route::put('/password', [ProfileController::class, 'updatePassword'])->name('password.update');

    // Admin routes
    Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function() {
        // Dashboard admin avec statistiques
        Route::get('/dashboard', function () {
            $stats = [
                'users' => \App\Models\User::count(),
                'structures' => \App\Models\Structure::count(),
                'stagiaires' => \App\Models\Stagiaire::count(),
                'agents' => \App\Models\Agent::count(),
            ];

            $recentStagiaires = DB::table('stagiaires')->orderBy('created_at', 'desc')->take(2)->get();
            $recentStructures = DB::table('structures')->orderBy('created_at', 'desc')->take(1)->get();
            $lastDemande = DB::table('demande_stages')->orderBy('created_at', 'desc')->first();

            $recentActivities = [];

            // Ajout de la dernière demande de stage
            if ($lastDemande) {
                $stagiaire = DB::table('users')
                    ->join('stagiaires', 'users.id', '=', 'stagiaires.user_id')
                    ->where('stagiaires.id_stagiaire', $lastDemande->stagiaire_id)
                    ->select('users.nom', 'users.prenom')
                    ->first();
                $structure = null;
                if ($lastDemande->structure_id) {
                    $structureObj = DB::table('structures')->where('id', $lastDemande->structure_id)->first();
                    $structure = $structureObj ? $structureObj->sigle : null;
                }
                $recentActivities[] = [
                    'type' => 'demande',
                    'title' => 'Dernière demande de stage',
                    'user' => $stagiaire ? trim(($stagiaire->prenom ?? '') . ' ' . ($stagiaire->nom ?? '')) : null,
                    'structure' => $structure,
                    'created_at' => $lastDemande->created_at,
                ];
            }

            foreach ($recentStagiaires as $stagiaire) {
                // On va chercher le nom du user associé
                $user = DB::table('users')->where('id', $stagiaire->user_id)->first();
                $userName = $user ? trim(($user->prenom ?? '') . ' ' . ($user->nom ?? '')) : null;

                $recentActivities[] = [
                    'type' => 'stagiaire',
                    'title' => 'Nouveau stagiaire inscrit',
                    'user' => $userName,
                    'created_at' => $stagiaire->created_at,
                ];
            }
            foreach ($recentStructures as $structure) {
                $recentActivities[] = [
                    'type' => 'structure',
                    'title' => 'Nouvelle structure ajoutée',
                    'structure' => $structure->libelle,
                    'created_at' => $structure->created_at,
                ];
            }
            $recentActivities[] = [
                'type' => 'rapport',
                'title' => 'Rapport mensuel généré',
                'created_at' => now()->subDays(2),
            ];

            // Trie par date décroissante
            usort($recentActivities, fn($a, $b) => strtotime($b['created_at']) <=> strtotime($a['created_at']));

            return Inertia::render('Dashboard/Admin', [
                'stats' => $stats,
                'recentActivities' => $recentActivities,
                'notifications' => Auth::user()->notifications()->latest()->take(20)->get()
            ]);
        })->name('dashboard');

        // Autres routes admin...
        Route::get('/users', [UserController::class, 'index'])->name('users.index');
        Route::post('/users', [UserController::class, 'store'])->name('users.store');
        Route::put('/users/{user}', [UserController::class, 'update'])->name('users.update');
        Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy');

        // CORRECTION : Route spécifique AVANT les routes générales pour éviter les conflits
        Route::get('/structures/available', [App\Http\Controllers\StructureController::class, 'available'])->name('structures.available');

        Route::get('/structures', [StructureController::class, 'index'])->name('structures.index');
        Route::get('/structures/{structure}', [StructureController::class, 'show'])->name('structures.show');
        Route::post('/structures', [StructureController::class, 'store'])->name('structures.store');
        Route::put('/structures/{structure}', [StructureController::class, 'update'])->name('structures.update');
        Route::delete('/structures/{structure}', [StructureController::class, 'destroy'])->name('structures.destroy');

        Route::get('/stagiaires', [App\Http\Controllers\StagiaireController::class, 'index'])->name('stagiaires.index');

        // Routes pour les agents RS (actionnable)
        Route::get('/agents', [App\Http\Controllers\AgentController::class, 'index'])->name('agents.index');
        Route::post('/agents', [App\Http\Controllers\AgentController::class, 'store'])->name('agents.store');
        Route::put('/agents/{agent}', [App\Http\Controllers\AgentController::class, 'update'])->name('agents.update');
        Route::delete('/agents/{agent}', [App\Http\Controllers\AgentController::class, 'destroy'])->name('agents.destroy');

        // Routes spécialisées pour la création d'agents RS
        Route::get('/agent-rs/create', [App\Http\Controllers\Admin\AgentRSController::class, 'create'])->name('agent-rs.create');
        Route::post('/agent-rs', [App\Http\Controllers\Admin\AgentRSController::class, 'store'])->name('agent-rs.store');

        // Routes pour les agents MS (consultation seulement)
        Route::get('/agents-ms', [App\Http\Controllers\Admin\AgentMSController::class, 'index'])->name('agents-ms.index');
        Route::get('/agents-ms/{agent}', [App\Http\Controllers\Admin\AgentMSController::class, 'show'])->name('agents-ms.show');

        // Routes pour les sous-structures (vue seulement)
        Route::get('/sous-structures', [App\Http\Controllers\Admin\SousStructureController::class, 'index'])->name('sous-structures.index');
        Route::get('/sous-structures/{structure}', [App\Http\Controllers\Admin\SousStructureController::class, 'show'])->name('sous-structures.show');

        // Routes pour les organigrammes (vue seulement)
        Route::get('/organigrammes', [App\Http\Controllers\Admin\OrganigrammeController::class, 'index'])->name('organigrammes.index');
        Route::get('/organigrammes/{structure}', [App\Http\Controllers\Admin\OrganigrammeController::class, 'show'])->name('organigrammes.show');

        // Routes pour la gestion des universités (CRUD complet)
        Route::get('/universites', [App\Http\Controllers\Admin\UniversiteController::class, 'index'])->name('universites.index');
        Route::get('/universites/create', [App\Http\Controllers\Admin\UniversiteController::class, 'create'])->name('universites.create');
        Route::post('/universites', [App\Http\Controllers\Admin\UniversiteController::class, 'store'])->name('universites.store');
        Route::get('/universites/{universite}', [App\Http\Controllers\Admin\UniversiteController::class, 'show'])->name('universites.show');
        Route::get('/universites/{universite}/edit', [App\Http\Controllers\Admin\UniversiteController::class, 'edit'])->name('universites.edit');
        Route::put('/universites/{universite}', [App\Http\Controllers\Admin\UniversiteController::class, 'update'])->name('universites.update');
        Route::delete('/universites/{universite}', [App\Http\Controllers\Admin\UniversiteController::class, 'destroy'])->name('universites.destroy');
        Route::post('/universites/{universite}/toggle-active', [App\Http\Controllers\Admin\UniversiteController::class, 'toggleActive'])->name('universites.toggle-active');

        // Routes pour la gestion des responsables RU (CRUD complet)
        Route::get('/responsables-ru', [App\Http\Controllers\Admin\ResponsableRUController::class, 'index'])->name('responsables-ru.index');
        Route::get('/responsables-ru/create', [App\Http\Controllers\Admin\ResponsableRUController::class, 'create'])->name('responsables-ru.create');
        Route::post('/responsables-ru', [App\Http\Controllers\Admin\ResponsableRUController::class, 'store'])->name('responsables-ru.store');
        Route::get('/responsables-ru/{responsableRu}', [App\Http\Controllers\Admin\ResponsableRUController::class, 'show'])->name('responsables-ru.show');
        Route::get('/responsables-ru/{responsableRu}/edit', [App\Http\Controllers\Admin\ResponsableRUController::class, 'edit'])->name('responsables-ru.edit');
        Route::put('/responsables-ru/{responsableRu}', [App\Http\Controllers\Admin\ResponsableRUController::class, 'update'])->name('responsables-ru.update');
        Route::delete('/responsables-ru/{responsableRu}', [App\Http\Controllers\Admin\ResponsableRUController::class, 'destroy'])->name('responsables-ru.destroy');

    });

    // Routes pour les demandes de stage
    Route::post('/demande-stages', [DemandeController::class, 'store'])->name('demande_stages.store');
    Route::get('/mes-demandes', [DemandeController::class, 'index'])->name('mes.demandes');
    Route::get('/mes-demandes/{id}', [DemandeController::class, 'show'])->name('mes.demandes.show');
    Route::delete('/mes-demandes/{id}', [DemandeController::class, 'destroy'])->name('mes.demandes.annuler');
    Route::post('/demandes/recherche', [DemandeController::class, 'findByCode'])->name('demandes.search');
    Route::get('/demandes/recherche', [DemandeController::class, 'findByCode'])->name('demandes.search.get');
    Route::get('/recherche-code', [App\Http\Controllers\DemandeController::class, 'create'])->name('recherche.code');

    // Routes pour les emails
    Route::post('/api/emails/demande-confirmation', [EmailController::class, 'sendDemandeConfirmation']);
    Route::get('/api/emails/check-config', [EmailController::class, 'checkEmailConfig']);

    // Routes pour les stagiaires
    Route::resource('stagiaires', StagiaireController::class);

    // Routes pour les agents responsables de la structure DPAF
    Route::prefix('agent')->name('agent.')->middleware(['auth', 'verified'])->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Agent\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/demandes', [App\Http\Controllers\Agent\DemandeController::class, 'index'])->name('demandes');
        Route::get('/demandes/{demande}', [App\Http\Controllers\Agent\DemandeController::class, 'show'])->name('demandes.show');
        Route::post('/demandes/{demande}/approve', [App\Http\Controllers\Agent\DemandeController::class, 'approve'])->name('demandes.approve');
        Route::post('/demandes/{demande}/reject', [App\Http\Controllers\Agent\DemandeController::class, 'reject'])->name('demandes.reject');
        Route::get('/stages', [App\Http\Controllers\Agent\StageController::class, 'index'])->name('stages');
        Route::get('/stages/{stage}', [App\Http\Controllers\Agent\StageController::class, 'show'])->name('stages.show');
        Route::get('/structures', [App\Http\Controllers\Agent\StructureController::class, 'index'])->name('structures');
        Route::get('/structures/{structure}', [App\Http\Controllers\Agent\StructureController::class, 'show'])->name('structures.show');
        Route::get('/stagiaires', [App\Http\Controllers\Agent\StagiaireController::class, 'index'])->name('stagiaires.index');
        Route::get('/stagiaires/{stagiaire}', [App\Http\Controllers\Agent\StagiaireController::class, 'show'])->name('stagiaires.show');
        Route::post('/demandes/{demande}/affecter', [App\Http\Controllers\Agent\DemandeController::class, 'affecter'])->name('demandes.affecter');
        Route::post('/demandes/{demande}/refuse-definitivement', [App\Http\Controllers\Agent\DemandeController::class, 'refuseDefinitivement'])->name('demandes.refuseDefinitivement');

        // Routes pour les agents RS
        Route::prefix('rs')->name('rs.')->middleware(['auth'])->group(function () {
            Route::get('/dashboard', [App\Http\Controllers\Agent\RS\DashboardController::class, 'index'])->name('dashboard');
            Route::get('/demandes', [App\Http\Controllers\Agent\RS\DemandeController::class, 'index'])->name('demandes');
            Route::get('/demandes/{demande}', [App\Http\Controllers\Agent\RS\DemandeController::class, 'show'])->name('demandes.show');
            Route::post('/demandes/{demande}/approve', [App\Http\Controllers\Agent\RS\DemandeController::class, 'approve'])->name('demandes.approve');
            Route::post('/demandes/{demande}/reject', [App\Http\Controllers\Agent\RS\DemandeController::class, 'reject'])->name('demandes.reject');
            Route::get('/responsable-agents', [App\Http\Controllers\Agent\RS\DemandeController::class, 'getResponsableAgents'])->name('responsable-agents');
            Route::post('/demandes/{demande}/affecter-maitre', [App\Http\Controllers\Agent\RS\DemandeController::class, 'affecterMaitreStage'])->name('demandes.affecter-maitre');
            // Routes pour les stages
            Route::get('/stages', [App\Http\Controllers\Agent\RS\StageController::class, 'index'])->name('stages');
            Route::get('/stages/{stage}', [App\Http\Controllers\Agent\RS\StageController::class, 'show'])->name('stages.show');
            Route::post('/stages/{stage}/affecter-maitre', [App\Http\Controllers\Agent\RS\StageController::class, 'affecterMaitreStage'])->name('stages.affecter-maitre');
            Route::get('/stages/{stage}/attestation', [App\Http\Controllers\Agent\RS\StageController::class, 'attestation'])->name('stages.attestation');
            // Routes pour les notifications RS
            Route::get('/notifications', [App\Http\Controllers\Agent\RS\NotificationController::class, 'index'])->name('notifications.index');
            Route::post('/notifications/{id}/mark-as-read', [App\Http\Controllers\Agent\RS\NotificationController::class, 'markAsRead'])->name('notifications.markAsRead');
            Route::post('/notifications/mark-all-as-read', [App\Http\Controllers\Agent\RS\NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
            Route::delete('/notifications/{id}', [App\Http\Controllers\Agent\RS\NotificationController::class, 'destroy'])->name('notifications.destroy');
            Route::get('/notifications/unread', [App\Http\Controllers\Agent\RS\NotificationController::class, 'getUnread'])->name('notifications.unread');
            // CRUD agents sans rôle
            Route::resource('agents', App\Http\Controllers\Agent\RS\AgentController::class);
            // ROUTE PERSONNALISÉE D'ABORD
            Route::get('organigramme/sous-structures', [App\Http\Controllers\Agent\RS\StructureOrganigrammeController::class, 'sousStructures'])
                ->name('organigramme.sous-structures');
            // PUIS LA RESSOURCE
            Route::resource('organigramme', App\Http\Controllers\Agent\RS\StructureOrganigrammeController::class)
                ->parameters(['organigramme' => 'structure']);
            Route::post('organigramme/{structure}/assign-agent', [App\Http\Controllers\Agent\RS\StructureOrganigrammeController::class, 'assignAgent'])->name('organigramme.assign-agent');
        });

        // Routes pour les responsables université (RU)
        Route::prefix('ru')->name('ru.')->middleware(['auth'])->group(function () {
            Route::get('/dashboard', [App\Http\Controllers\Agent\RU\DashboardController::class, 'index'])->name('dashboard');
            Route::get('/universite', [App\Http\Controllers\Agent\RU\UniversiteController::class, 'show'])->name('universite.show');
            Route::put('/universite', [App\Http\Controllers\Agent\RU\UniversiteController::class, 'update'])->name('universite.update');
            Route::get('/stages', [App\Http\Controllers\Agent\RU\StageController::class, 'index'])->name('stages.index');
            Route::get('/stages/{stage}', [App\Http\Controllers\Agent\RU\StageController::class, 'show'])->name('stages.show');
            Route::get('/stages/{stage}/print-notes', [App\Http\Controllers\Agent\RU\StageController::class, 'printNotes'])->name('stages.print-notes');
            Route::get('/etudiants', [App\Http\Controllers\Agent\RU\EtudiantController::class, 'index'])->name('etudiants.index');
            Route::get('/etudiants/{etudiant}', [App\Http\Controllers\Agent\RU\EtudiantController::class, 'show'])->name('etudiants.show');
            Route::get('/demandes', [App\Http\Controllers\Agent\RU\DemandeController::class, 'index'])->name('demandes.index');
            Route::get('/demandes/{demande}', [App\Http\Controllers\Agent\RU\DemandeController::class, 'show'])->name('demandes.show');
            Route::get('/rapports', [App\Http\Controllers\Agent\RU\RapportController::class, 'index'])->name('rapports.index');

            // AMÉLIORATION : Routes pour la gestion des évaluations
            Route::get('/evaluations', [App\Http\Controllers\Agent\RU\EvaluationController::class, 'index'])->name('evaluations.index');
            Route::get('/evaluations/{evaluation}', [App\Http\Controllers\Agent\RU\EvaluationController::class, 'show'])->name('evaluations.show');
            Route::post('/evaluations/{evaluation}/valider', [App\Http\Controllers\Agent\RU\EvaluationController::class, 'valider'])->name('evaluations.valider');
            Route::get('/evaluations/{evaluation}/pdf', [App\Http\Controllers\Agent\RU\EvaluationController::class, 'genererPDF'])->name('evaluations.pdf');

            // Routes pour le profil RU
            Route::get('/profile', [App\Http\Controllers\Agent\RU\ProfileController::class, 'edit'])->name('profile.edit');
            Route::patch('/profile', [App\Http\Controllers\Agent\RU\ProfileController::class, 'update'])->name('profile.update');
            Route::delete('/profile', [App\Http\Controllers\Agent\RU\ProfileController::class, 'destroy'])->name('profile.destroy');
        });

        // Routes pour les maîtres de stage (MS)
        Route::prefix('ms')->name('ms.')->middleware(['auth', 'ms'])->group(function () {
            Route::get('/dashboard', [App\Http\Controllers\Agent\MS\DashboardController::class, 'index'])->name('dashboard');
            Route::get('/stages', [App\Http\Controllers\Agent\MS\StageController::class, 'index'])->name('stages');
            Route::get('/stages/{stage}', [App\Http\Controllers\Agent\MS\StageController::class, 'show'])->name('stages.show');
            Route::post('/stages/{stage}/update-status', [App\Http\Controllers\Agent\MS\StageController::class, 'updateStatus'])->name('stages.update-status');
            Route::post('/stages/{stage}/theme', [App\Http\Controllers\Agent\MS\StageController::class, 'storeTheme'])->name('stages.theme.store');
            Route::post('/stages/{stage}/valider-theme', [App\Http\Controllers\Agent\MS\StageController::class, 'validerTheme'])->name('stages.valider-theme');
            Route::post('/stages/{stage}/refuser-theme', [App\Http\Controllers\Agent\MS\StageController::class, 'refuserTheme'])->name('stages.refuser-theme');
            Route::post('/stages/{stage}/noter', [App\Http\Controllers\Agent\MS\StageController::class, 'noter'])->name('stages.noter');
            // AMÉLIORATION : Routes pour les formats d'évaluation personnalisés
            Route::get('/stages/{stage}/format-evaluation', [App\Http\Controllers\Agent\MS\StageController::class, 'getFormatEvaluation'])->name('stages.format-evaluation');
            Route::get('/stages/{stage}/format-evaluation/{universiteNom}', [App\Http\Controllers\Agent\MS\StageController::class, 'getFormatEvaluationUniversite'])->name('stages.format-evaluation-universite');
            Route::post('/stages/{stage}/format-evaluation', [App\Http\Controllers\Agent\MS\StageController::class, 'creerFormatEvaluation'])->name('stages.creer-format-evaluation');
            Route::get('/stages/{stage}/maitres-stage-substructures', [App\Http\Controllers\Agent\MS\StageController::class, 'getMaitresStageSubstructures'])->name('stages.maitres-stage-substructures');
            Route::post('/stages/{stage}/reaffecter', [App\Http\Controllers\Agent\MS\StageController::class, 'reaffecter'])->name('stages.reaffecter');
            // Routes pour les notifications MS
            Route::get('/notifications', [App\Http\Controllers\Agent\MS\NotificationController::class, 'index'])->name('notifications.index');
            Route::post('/notifications/{id}/mark-as-read', [App\Http\Controllers\Agent\MS\NotificationController::class, 'markAsRead'])->name('notifications.markAsRead');
            Route::post('/notifications/mark-all-as-read', [App\Http\Controllers\Agent\MS\NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
            Route::delete('/notifications/{id}', [App\Http\Controllers\Agent\MS\NotificationController::class, 'destroy'])->name('notifications.destroy');
            Route::get('/notifications/unread', [App\Http\Controllers\Agent\MS\NotificationController::class, 'getUnread'])->name('notifications.unread');
            Route::get('/stages/{stage}/themes-proposes', [App\Http\Controllers\Agent\MS\StageController::class, 'getThemesProposes'])->name('stages.themes-proposes');
            Route::post('/stages/{stage}/themes/{theme}/action', [App\Http\Controllers\Agent\MS\StageController::class, 'validerOuRefuserTheme'])->name('stages.theme-action');
            // Route pour récupérer l'évaluation individuelle d'un membre du groupe
            Route::get('/stages/{stage}/evaluations/{membre}', [App\Http\Controllers\Agent\MS\StageController::class, 'getEvaluationMembre']);

            // Route pour envoyer un message à un stagiaire
            Route::post('/stages/send-message', [StagiaireMessageController::class, 'sendMessage'])->name('stages.send-message');

            Route::post('/stages/{stage}/confirmer-fin', [App\Http\Controllers\Agent\MS\StageController::class, 'confirmerFinStage'])
                ->name('ms.stages.confirmer-fin');

            // Route pour permettre au maître de stage de proposer un thème
            Route::post('/stages/{stage}/themes', [App\Http\Controllers\Agent\MS\StageController::class, 'proposerTheme'])->name('stages.proposer-theme');
        });
    });

    // Routes pour les stagiaires
    Route::prefix('stagiaire')->name('stagiaire.')->middleware(['auth', 'verified'])->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Stagiaire\DashboardController::class, 'index'])->name('dashboard');
        // Routes pour les stages
        Route::get('/stages', [App\Http\Controllers\Stagiaire\StageController::class, 'index'])->name('stages');
        Route::get('/stages/{stage}', [App\Http\Controllers\Stagiaire\StageController::class, 'show'])->name('stages.show');
        Route::post('/stages/{stage}/themes', [App\Http\Controllers\Stagiaire\StageController::class, 'proposerTheme'])->name('stages.proposer-theme');
    });

    // Route pour télécharger le rapport mensuel
    Route::get('/admin/rapport-mensuel', [RapportController::class, 'telecharger'])->name('admin.rapport-mensuel');

    Route::post('/stagiaire/notifications/{id}/mark-as-read', [
        \App\Http\Controllers\Stagiaire\NotificationController::class,
        'markAsRead'
    ])->name('stagiaire.notifications.markAsRead');
});

// Routes pour les Responsables Université (RU)
Route::prefix('responsable-universite')->name('responsable-universite.')->middleware(['auth', 'verified'])->group(function () {
    Route::get('/evaluations', [App\Http\Controllers\ResponsableUniversite\EvaluationController::class, 'index'])->name('evaluations.index');
    Route::post('/evaluations/{evaluation}/valider', [App\Http\Controllers\ResponsableUniversite\EvaluationController::class, 'valider'])->name('evaluations.valider');
});

// Routes pour les notifications des agents (RS, MS, RU)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::post('/agent/notifications/{id}/mark-as-read', [
        \App\Http\Controllers\Api\NotificationController::class,
        'markAsReadInertia'
    ])->name('agent.notifications.markAsRead');
});

// AMÉLIORATION : Routes de test pour le système d'évaluation
require __DIR__.'/test-evaluation-system.php';
require __DIR__.'/test-simple.php';
require __DIR__.'/test-attestation-workflow.php';

// Routes pour les notifications admin
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/notifications', [
        \App\Http\Controllers\Admin\NotificationController::class,
        'index'
    ])->name('notifications.index');

    Route::post('/notifications/{id}/mark-as-read', [
        \App\Http\Controllers\Admin\NotificationController::class,
        'markAsRead'
    ])->name('notifications.markAsRead');

    Route::post('/notifications/mark-all-as-read', [
        \App\Http\Controllers\Admin\NotificationController::class,
        'markAllAsRead'
    ])->name('notifications.markAllAsRead');

    Route::delete('/notifications/{id}', [
        \App\Http\Controllers\Admin\NotificationController::class,
        'destroy'
    ])->name('notifications.destroy');

    Route::get('/notifications/unread', [
        \App\Http\Controllers\Admin\NotificationController::class,
        'getUnread'
    ])->name('notifications.unread');
});

require __DIR__.'/auth.php';

// Routes de test pour les emails (à supprimer en production)
if (app()->environment(['local', 'testing'])) {
    require __DIR__.'/test-emails.php';
}