<template>
    <!-- APPROCHE CHIRURGICALE : Cloche de notifications avec positionnement adaptatif -->
    <div :class="[
        'z-50',
        positioning === 'fixed' ? 'fixed top-8 right-8' : 'absolute top-4 right-4'
    ]">
        <div class="relative">
            <button @click="showNotifications = !showNotifications" class="relative focus:outline-none group">
                <div :class="[
                    'p-3 rounded-full shadow-lg border transition-all duration-200 group-hover:shadow-xl',
                    getButtonClasses()
                ]">
                    <svg class="w-6 h-6 transition-colors" :class="getIconClasses()" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                </div>
                <span v-if="notifications.length" :class="[
                    'absolute -top-1 -right-1 text-white text-xs rounded-full px-2 py-1 font-medium shadow-md min-w-[1.25rem] text-center',
                    getBadgeClasses()
                ]">
                    {{ notifications.length > 99 ? '99+' : notifications.length }}
                </span>
            </button>
            
            <!-- Panel de notifications avec design modernisé -->
            <div v-if="showNotifications" :class="[
                'absolute right-0 mt-4 w-96 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden animate-fade-in z-50'
            ]">
                <div :class="['p-4 border-b', getHeaderClasses()]">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5" :class="getHeaderIconClasses()" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <span class="font-semibold text-gray-800">Notifications</span>
                            <span v-if="notifications.length" :class="['text-white text-xs px-2 py-1 rounded-full', getCountBadgeClasses()]">{{ notifications.length }}</span>
                        </div>
                        <button @click="showNotifications = false" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-white/50 transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div v-if="notifications.length === 0" class="p-8 text-center">
                    <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                    </svg>
                    <p class="text-gray-500 font-medium">Aucune notification</p>
                    <p class="text-sm text-gray-400 mt-1">Vous êtes à jour !</p>
                </div>
                
                <ul v-else class="divide-y divide-gray-100 max-h-96 overflow-y-auto">
                    <li v-for="notif in notifications" :key="notif.id" class="p-4 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start gap-3">
                            <div class="pt-1 flex-shrink-0">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="getNotificationIconBg(notif)">
                                    <svg v-if="notif.data.message.includes('acceptée')" class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    <svg v-else-if="notif.data.message.includes('refusée')" class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    <svg v-else-if="notif.data.message.includes('maître de stage')" class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <svg v-else class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" />
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 mb-1 leading-relaxed" v-html="notif.data.message"></div>
                                <div v-if="notif.data.url" class="mb-2">
                                    <a :href="notif.data.url" class="inline-flex items-center text-xs text-blue-600 hover:text-blue-700 font-medium hover:underline">
                                        Voir les détails
                                        <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </a>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="text-xs text-gray-500">{{ formatDate(notif.created_at) }}</div>
                                    <button @click="markAsRead(notif.id)" class="text-xs text-gray-400 hover:text-green-600 font-medium hover:bg-green-50 px-2 py-1 rounded transition-colors" title="Marquer comme lue">
                                        Marquer comme lue
                                    </button>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { usePage, router } from '@inertiajs/vue3'

const props = defineProps({
    userRole: {
        type: String,
        required: true,
        validator: (value) => ['admin', 'agent', 'stagiaire'].includes(value)
    },
    theme: {
        type: String,
        default: 'blue',
        validator: (value) => ['blue', 'emerald', 'purple', 'amber'].includes(value)
    },
    notifications: {
        type: Array,
        default: () => []
    },
    // APPROCHE CHIRURGICALE : Nouveau prop pour contrôler le positionnement
    positioning: {
        type: String,
        default: 'fixed',
        validator: (value) => ['fixed', 'absolute'].includes(value)
    }
})

const showNotifications = ref(false)
const page = usePage()

const getButtonClasses = () => {
    const themes = {
        blue: 'bg-white border-gray-200 group-hover:border-blue-300',
        emerald: 'bg-white border-gray-200 group-hover:border-emerald-300',
        purple: 'bg-white border-gray-200 group-hover:border-purple-300',
        amber: 'bg-white border-gray-200 group-hover:border-amber-300'
    }
    return themes[props.theme]
}

const getIconClasses = () => {
    const themes = {
        blue: 'text-blue-600 group-hover:text-blue-700',
        emerald: 'text-emerald-600 group-hover:text-emerald-700',
        purple: 'text-purple-600 group-hover:text-purple-700',
        amber: 'text-amber-600 group-hover:text-amber-700'
    }
    return themes[props.theme]
}

const getBadgeClasses = () => {
    const themes = {
        blue: 'bg-red-500',
        emerald: 'bg-red-500',
        purple: 'bg-red-500',
        amber: 'bg-red-500'
    }
    return themes[props.theme]
}

const getHeaderClasses = () => {
    const themes = {
        blue: 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200',
        emerald: 'bg-gradient-to-r from-emerald-50 to-emerald-100 border-emerald-200',
        purple: 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200',
        amber: 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200'
    }
    return themes[props.theme]
}

const getHeaderIconClasses = () => {
    const themes = {
        blue: 'text-blue-600',
        emerald: 'text-emerald-600',
        purple: 'text-purple-600',
        amber: 'text-amber-600'
    }
    return themes[props.theme]
}

const getCountBadgeClasses = () => {
    const themes = {
        blue: 'bg-blue-600',
        emerald: 'bg-emerald-600',
        purple: 'bg-purple-600',
        amber: 'bg-amber-600'
    }
    return themes[props.theme]
}

const getNotificationIconBg = (notif) => {
    if (notif.data.message.includes('acceptée')) return 'bg-green-100'
    if (notif.data.message.includes('refusée')) return 'bg-red-100'
    if (notif.data.message.includes('maître de stage')) return 'bg-blue-100'
    return 'bg-gray-100'
}

const formatDate = (date) => {
    return new Date(date).toLocaleString('fr-FR')
}

const markAsRead = (notificationId) => {
    const routes = {
        admin: 'admin.notifications.markAsRead',
        agent: 'agent.notifications.markAsRead',
        stagiaire: 'stagiaire.notifications.markAsRead'
    }
    
    const routeName = routes[props.userRole]
    if (routeName) {
        router.post(route(routeName, notificationId), {}, {
            preserveScroll: true,
        })
    }
}
</script>

<style scoped>
.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
</style>
