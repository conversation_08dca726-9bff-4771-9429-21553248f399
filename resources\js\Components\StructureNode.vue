<template>
  <div class="structure-node">
    <!-- Nœud principal -->
    <div 
      :class="[
        'flex items-center gap-4 p-4 rounded-xl border-2 transition-all duration-200',
        getNodeClasses()
      ]"
      :style="{ marginLeft: `${level * 2}rem` }"
    >
      <!-- Bouton d'expansion -->
      <button 
        v-if="hasChildren"
        @click="$emit('toggle', structure.id)"
        class="flex-shrink-0 w-8 h-8 rounded-full bg-slate-100 hover:bg-slate-200 flex items-center justify-center transition-colors"
      >
        <svg 
          :class="['w-4 h-4 text-slate-600 transition-transform', { 'rotate-90': isExpanded }]"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
      </button>
      
      <!-- Icône de structure -->
      <div :class="getIconClasses()">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
        </svg>
      </div>

      <!-- Informations de la structure -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-3 mb-2">
          <h3 class="text-lg font-bold text-slate-800">{{ structure.sigle }}</h3>
          <span :class="getTypeClasses()">
            {{ structure.type_structure || 'Structure' }}
          </span>
        </div>
        <p class="text-sm text-slate-600 mb-2">{{ structure.libelle }}</p>
        
        <!-- Responsable -->
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2">
            <div v-if="structure.responsable" class="flex items-center gap-2">
              <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                <span class="text-xs font-bold text-white">
                  {{ getInitials(structure.responsable.user) }}
                </span>
              </div>
              <div>
                <p class="text-sm font-semibold text-slate-800">
                  {{ getFullName(structure.responsable.user) }}
                </p>
                <p class="text-xs text-slate-500">Responsable de Structure</p>
              </div>
            </div>
            <div v-else class="flex items-center gap-2">
              <div class="w-8 h-8 rounded-full bg-slate-300 flex items-center justify-center">
                <svg class="w-4 h-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
              <div>
                <p class="text-sm font-medium text-slate-500 italic">Aucun responsable assigné</p>
                <p class="text-xs text-slate-400">Poste vacant</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Agents MS dans cette structure -->
        <div v-if="structureAgents.length > 0" class="mt-3 pt-3 border-t border-slate-200">
          <p class="text-xs font-semibold text-slate-600 mb-2">Agents MS ({{ structureAgents.length }})</p>
          <div class="flex flex-wrap gap-2">
            <div 
              v-for="agent in structureAgents.slice(0, 3)" 
              :key="agent.id"
              class="flex items-center gap-1 px-2 py-1 bg-purple-100 rounded-lg"
            >
              <div class="w-5 h-5 rounded-full bg-purple-500 flex items-center justify-center">
                <span class="text-xs font-bold text-white">
                  {{ getInitials(agent.user) }}
                </span>
              </div>
              <span class="text-xs font-medium text-purple-700">
                {{ getFullName(agent.user) }}
              </span>
            </div>
            <div v-if="structureAgents.length > 3" class="flex items-center px-2 py-1 bg-slate-100 rounded-lg">
              <span class="text-xs font-medium text-slate-600">
                +{{ structureAgents.length - 3 }} autres
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Statistiques -->
      <div class="flex-shrink-0 text-right">
        <div class="flex items-center gap-4">
          <div v-if="hasChildren" class="text-center">
            <p class="text-lg font-bold text-slate-800">{{ structure.children.length }}</p>
            <p class="text-xs text-slate-500">Sous-structures</p>
          </div>
          <div class="text-center">
            <p class="text-lg font-bold text-slate-800">{{ structureAgents.length }}</p>
            <p class="text-xs text-slate-500">Agents MS</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Sous-structures (récursif) -->
    <div v-if="hasChildren && isExpanded" class="mt-4 space-y-3">
      <StructureNode
        v-for="child in structure.children"
        :key="child.id"
        :structure="child"
        :agents="agents"
        :expanded="expanded"
        @toggle="$emit('toggle', $event)"
        :level="level + 1"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'StructureNode'
};
</script>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  structure: Object,
  agents: Array,
  expanded: Set,
  level: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['toggle']);

// Computed properties
const hasChildren = computed(() => {
  return props.structure.children && props.structure.children.length > 0;
});

const isExpanded = computed(() => {
  return props.expanded.has(props.structure.id);
});

const structureAgents = computed(() => {
  return props.agents.filter(agent => 
    agent.structure_id === props.structure.id && agent.role_agent === 'MS'
  );
});

// Méthodes de style
const getNodeClasses = () => {
  const baseClasses = 'bg-white hover:shadow-lg';
  
  if (props.level === 0) {
    return `${baseClasses} border-indigo-200 shadow-md`;
  } else if (props.level === 1) {
    return `${baseClasses} border-blue-200 shadow-sm`;
  } else {
    return `${baseClasses} border-slate-200 shadow-sm`;
  }
};

const getIconClasses = () => {
  if (props.level === 0) {
    return 'w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500 to-indigo-600 flex items-center justify-center shadow-lg';
  } else if (props.level === 1) {
    return 'w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-md';
  } else {
    return 'w-8 h-8 rounded-lg bg-gradient-to-br from-slate-500 to-slate-600 flex items-center justify-center shadow-sm';
  }
};

const getTypeClasses = () => {
  const type = props.structure.type_structure;
  if (type === 'Direction') {
    return 'px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs font-semibold';
  } else if (type === 'Service') {
    return 'px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-semibold';
  } else if (type === 'Département') {
    return 'px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-semibold';
  } else {
    return 'px-3 py-1 bg-slate-100 text-slate-700 rounded-full text-xs font-semibold';
  }
};

// Fonctions helper pour les noms
const getFullName = (user) => {
  if (!user) return 'Non défini';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  return `${prenom} ${nom}`.trim() || 'Non défini';
};

const getInitials = (user) => {
  if (!user) return 'N';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  const prenomInitial = prenom.charAt(0).toUpperCase();
  const nomInitial = nom.charAt(0).toUpperCase();
  return `${prenomInitial}${nomInitial}` || 'N';
};
</script>

<style scoped>
.structure-node {
  position: relative;
}

.structure-node::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #e2e8f0, transparent);
}

.structure-node:last-child::before {
  background: linear-gradient(to bottom, #e2e8f0 50%, transparent 50%);
}
</style>
