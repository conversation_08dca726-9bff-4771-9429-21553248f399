<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\DemandeStage;
use App\Models\User;

class DPAFNouvelleDemandeMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $demande;
    public $stagiaire;

    /**
     * Create a new message instance.
     */
    public function __construct(DemandeStage $demande)
    {
        $this->demande = $demande;
        $this->stagiaire = $demande->stagiaire->user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Nouvelle demande de stage à traiter - ' . $this->demande->code_suivi,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.dpaf-nouvelle-demande',
            with: [
                'demande' => $this->demande,
                'stagiaire' => $this->stagiaire,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
