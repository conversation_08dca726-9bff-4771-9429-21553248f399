<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Stage;

class StagiaireFinStageMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $stage;
    public $stagiaire;

    /**
     * Create a new message instance.
     */
    public function __construct(Stage $stage)
    {
        $this->stage = $stage;
        $this->stagiaire = $stage->stagiaire->user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Confirmation de fin de stage - ' . $this->stage->demandeStage->code_suivi,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.stagiaire-fin-stage',
            with: [
                'stage' => $this->stage,
                'stagiaire' => $this->stagiaire,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
