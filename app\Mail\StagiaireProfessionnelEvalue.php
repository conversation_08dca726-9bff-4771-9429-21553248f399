<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Stagiaire;
use App\Models\Stage;
use App\Models\Evaluation;

class StagiaireProfessionnelEvalue extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $stagiaire;
    public $stage;
    public $evaluation;

    /**
     * Create a new message instance.
     */
    public function __construct(Stagiaire $stagiaire, Stage $stage, Evaluation $evaluation)
    {
        $this->stagiaire = $stagiaire;
        $this->stage = $stage;
        $this->evaluation = $evaluation;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Résultats de votre évaluation de stage professionnel',
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.stagiaire-professionnel-evalue',
            with: [
                'stagiaire' => $this->stagiaire,
                'stage' => $this->stage,
                'evaluation' => $this->evaluation,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
