<?php

namespace Tests\Feature;

use App\Models\Agent;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AgentHierarchyTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function admin_can_only_see_agents_they_created()
    {
        // Créer deux admins
        $admin1 = User::factory()->create(['role' => 'admin']);
        $admin2 = User::factory()->create(['role' => 'admin']);

        // Créer des agents pour chaque admin
        $agent1 = Agent::factory()->create(['created_by' => $admin1->id]);
        $agent2 = Agent::factory()->create(['created_by' => $admin2->id]);

        // Admin1 ne devrait voir que son agent
        $this->actingAs($admin1);
        $response = $this->get(route('agents.index'));
        
        $response->assertStatus(200);
        $agents = $response->viewData('agents') ?? collect();
        
        $this->assertTrue($agents->contains('id', $agent1->id));
        $this->assertFalse($agents->contains('id', $agent2->id));
    }

    /** @test */
    public function admin_cannot_modify_agents_created_by_others()
    {
        // Créer deux admins
        $admin1 = User::factory()->create(['role' => 'admin']);
        $admin2 = User::factory()->create(['role' => 'admin']);

        // Créer un agent pour admin2
        $agent = Agent::factory()->create(['created_by' => $admin2->id]);

        // Admin1 essaie de modifier l'agent d'admin2
        $this->actingAs($admin1);
        $response = $this->put(route('agents.update', $agent), [
            'nom' => 'Nouveau nom',
            'prenom' => 'Nouveau prénom',
            'email' => '<EMAIL>',
            'telephone' => '12345678',
            'date_de_naissance' => '1990-01-01',
            'sexe' => 'Homme',
            'matricule' => 'MAT123',
            'fonction' => 'Nouvelle fonction',
            'role_agent' => 'RS',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Vous ne pouvez modifier que les agents que vous avez créés.');
    }

    /** @test */
    public function admin_cannot_delete_agents_created_by_others()
    {
        // Créer deux admins
        $admin1 = User::factory()->create(['role' => 'admin']);
        $admin2 = User::factory()->create(['role' => 'admin']);

        // Créer un agent pour admin2
        $agent = Agent::factory()->create(['created_by' => $admin2->id]);

        // Admin1 essaie de supprimer l'agent d'admin2
        $this->actingAs($admin1);
        $response = $this->delete(route('agents.destroy', $agent));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Vous ne pouvez supprimer que les agents que vous avez créés.');
        
        // Vérifier que l'agent existe toujours
        $this->assertDatabaseHas('agents', ['id' => $agent->id]);
    }
}
