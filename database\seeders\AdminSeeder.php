<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Carbon;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== Création des utilisateurs administrateurs ===');

        // Données des administrateurs
        $admins = [
            [
                'nom' => 'Admin',
                'prenom' => 'Un',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'date_de_naissance' => Carbon::create(1980, 1, 15),
                'sexe' => 'Homme',
                'adresse' => 'Adresse Admin 1, <PERSON><PERSON>ou',
                'telephone' => '+229 90 12 34 56',
            ],
            [
                'nom' => 'Admin',
                'prenom' => 'Deux',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'date_de_naissance' => Carbon::create(1985, 6, 20),
                'sexe' => 'Femme',
                'adresse' => 'Adresse Admin 2, <PERSON><PERSON><PERSON>',
                'telephone' => '+229 97 65 43 21',
            ]
        ];

        foreach ($admins as $adminData) {
            // Vérifier si l'utilisateur existe déjà
            $existingUser = User::where('email', $adminData['email'])->first();
            
            if ($existingUser) {
                $this->command->warn("❌ L'utilisateur {$adminData['email']} existe déjà (ID: {$existingUser->id})");
                continue;
            }
            
            try {
                // Créer l'utilisateur admin
                $user = User::create([
                    'nom' => $adminData['nom'],
                    'prenom' => $adminData['prenom'],
                    'date_de_naissance' => $adminData['date_de_naissance'],
                    'sexe' => $adminData['sexe'],
                    'adresse' => $adminData['adresse'],
                    'email' => $adminData['email'],
                    'password' => Hash::make($adminData['password']),
                    'telephone' => $adminData['telephone'],
                    'date_d_inscription' => now(),
                    'role' => 'admin',
                    'remember_token' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                // Assigner le rôle admin avec Spatie Permission
                try {
                    if (class_exists('Spatie\Permission\Models\Role')) {
                        $adminRole = \Spatie\Permission\Models\Role::where('name', 'admin')->first();
                        if ($adminRole) {
                            $user->assignRole('admin');
                            $this->command->info("✅ Rôle Spatie 'admin' assigné à {$adminData['email']}");
                        }
                    }
                } catch (\Exception $e) {
                    $this->command->warn("⚠️  Erreur lors de l'assignation du rôle Spatie : " . $e->getMessage());
                }
                
                $this->command->info("✅ Utilisateur admin créé avec succès :");
                $this->command->info("   - Email: {$adminData['email']}");
                $this->command->info("   - Mot de passe: {$adminData['password']}");
                $this->command->info("   - ID: {$user->id}");
                $this->command->info("   - Rôle: {$user->role}");
                
            } catch (\Exception $e) {
                $this->command->error("❌ Erreur lors de la création de {$adminData['email']} : " . $e->getMessage());
            }
        }

        $this->command->info('=== Vérification des utilisateurs admin créés ===');

        $adminUsers = User::where('role', 'admin')->get();
        $this->command->info("Nombre total d'administrateurs : " . $adminUsers->count());

        foreach ($adminUsers as $admin) {
            $this->command->info("👤 Admin ID {$admin->id} :");
            $this->command->info("   - Nom complet: {$admin->prenom} {$admin->nom}");
            $this->command->info("   - Email: {$admin->email}");
            $this->command->info("   - Rôle: {$admin->role}");
            $this->command->info("   - Date création: {$admin->created_at}");
            
            // Vérifier les rôles Spatie si disponibles
            try {
                if (method_exists($admin, 'getRoleNames')) {
                    $spatieRoles = $admin->getRoleNames();
                    $this->command->info("   - Rôles Spatie: " . $spatieRoles->implode(', '));
                }
            } catch (\Exception $e) {
                $this->command->info("   - Rôles Spatie: Non disponibles");
            }
        }

        $this->command->info('=== Script terminé ===');
        $this->command->info('Les administrateurs peuvent maintenant se connecter avec :');
        $this->command->info('- <EMAIL> / password123');
        $this->command->info('- <EMAIL> / password123');
    }
}
