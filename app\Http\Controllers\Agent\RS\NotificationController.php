<?php

namespace App\Http\Controllers\Agent\RS;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class NotificationController extends Controller
{
    /**
     * Affiche la liste des notifications pour l'agent RS.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Récupérer les notifications de l'agent RS
        $notifications = $user->notifications()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return Inertia::render('Agent/RS/Notifications/Index', [
            'notifications' => $notifications,
        ]);
    }

    /**
     * Marque une notification comme lue.
     */
    public function markAsRead($id)
    {
        $user = Auth::user();
        
        $notification = $user->notifications()->find($id);
        
        if ($notification) {
            $notification->markAsRead();
            
            return response()->json([
                'success' => true,
                'message' => 'Notification marquée comme lue'
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Notification non trouvée'
        ], 404);
    }

    /**
     * Marque toutes les notifications comme lues.
     */
    public function markAllAsRead()
    {
        $user = Auth::user();
        
        $user->unreadNotifications->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => 'Toutes les notifications ont été marquées comme lues'
        ]);
    }

    /**
     * Supprime une notification.
     */
    public function destroy($id)
    {
        $user = Auth::user();
        
        $notification = $user->notifications()->find($id);
        
        if ($notification) {
            $notification->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Notification supprimée'
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Notification non trouvée'
        ], 404);
    }

    /**
     * Récupère les notifications non lues pour l'API.
     */
    public function getUnread()
    {
        $user = Auth::user();
        
        $notifications = $user->unreadNotifications()
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'notifications' => $notifications,
            'unread_count' => $user->unreadNotifications()->count()
        ]);
    }
}
