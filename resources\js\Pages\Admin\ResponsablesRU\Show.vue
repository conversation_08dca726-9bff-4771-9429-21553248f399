<template>
  <Head :title="`${responsableRu.user.prenom} ${responsableRu.user.nom} - Responsable RU`" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
          <div class="flex items-center gap-4">
            <div class="p-3 bg-blue-600 rounded-xl shadow-lg">
              <UserIcon class="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 class="text-3xl font-bold text-slate-800">
                {{ responsableRu.user.prenom }} {{ responsableRu.user.nom }}
              </h1>
              <p class="text-slate-600 mt-1">Responsable Université (RU)</p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <Link
              :href="route('admin.responsables-ru.edit', responsableRu.id)"
              class="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <PencilIcon class="w-4 h-4" />
              Modifier
            </Link>
            <Link
              :href="route('admin.responsables-ru.index')"
              class="inline-flex items-center gap-2 border border-slate-300 text-slate-700 hover:bg-slate-50 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <ArrowLeftIcon class="w-5 h-5" />
              Retour
            </Link>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Informations principales -->
          <div class="lg:col-span-2 space-y-8">
            <!-- Informations personnelles -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-blue-100">
                <h3 class="text-lg font-semibold text-blue-900">Informations personnelles</h3>
              </div>
              <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Nom complet</label>
                    <p class="text-lg font-semibold text-gray-900">
                      {{ responsableRu.user.prenom }} {{ responsableRu.user.nom }}
                    </p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Email</label>
                    <p class="text-gray-900">{{ responsableRu.user.email }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Téléphone</label>
                    <p class="text-gray-900">{{ responsableRu.user.telephone }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Date de naissance</label>
                    <p class="text-gray-900">{{ formatDate(responsableRu.user.date_de_naissance) }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Sexe</label>
                    <p class="text-gray-900">{{ responsableRu.user.sexe }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Informations professionnelles -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-50 to-emerald-100">
                <h3 class="text-lg font-semibold text-emerald-900">Informations professionnelles</h3>
              </div>
              <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Matricule</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      {{ responsableRu.matricule }}
                    </span>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Fonction</label>
                    <p class="text-gray-900">{{ responsableRu.fonction }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Rôle</label>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                      Responsable Université (RU)
                    </span>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-500 mb-1">Date d'embauche</label>
                    <p class="text-gray-900">{{ formatDate(responsableRu.date_embauche) }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Université assignée -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-amber-50 to-amber-100">
                <h3 class="text-lg font-semibold text-amber-900">Université assignée</h3>
              </div>
              <div class="p-6">
                <div v-if="responsableRu.universite_responsable" class="space-y-4">
                  <div class="flex items-start gap-4">
                    <div class="p-3 bg-amber-100 rounded-lg">
                      <AcademicCapIcon class="w-6 h-6 text-amber-600" />
                    </div>
                    <div class="flex-1">
                      <h4 class="text-lg font-semibold text-gray-900">
                        {{ responsableRu.universite_responsable.nom_complet }}
                      </h4>
                      <p class="text-gray-600 mt-1">{{ responsableRu.universite_responsable.sigle }}</p>
                      <div v-if="responsableRu.universite_responsable.description" class="mt-2">
                        <p class="text-sm text-gray-600">{{ responsableRu.universite_responsable.description }}</p>
                      </div>
                      <div v-if="responsableRu.universite_responsable.localisation" class="mt-2">
                        <p class="text-sm text-gray-600">
                          <MapPinIcon class="w-4 h-4 inline mr-1" />
                          {{ responsableRu.universite_responsable.localisation }}
                        </p>
                      </div>
                    </div>
                    <div class="text-right">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Actif
                      </span>
                    </div>
                  </div>
                  
                  <!-- Statistiques de l'université -->
                  <div v-if="responsableRu.universite_responsable.stagiaires" class="mt-6 pt-6 border-t border-gray-200">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Stagiaires de cette université</h5>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <div>
                            <p class="text-sm font-medium text-blue-600">Total stagiaires</p>
                            <p class="text-2xl font-bold text-blue-900">
                              {{ responsableRu.universite_responsable.stagiaires.length }}
                            </p>
                          </div>
                          <UserGroupIcon class="w-8 h-8 text-blue-600" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else class="text-center py-8">
                  <ExclamationTriangleIcon class="mx-auto h-12 w-12 text-orange-400" />
                  <h3 class="mt-2 text-sm font-medium text-gray-900">Aucune université assignée</h3>
                  <p class="mt-1 text-sm text-gray-500">Ce responsable RU n'a pas encore d'université assignée.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Informations système -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Informations système</h3>
              </div>
              <div class="p-6 space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1">Créé le</label>
                  <p class="text-sm text-gray-900">{{ formatDateTime(responsableRu.created_at) }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500 mb-1">Modifié le</label>
                  <p class="text-sm text-gray-900">{{ formatDateTime(responsableRu.updated_at) }}</p>
                </div>
                <div v-if="responsableRu.creator">
                  <label class="block text-sm font-medium text-gray-500 mb-1">Créé par</label>
                  <p class="text-sm text-gray-900">
                    {{ responsableRu.creator.prenom }} {{ responsableRu.creator.nom }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Actions rapides -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Actions rapides</h3>
              </div>
              <div class="p-6 space-y-3">
                <Link
                  :href="route('admin.responsables-ru.edit', responsableRu.id)"
                  class="w-full inline-flex items-center justify-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  <PencilIcon class="w-4 h-4" />
                  Modifier les informations
                </Link>
                
                <Link
                  v-if="responsableRu.universite_responsable"
                  :href="route('admin.universites.show', responsableRu.universite_responsable.id)"
                  class="w-full inline-flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  <AcademicCapIcon class="w-4 h-4" />
                  Voir l'université
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import {
  UserIcon,
  PencilIcon,
  ArrowLeftIcon,
  AcademicCapIcon,
  MapPinIcon,
  UserGroupIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  responsableRu: Object,
});

const formatDate = (dateString) => {
  if (!dateString) return 'Non disponible';
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatDateTime = (dateString) => {
  if (!dateString) return 'Non disponible';
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>
