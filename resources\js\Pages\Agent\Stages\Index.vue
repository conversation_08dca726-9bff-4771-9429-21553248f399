<template>
  <AgentDPAFLayout>
    <template #header>
      <div class="flex items-center gap-4 mb-2">
        <div class="bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-xl w-16 h-16 flex items-center justify-center shadow-lg transform transition-transform hover:scale-105">
          <DocumentTextIcon class="w-8 h-8" />
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800 leading-tight md:text-3xl tracking-tight">Suivi Global des Stages</h1>
          <p class="text-sm text-gray-600 mt-1 font-medium">
            Vue d'ensemble de tous les stages - Toutes structures confondues
          </p>
        </div>
      </div>
    </template>

    <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
          
          <!-- Statistiques rapides -->
          <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold">{{ stages.total }}</div>
                <div class="text-sm opacity-90">Total Stages</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">{{ getStagesByStatus('En cours') }}</div>
                <div class="text-sm opacity-90">En Cours</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">{{ getStagesByStatus('Terminé') }}</div>
                <div class="text-sm opacity-90">Terminés</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold">{{ structures.length }}</div>
                <div class="text-sm opacity-90">Structures</div>
              </div>
            </div>
          </div>

          <div class="p-8">
            <!-- Filtres avec style glassmorphisme moderne -->
            <div class="mb-8">
              <div class="bg-gradient-to-r from-gray-50 to-gray-100/80 backdrop-blur-xl p-6 rounded-2xl border border-gray-200/50 shadow-sm">
                <form @submit.prevent="applyFilters" class="flex flex-col lg:flex-row flex-wrap gap-6">
                  <div class="w-full lg:w-auto min-w-[200px]">
                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2">Statut du stage</label>
                    <select
                      v-model="filters.status"
                      id="status"
                      class="w-full rounded-xl border-0 bg-white shadow-sm ring-1 ring-gray-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 text-sm px-4 py-3 transition-all duration-200 hover:shadow-md"
                    >
                      <option value="all">🔍 Tous les statuts</option>
                      <option value="En attente">⏳ En attente</option>
                      <option value="En cours">🔄 En cours</option>
                      <option value="Terminé">✅ Terminé</option>
                      <option value="Annulé">❌ Annulé</option>
                    </select>
                  </div>

                  <div class="w-full lg:w-auto min-w-[250px]">
                    <label for="structure" class="block text-sm font-semibold text-gray-700 mb-2">Structure</label>
                    <select
                      v-model="filters.structure"
                      id="structure"
                      class="w-full rounded-xl border-0 bg-white shadow-sm ring-1 ring-gray-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 text-sm px-4 py-3 transition-all duration-200 hover:shadow-md"
                    >
                      <option value="all">🏢 Toutes les structures</option>
                      <option v-for="structure in structures" :key="structure.id" :value="structure.id">
                        {{ structure.sigle }} - {{ structure.libelle }}
                      </option>
                    </select>
                  </div>

                  <div class="flex-1 min-w-[250px]">
                    <label for="search" class="block text-sm font-semibold text-gray-700 mb-2">Rechercher un stagiaire</label>
                    <div class="relative">
                      <input
                        v-model="filters.search"
                        type="text"
                        id="search"
                        placeholder="Nom, prénom ou email..."
                        class="w-full rounded-xl border-0 bg-white shadow-sm ring-1 ring-gray-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 text-sm px-4 py-3 pl-10 transition-all duration-200 hover:shadow-md"
                      />
                      <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>

                  <div class="flex items-end gap-3">
                    <button
                      type="submit"
                      class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2 font-medium"
                    >
                      <FunnelIcon class="h-4 w-4" />
                      Filtrer
                    </button>
                    <button
                      type="button"
                      @click="resetFilters"
                      class="bg-gray-100 text-gray-700 px-6 py-3 rounded-xl hover:bg-gray-200 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 flex items-center gap-2 font-medium"
                    >
                      Réinitialiser
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- Tableau des stages -->
            <div class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100/50 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200/50">
                  <thead class="bg-gradient-to-r from-gray-50 to-gray-100/80">
                    <tr>
                      <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Stagiaire</th>
                      <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Structure</th>
                      <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Période</th>
                      <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Statut</th>
                      <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Maître de Stage</th>
                      <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white/30 divide-y divide-gray-200/30">
                    <tr v-if="stages.data.length === 0">
                      <td colspan="6" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center justify-center space-y-4">
                          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                            <FolderOpenIcon class="w-8 h-8 text-gray-400" />
                          </div>
                          <div class="space-y-2">
                            <p class="text-gray-600 font-medium">Aucun stage trouvé</p>
                            <p class="text-gray-400 text-sm">Essayez de modifier vos critères de recherche</p>
                          </div>
                          <button
                            @click="resetFilters"
                            class="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium"
                          >
                            Réinitialiser les filtres
                          </button>
                        </div>
                      </td>
                    </tr>
                    <tr v-for="stage in stages.data" :key="stage.id" class="hover:bg-gray-50/50 transition-all duration-200 group">
                      <td class="px-6 py-5 whitespace-nowrap">
                        <div v-if="stage.demande_stage?.stagiaire?.user" class="flex items-center gap-4">
                          <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center flex-shrink-0 shadow-md group-hover:shadow-lg transition-all duration-200">
                            <UserIcon class="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <div class="text-sm font-semibold text-gray-900 leading-tight">
                              {{ stage.demande_stage.stagiaire.user.nom }} {{ stage.demande_stage.stagiaire.user.prenom }}
                            </div>
                            <div class="text-xs text-gray-500 mt-1 flex items-center gap-1">
                              <span>{{ stage.demande_stage.stagiaire.user.email }}</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-5 whitespace-nowrap">
                        <div v-if="stage.structure">
                          <div class="text-sm font-medium text-gray-900">{{ stage.structure.sigle }}</div>
                          <div class="text-xs text-gray-500 mt-1">{{ stage.structure.libelle }}</div>
                        </div>
                      </td>
                      <td class="px-6 py-5 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                          <div class="flex items-center gap-1 mb-1">
                            <CalendarIcon class="h-4 w-4 text-gray-400" />
                            <span class="font-medium">{{ formatDate(stage.date_debut) }}</span>
                          </div>
                          <div class="text-xs text-gray-500">
                            au {{ formatDate(stage.date_fin) }}
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-5 whitespace-nowrap">
                        <span :class="getStatusClass(stage.statut)" class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium">
                          {{ stage.statut }}
                        </span>
                      </td>
                      <td class="px-6 py-5 whitespace-nowrap">
                        <div v-if="stage.affectation_recente?.maitre_stage" class="text-sm">
                          <div class="font-medium text-gray-900">
                            {{ stage.affectation_recente.maitre_stage.nom }} {{ stage.affectation_recente.maitre_stage.prenom }}
                          </div>
                          <div class="text-xs text-gray-500">{{ stage.affectation_recente.maitre_stage.email }}</div>
                        </div>
                        <div v-else class="text-sm text-gray-400 italic">Non affecté</div>
                      </td>
                      <td class="px-6 py-5 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center gap-2">
                          <Link
                            :href="route('agent.stages.show', stage.id)"
                            class="inline-flex items-center gap-1 px-3 py-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors text-xs font-medium shadow-sm hover:shadow-md transform hover:scale-105"
                          >
                            <EyeIcon class="h-4 w-4" />
                            Voir
                          </Link>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Pagination avec style moderne -->
            <div class="mt-8 flex justify-center">
              <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-2">
                <Pagination :links="stages.links" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AgentDPAFLayout>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { Link, router } from '@inertiajs/vue3';
import AgentDPAFLayout from '@/Layouts/AgentDPAF.vue';
import Pagination from '@/Components/Pagination.vue';
import { 
  DocumentTextIcon, 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  UserIcon, 
  CalendarIcon, 
  EyeIcon, 
  FolderOpenIcon 
} from '@heroicons/vue/24/outline';

const props = defineProps({
  stages: Object,
  structures: Array,
  filters: Object,
});

const filters = ref({
  status: props.filters.status || 'all',
  structure: props.filters.structure || 'all',
  search: props.filters.search || '',
  page: props.filters.page || 1,
});

// Surveiller les changements de filtres pour mettre à jour l'URL
watch(filters.value, (newFilters) => {
  if (newFilters.page === 1) {
    delete newFilters.page;
  }
});

// Appliquer les filtres
function applyFilters() {
  router.get(route('agent.stages'), filters.value, {
    preserveState: true,
    replace: true,
  });
}

// Réinitialiser les filtres
function resetFilters() {
  filters.value = {
    status: 'all',
    structure: 'all',
    search: '',
    page: 1,
  };
  applyFilters();
}

// Formater une date avec un style plus moderne
function formatDate(date) {
  return new Date(date).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

// Obtenir la classe CSS pour le statut
function getStatusClass(status) {
  const classes = {
    'En attente': 'bg-yellow-100 text-yellow-800',
    'En cours': 'bg-blue-100 text-blue-800',
    'Terminé': 'bg-green-100 text-green-800',
    'Annulé': 'bg-red-100 text-red-800',
  };
  return classes[status] || 'bg-gray-100 text-gray-800';
}

// Compter les stages par statut pour les statistiques
function getStagesByStatus(status) {
  return props.stages.data.filter(stage => stage.statut === status).length;
}
</script>
