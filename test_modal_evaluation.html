<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Évaluation</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; }
        .modal-content { background: white; padding: 20px; border-radius: 10px; max-width: 500px; width: 100%; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div id="app">
        <h1>Test Modal Évaluation</h1>
        
        <h2>Test avec stagiaire principal (professionnel)</h2>
        <button @click="testerModalProfessionnel" class="btn-primary">
            Tester Modal Professionnel
        </button>
        
        <h2>Test avec stagiaire académique</h2>
        <button @click="testerModalAcademique" class="btn-primary">
            Tester Modal Académique
        </button>
        
        <!-- Modal de test -->
        <div v-if="modalOuvert" class="modal">
            <div class="modal-content">
                <h3>Évaluer {{ membreTest?.user?.prenom }} {{ membreTest?.user?.nom }}</h3>
                <p><strong>Type détecté:</strong> {{ getTypeStageTest() }}</p>
                <p><strong>Université:</strong> {{ getUniversiteNomTest() }}</p>
                
                <div v-if="getTypeStageTest() === 'professionnel'">
                    <h4>Formulaire Professionnel</h4>
                    <p>10 critères standards du ministère</p>
                </div>
                
                <div v-else-if="getTypeStageTest() === 'academique'">
                    <h4>Formulaire Académique</h4>
                    <p>Critères personnalisés selon l'université</p>
                </div>
                
                <div v-else>
                    <h4 style="color: red;">Type non reconnu: {{ getTypeStageTest() }}</h4>
                </div>
                
                <button @click="fermerModal" class="btn-secondary">Fermer</button>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;
        
        createApp({
            setup() {
                const modalOuvert = ref(false);
                const membreTest = ref(null);
                const stageTest = ref(null);
                
                const getTypeStageTest = () => {
                    try {
                        if (!membreTest.value) return 'professionnel';
                        
                        if (membreTest.value.id === 'principal') {
                            return stageTest.value?.type || 'professionnel';
                        }
                        
                        return membreTest.value.type_stage || 'professionnel';
                    } catch (error) {
                        console.error('Erreur getTypeStageTest:', error);
                        return 'professionnel';
                    }
                };
                
                const getUniversiteNomTest = () => {
                    try {
                        if (!membreTest.value) return '';
                        
                        if (membreTest.value.id === 'principal') {
                            return stageTest.value?.stagiaire_info?.universite || '';
                        }
                        
                        return membreTest.value.universite || '';
                    } catch (error) {
                        console.error('Erreur getUniversiteNomTest:', error);
                        return '';
                    }
                };
                
                const testerModalProfessionnel = () => {
                    stageTest.value = {
                        type: 'professionnel',
                        stagiaire_info: {
                            universite: 'École Supérieure de Commerce'
                        }
                    };
                    
                    membreTest.value = {
                        id: 'principal',
                        user: {
                            prenom: 'Jean',
                            nom: 'Dupont'
                        }
                    };
                    
                    modalOuvert.value = true;
                    console.log('Test professionnel:', { membre: membreTest.value, stage: stageTest.value });
                };
                
                const testerModalAcademique = () => {
                    stageTest.value = {
                        type: 'academique',
                        stagiaire_info: {
                            universite: 'Université de Cotonou'
                        }
                    };
                    
                    membreTest.value = {
                        id: 'principal',
                        user: {
                            prenom: 'Marie',
                            nom: 'Martin'
                        }
                    };
                    
                    modalOuvert.value = true;
                    console.log('Test académique:', { membre: membreTest.value, stage: stageTest.value });
                };
                
                const fermerModal = () => {
                    modalOuvert.value = false;
                    membreTest.value = null;
                    stageTest.value = null;
                };
                
                return {
                    modalOuvert,
                    membreTest,
                    stageTest,
                    getTypeStageTest,
                    getUniversiteNomTest,
                    testerModalProfessionnel,
                    testerModalAcademique,
                    fermerModal
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
