<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('membre_groupes', function (Blueprint $table) {
            $table->string('lettre_cv_path')->nullable()->after('user_id');
            $table->string('diplomes_path')->nullable()->after('lettre_cv_path');
            $table->string('visage_path')->nullable()->after('diplomes_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('membre_groupes', function (Blueprint $table) {
            $table->dropColumn(['lettre_cv_path', 'diplomes_path', 'visage_path']);
        });
    }
};
