<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\Agent;
use App\Models\Universite;
use App\Models\DemandeStage;
use App\Models\Stagiaire;
use App\Models\User;

class DashboardController extends Controller
{
    /**
     * Affiche le tableau de bord du Responsable Université
     */
    public function index()
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        // Statistiques générales
        $stats = [
            'total_etudiants' => 0,
            'demandes_en_attente' => 0,
            'demandes_acceptees' => 0,
            'stages_en_cours' => 0,
            'evaluations_en_attente' => 0,
            'evaluations_validees' => 0,
        ];

        // Si une université est assignée, calculer les statistiques
        if ($universite) {
            // Compter les étudiants de cette université
            $stats['total_etudiants'] = User::whereHas('stagiaire', function ($query) use ($universite) {
                $query->where('universite', $universite->nom_complet);
            })->count();

            // Compter les demandes de stage des étudiants de cette université
            $demandesUniversite = DemandeStage::whereHas('stagiaire.user', function ($query) use ($universite) {
                $query->whereHas('stagiaire', function ($subQuery) use ($universite) {
                    $subQuery->where('universite', $universite->nom_complet);
                });
            });

            $stats['demandes_en_attente'] = (clone $demandesUniversite)->where('statut', 'En attente')->count();
            $stats['demandes_acceptees'] = (clone $demandesUniversite)->where('statut', 'Acceptée')->count();
            $stats['stages_en_cours'] = (clone $demandesUniversite)->where('statut', 'En cours')->count();

            // Statistiques des évaluations académiques
            $evaluationsAcademiques = \App\Models\Evaluation::whereHas('stage', function ($query) use ($universite) {
                $query->where('type', 'Académique')
                      ->whereHas('stagiaire', function ($subQuery) use ($universite) {
                          $subQuery->where('universite_id', $universite->id);
                      });
            });

            $stats['evaluations_en_attente'] = (clone $evaluationsAcademiques)->where('validee_par_ru', false)->count();
            $stats['evaluations_validees'] = (clone $evaluationsAcademiques)->where('validee_par_ru', true)->count();
        }

        // Récupérer les dernières demandes de stage des étudiants de l'université
        $dernieresDemandes = collect();
        if ($universite) {
            $dernieresDemandes = DemandeStage::with(['stagiaire.user', 'structure'])
                ->whereHas('stagiaire.user', function ($query) use ($universite) {
                    $query->whereHas('stagiaire', function ($subQuery) use ($universite) {
                        $subQuery->where('universite', $universite->nom_complet);
                    });
                })
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
        }

        // Activités récentes
        $activitesRecentes = collect();
        if ($universite) {
            // Nouvelles inscriptions d'étudiants
            $nouvellesInscriptions = User::with('stagiaire')
                ->whereHas('stagiaire', function ($query) use ($universite) {
                    $query->where('universite', $universite->nom_complet);
                })
                ->where('created_at', '>=', now()->subDays(7))
                ->get()
                ->map(function ($user) {
                    return [
                        'type' => 'inscription',
                        'description' => "Nouvel étudiant inscrit : {$user->prenom} {$user->nom}",
                        'date' => $user->created_at,
                        'icon' => 'user-plus',
                        'color' => 'emerald'
                    ];
                });

            // Nouvelles demandes de stage
            $nouvellesDemandes = DemandeStage::with(['stagiaire.user'])
                ->whereHas('stagiaire.user', function ($query) use ($universite) {
                    $query->whereHas('stagiaire', function ($subQuery) use ($universite) {
                        $subQuery->where('universite', $universite->nom_complet);
                    });
                })
                ->where('created_at', '>=', now()->subDays(7))
                ->get()
                ->map(function ($demande) {
                    return [
                        'type' => 'demande',
                        'description' => "Nouvelle demande de stage : {$demande->stagiaire->user->prenom} {$demande->stagiaire->user->nom}",
                        'date' => $demande->created_at,
                        'icon' => 'document-plus',
                        'color' => 'blue'
                    ];
                });

            $activitesRecentes = $nouvellesInscriptions->concat($nouvellesDemandes)
                ->sortByDesc('date')
                ->take(10)
                ->values();
        }

        // Récupérer les évaluations en attente de validation pour cette université
        $evaluationsEnAttente = [];
        $stagesRecents = [];

        if ($universite) {
            // Évaluations académiques en attente de validation RU
            $evaluationsEnAttente = \App\Models\Evaluation::with([
                'stage.stagiaire.user',
                'stage.structure',
                'agent.user'
            ])
            ->whereHas('stage', function ($query) use ($universite) {
                $query->where('type', 'Académique')
                      ->whereHas('stagiaire', function ($subQuery) use ($universite) {
                          $subQuery->where('universite_id', $universite->id);
                      });
            })
            ->where('validee_par_ru', false)
            ->orderBy('date_evaluation', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($evaluation) {
                return [
                    'id' => $evaluation->id,
                    'stage_id' => $evaluation->stage_id,
                    'stagiaire' => [
                        'nom' => $evaluation->stage->stagiaire->user->nom,
                        'prenom' => $evaluation->stage->stagiaire->user->prenom,
                    ],
                    'structure' => $evaluation->stage->structure->libelle ?? 'Non définie',
                    'note_totale' => $evaluation->note_totale,
                    'date_evaluation' => $evaluation->date_evaluation->format('d/m/Y H:i'),
                ];
            });

            // Stages récents de l'université
            $stagesRecents = \App\Models\Stage::with([
                'stagiaire.user',
                'structure'
            ])
            ->whereHas('stagiaire', function ($query) use ($universite) {
                $query->where('universite_id', $universite->id);
            })
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($stage) {
                return [
                    'id' => $stage->id,
                    'stagiaire' => [
                        'nom' => $stage->stagiaire->user->nom,
                        'prenom' => $stage->stagiaire->user->prenom,
                    ],
                    'structure' => $stage->structure->libelle ?? 'Non définie',
                    'type' => $stage->type,
                    'statut' => $stage->statut,
                    'date_debut' => $stage->date_debut ? \Carbon\Carbon::parse($stage->date_debut)->format('d/m/Y') : null,
                    'date_fin' => $stage->date_fin ? \Carbon\Carbon::parse($stage->date_fin)->format('d/m/Y') : null,
                ];
            });
        }

        return Inertia::render('Agent/RU/Dashboard', [
            'pageTitle' => 'Tableau de bord RU',
            'universite' => $universite,
            'stats' => $stats,
            'dernieresDemandes' => $dernieresDemandes,
            'activitesRecentes' => $activitesRecentes,
            'agent' => $agent->load('user'),
            'evaluationsEnAttente' => $evaluationsEnAttente,
            'stagesRecents' => $stagesRecents,
            'notifications' => $user->notifications()->latest()->take(20)->get()
        ]);
    }
}
