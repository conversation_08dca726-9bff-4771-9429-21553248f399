<?php

use Illuminate\Support\Facades\Route;
use App\Models\DemandeStage;
use App\Models\Stagiaire;
use App\Models\User;

// Page d'accueil pour tester les emails
Route::get('/test-emails', function () {
    return view('test-emails');
});

// Routes de test pour prévisualiser les emails (à supprimer en production)
Route::get('/test-email-acceptation', function () {
    // Créer des données de test
    $user = new User([
        'nom' => 'DUPONT',
        'prenom' => 'Jean',
        'email' => '<EMAIL>'
    ]);
    
    $stagiaire = new Stagiaire();
    $stagiaire->user = $user;
    
    $demande = new DemandeStage([
        'code_suivi' => 'STG-2024-001',
        'type' => 'Académique',
        'nature' => 'Individuel',
        'date_debut' => '2024-07-15',
        'date_fin' => '2024-09-15',
        'created_at' => now()
    ]);
    
    // Simuler une structure
    $demande->structure = (object) ['libelle' => 'Direction des Ressources Humaines'];
    
    return view('emails.demande_acceptee', [
        'stagiaire' => $stagiaire,
        'demande' => $demande
    ]);
});

Route::get('/test-email-rejet', function () {
    // Créer des données de test
    $user = new User([
        'nom' => 'MARTIN',
        'prenom' => 'Marie',
        'email' => '<EMAIL>'
    ]);
    
    $stagiaire = new Stagiaire();
    $stagiaire->user = $user;
    
    $demande = new DemandeStage([
        'code_suivi' => 'STG-2024-002',
        'type' => 'Professionnel',
        'nature' => 'Groupe',
        'date_debut' => '2024-08-01',
        'date_fin' => '2024-10-01',
        'created_at' => now()
    ]);
    
    $motifRejet = "Après examen de votre dossier, nous constatons que votre profil ne correspond pas aux critères requis pour ce type de stage. Nous vous encourageons à revoir votre candidature et à postuler à nouveau lors de la prochaine session.";
    
    return view('emails.demande_rejetee', [
        'stagiaire' => $stagiaire,
        'demande' => $demande,
        'motifRejet' => $motifRejet
    ]);
});

// Route de test pour vérifier la logique des boutons RS
Route::get('/test-workflow-rs', function () {
    $scenarios = [
        [
            'titre' => 'Demande En cours - Boutons visibles',
            'statut' => 'En cours',
            'maitre_stage_affecte' => false,
            'boutons_visibles' => true
        ],
        [
            'titre' => 'Demande Acceptée - Boutons cachés',
            'statut' => 'Acceptée',
            'maitre_stage_affecte' => false,
            'boutons_visibles' => false
        ],
        [
            'titre' => 'Demande En cours + Maître affecté - Boutons cachés',
            'statut' => 'En cours',
            'maitre_stage_affecte' => true,
            'boutons_visibles' => false
        ],
        [
            'titre' => 'Demande Acceptée + Maître affecté - Boutons cachés',
            'statut' => 'Acceptée',
            'maitre_stage_affecte' => true,
            'boutons_visibles' => false
        ]
    ];

    return view('test-workflow-rs', compact('scenarios'));
});
