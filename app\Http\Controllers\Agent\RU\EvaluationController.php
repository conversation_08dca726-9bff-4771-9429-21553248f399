<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use App\Models\Evaluation;
use App\Models\Stage;
use App\Models\Agent;
use App\Models\Universite;
use App\Notifications\StagiaireNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;
use App\Mail\StagiaireProfessionnelEvalue;

class EvaluationController extends Controller
{
    /**
     * Afficher la liste des évaluations en attente de validation
     */
    public function index()
    {
        try {
            $user = Auth::user();
            $agent = $user->agent;

            // Vérifier que l'utilisateur est bien un RU
            if ($agent->role_agent !== 'RU') {
                return redirect()->back()->with('error', 'Accès non autorisé.');
            }

            // Obtenir l'université gérée par ce RU
            $universite = Universite::where('responsable_id', $agent->id)->first();
            
            if (!$universite) {
                // Créer un objet de pagination vide pour éviter les erreurs
                $evaluationsVides = new \Illuminate\Pagination\LengthAwarePaginator(
                    [],
                    0,
                    20,
                    1,
                    [
                        'path' => request()->url(),
                        'pageName' => 'page'
                    ]
                );

                // Ajouter les propriétés nécessaires pour éviter les erreurs href null
                $evaluationsVides->withPath(request()->url());

                return Inertia::render('Agent/RU/Evaluations/Index', [
                    'evaluations' => $evaluationsVides,
                    'universite' => null,
                    'message' => 'Aucune université assignée à ce responsable.'
                ]);
            }

            // Récupérer les évaluations des stages académiques de cette université
            $evaluations = Evaluation::whereHas('stage', function($query) use ($universite) {
                $query->where('type', 'Académique')
                      ->whereHas('stagiaire', function($subQuery) use ($universite) {
                          $subQuery->where('universite_id', $universite->id);
                      });
            })
            ->with([
                'stage.stagiaire.user',
                'stage.structure',
                'agent.user',
                'formatEvaluationUniversite'
            ])
            ->orderBy('date_evaluation', 'desc')
            ->paginate(20);

            return Inertia::render('Agent/RU/Evaluations/Index', [
                'evaluations' => $evaluations,
                'universite' => $universite
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'affichage des évaluations RU', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return redirect()->back()->with('error', 'Une erreur est survenue.');
        }
    }

    /**
     * Afficher les détails d'une évaluation
     */
    public function show(Evaluation $evaluation)
    {
        try {
            $user = Auth::user();
            $agent = $user->agent;

            // Vérifier que l'utilisateur est bien un RU
            if ($agent->role_agent !== 'RU') {
                return redirect()->back()->with('error', 'Accès non autorisé.');
            }

            // Vérifier que l'évaluation concerne l'université de ce RU
            $universite = Universite::where('responsable_id', $agent->id)->first();
            $stage = $evaluation->stage;
            
            if (!$universite || !$stage || !$stage->stagiaire || $stage->stagiaire->universite_id !== $universite->id) {
                return redirect()->back()->with('error', 'Évaluation non accessible.');
            }

            // Charger les relations nécessaires
            $evaluation->load([
                'stage.stagiaire.user',
                'stage.structure',
                'stage.demandeStage',
                'agent.user',
                'formatEvaluationUniversite',
                'ruValidateur.user'
            ]);

            return Inertia::render('Agent/RU/Evaluations/Show', [
                'evaluation' => $evaluation,
                'universite' => $universite
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de l\'affichage de l\'évaluation RU', [
                'error' => $e->getMessage(),
                'evaluation_id' => $evaluation->id
            ]);

            return redirect()->back()->with('error', 'Une erreur est survenue.');
        }
    }

    /**
     * Valider une évaluation (la rendre visible au stagiaire)
     */
    public function valider(Request $request, Evaluation $evaluation)
    {
        try {
            $user = Auth::user();
            $agent = $user->agent;

            // Vérifier que l'utilisateur est bien un RU
            if ($agent->role_agent !== 'RU') {
                return response()->json(['error' => 'Accès non autorisé'], 403);
            }

            // Vérifier que l'évaluation concerne l'université de ce RU
            $universite = Universite::where('responsable_id', $agent->id)->first();
            $stage = $evaluation->stage;
            
            if (!$universite || !$stage || !$stage->stagiaire || $stage->stagiaire->universite_id !== $universite->id) {
                return response()->json(['error' => 'Évaluation non accessible'], 403);
            }

            // Vérifier que l'évaluation n'est pas déjà validée
            if ($evaluation->validee_par_ru) {
                return response()->json(['error' => 'Cette évaluation est déjà validée'], 400);
            }

            // Validation optionnelle du commentaire RU
            $validated = $request->validate([
                'commentaire_ru' => 'nullable|string|max:1000'
            ]);

            // Valider l'évaluation
            $evaluation->update([
                'validee_par_ru' => true,
                'date_validation_ru' => now(),
                'ru_validateur_id' => $agent->id,
                'commentaire_ru' => $validated['commentaire_ru'] ?? null
            ]);

            // Envoyer un email au stagiaire avec ses notes complètes
            $stagiaire = $stage->stagiaire;
            if ($stagiaire && $stagiaire->user) {
                // Envoyer l'email avec les notes
                $this->envoyerEmailNotesValidees($stagiaire, $stage, $evaluation);

                // Notification interne
                $stagiaire->user->notify(new StagiaireNotification(
                    'Votre évaluation de stage a été validée et est maintenant visible.',
                    route('stagiaire.stages.show', $stage->id)
                ));
            }

            Log::info('Évaluation validée par RU', [
                'evaluation_id' => $evaluation->id,
                'ru_id' => $agent->id,
                'stage_id' => $stage->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Évaluation validée avec succès. Le stagiaire peut maintenant voir sa note.'
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la validation de l\'évaluation par RU', [
                'error' => $e->getMessage(),
                'evaluation_id' => $evaluation->id
            ]);

            return response()->json(['error' => 'Une erreur est survenue'], 500);
        }
    }

    /**
     * Générer le PDF de la fiche d'évaluation
     */
    public function genererPDF(Evaluation $evaluation)
    {
        try {
            $user = Auth::user();
            $agent = $user->agent;

            // Vérifier que l'utilisateur est bien un RU
            if ($agent->role_agent !== 'RU') {
                return redirect()->back()->with('error', 'Accès non autorisé.');
            }

            // Vérifier que l'évaluation concerne l'université de ce RU
            $universite = Universite::where('responsable_id', $agent->id)->first();
            $stage = $evaluation->stage;
            
            if (!$universite || !$stage || !$stage->stagiaire || $stage->stagiaire->universite_id !== $universite->id) {
                return redirect()->back()->with('error', 'Évaluation non accessible.');
            }

            // Charger les relations nécessaires
            $evaluation->load([
                'stage.stagiaire.user',
                'stage.structure',
                'stage.demandeStage',
                'agent.user',
                'formatEvaluationUniversite'
            ]);

            // Générer le PDF (à implémenter avec une librairie PDF)
            $pdf = \PDF::loadView('pdf.fiche-evaluation', [
                'evaluation' => $evaluation,
                'universite' => $universite
            ]);

            $filename = "evaluation_stage_{$stage->id}_{$stage->stagiaire->user->nom}_{$stage->stagiaire->user->prenom}.pdf";

            return $pdf->download($filename);

        } catch (\Exception $e) {
            Log::error('Erreur lors de la génération du PDF d\'évaluation', [
                'error' => $e->getMessage(),
                'evaluation_id' => $evaluation->id
            ]);

            return redirect()->back()->with('error', 'Erreur lors de la génération du PDF.');
        }
    }

    /**
     * Envoyer un email au stagiaire après validation des notes par le RU
     */
    private function envoyerEmailNotesValidees($stagiaire, $stage, $evaluation)
    {
        try {
            // Envoyer un email avec les notes complètes (comme pour un stage professionnel)
            Mail::to($stagiaire->user->email)
                ->send(new StagiaireProfessionnelEvalue($stagiaire, $stage, $evaluation));

            Log::info('Email notes validées envoyé au stagiaire académique', [
                'stagiaire_email' => $stagiaire->user->email,
                'evaluation_id' => $evaluation->id,
                'stage_id' => $stage->id
            ]);

        } catch (\Exception $e) {
            Log::error('Erreur envoi email notes validées', [
                'error' => $e->getMessage(),
                'stagiaire_email' => $stagiaire->user->email,
                'evaluation_id' => $evaluation->id
            ]);
        }
    }
}
