<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\User;
use App\Models\Universite;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ResponsableRUController extends Controller
{
    /**
     * Affiche la liste des responsables RU
     */
    public function index()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Récupérer tous les agents RU avec leurs relations
        $responsablesRU = Agent::with([
            'user',
            'creator',
            'universiteResponsable' // Université dont il est responsable
        ])
            ->where('role_agent', 'RU')
            ->get();

        // Statistiques
        $stats = [
            'total' => $responsablesRU->count(),
            'avec_universite' => $responsablesRU->whereNotNull('universite_responsable_id')->count(),
            'sans_universite' => $responsablesRU->whereNull('universite_responsable_id')->count(),
        ];

        return Inertia::render('Admin/ResponsablesRU/Index', [
            'responsablesRU' => $responsablesRU,
            'stats' => $stats,
        ]);
    }

    /**
     * Affiche le formulaire de création d'un responsable RU
     */
    public function create()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Récupérer les universités actives sans responsable
        $universites = Universite::where('active', true)
            ->whereNull('responsable_id')
            ->orderBy('nom_complet')
            ->get();

        return Inertia::render('Admin/ResponsablesRU/Create', [
            'universites' => $universites,
        ]);
    }

    /**
     * Stocke un nouveau responsable RU
     */
    public function store(Request $request)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'telephone' => 'required|string|max:20',
            'date_de_naissance' => 'required|date',
            'sexe' => 'required|in:Homme,Femme',
            'matricule' => 'required|string|unique:agents',
            'fonction' => 'required|string',
            'date_embauche' => 'nullable|date',
            'universite_id' => 'required|exists:universites,id',
        ]);

        // Vérifier que l'université n'a pas déjà un responsable
        $universite = Universite::find($validated['universite_id']);
        if ($universite->responsable_id) {
            return back()->withErrors(['universite_id' => 'Cette université a déjà un responsable assigné.']);
        }

        // Créer l'utilisateur
        $user = User::create([
            'nom' => $validated['nom'],
            'prenom' => $validated['prenom'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'telephone' => $validated['telephone'],
            'date_de_naissance' => $validated['date_de_naissance'],
            'sexe' => $validated['sexe'],
            'role' => 'agent',
            'created_by' => Auth::id(),
        ]);

        // Créer l'agent RU
        $agent = Agent::create([
            'user_id' => $user->id,
            'matricule' => $validated['matricule'],
            'fonction' => $validated['fonction'],
            'role_agent' => 'RU',
            'date_embauche' => $validated['date_embauche'] ?? now(),
            'universite_responsable_id' => $validated['universite_id'],
            'created_by' => Auth::id(),
        ]);

        // Assigner le responsable à l'université
        $universite->update(['responsable_id' => $agent->id]);

        return redirect()->route('admin.responsables-ru.index')
            ->with('success', 'Responsable RU créé et assigné à l\'université avec succès.');
    }

    /**
     * Affiche les détails d'un responsable RU
     */
    public function show(Agent $responsableRu)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Vérifier que c'est bien un agent RU
        if ($responsableRu->role_agent !== 'RU') {
            return redirect()->route('admin.responsables-ru.index')
                ->with('error', 'Cet agent n\'est pas un Responsable Université.');
        }

        // Charger les relations nécessaires
        $responsableRu->load([
            'user',
            'creator',
            'universiteResponsable.stagiaires.user' // Université et ses stagiaires
        ]);

        return Inertia::render('Admin/ResponsablesRU/Show', [
            'responsableRu' => $responsableRu,
        ]);
    }

    /**
     * Affiche le formulaire d'édition d'un responsable RU
     */
    public function edit(Agent $responsableRu)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Vérifier que c'est bien un agent RU
        if ($responsableRu->role_agent !== 'RU') {
            return redirect()->route('admin.responsables-ru.index')
                ->with('error', 'Cet agent n\'est pas un Responsable Université.');
        }

        // Charger les relations
        $responsableRu->load(['user', 'universiteResponsable']);

        // Récupérer les universités disponibles (sans responsable + celle actuellement assignée)
        $universites = Universite::where('active', true)
            ->where(function ($query) use ($responsableRu) {
                $query->whereNull('responsable_id')
                    ->orWhere('id', $responsableRu->universite_responsable_id);
            })
            ->orderBy('nom_complet')
            ->get();

        return Inertia::render('Admin/ResponsablesRU/Edit', [
            'responsableRu' => $responsableRu,
            'universites' => $universites,
        ]);
    }

    /**
     * Met à jour un responsable RU
     */
    public function update(Request $request, Agent $responsableRu)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Vérifier que c'est bien un agent RU
        if ($responsableRu->role_agent !== 'RU') {
            return redirect()->route('admin.responsables-ru.index')
                ->with('error', 'Cet agent n\'est pas un Responsable Université.');
        }

        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $responsableRu->user_id,
            'telephone' => 'required|string|max:20',
            'date_de_naissance' => 'required|date',
            'sexe' => 'required|in:Homme,Femme',
            'matricule' => 'required|string|unique:agents,matricule,' . $responsableRu->id,
            'fonction' => 'required|string',
            'date_embauche' => 'nullable|date',
            'universite_id' => 'required|exists:universites,id',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Vérifier que l'université n'a pas déjà un autre responsable
        $universite = Universite::find($validated['universite_id']);
        if ($universite->responsable_id && $universite->responsable_id !== $responsableRu->id) {
            return back()->withErrors(['universite_id' => 'Cette université a déjà un autre responsable assigné.']);
        }

        // Mettre à jour l'utilisateur
        $userData = [
            'nom' => $validated['nom'],
            'prenom' => $validated['prenom'],
            'email' => $validated['email'],
            'telephone' => $validated['telephone'],
            'date_de_naissance' => $validated['date_de_naissance'],
            'sexe' => $validated['sexe'],
        ];

        if (!empty($validated['password'])) {
            $userData['password'] = Hash::make($validated['password']);
        }

        $responsableRu->user->update($userData);

        // Gérer le changement d'université
        $ancienneUniversiteId = $responsableRu->universite_responsable_id;
        $nouvelleUniversiteId = $validated['universite_id'];

        // Mettre à jour l'agent
        $responsableRu->update([
            'matricule' => $validated['matricule'],
            'fonction' => $validated['fonction'],
            'date_embauche' => $validated['date_embauche'],
            'universite_responsable_id' => $nouvelleUniversiteId,
        ]);

        // Gérer les relations université-responsable
        if ($ancienneUniversiteId !== $nouvelleUniversiteId) {
            // Retirer le responsable de l'ancienne université
            if ($ancienneUniversiteId) {
                Universite::where('id', $ancienneUniversiteId)
                    ->update(['responsable_id' => null]);
            }

            // Assigner à la nouvelle université
            Universite::where('id', $nouvelleUniversiteId)
                ->update(['responsable_id' => $responsableRu->id]);
        }

        return redirect()->route('admin.responsables-ru.index')
            ->with('success', 'Responsable RU mis à jour avec succès.');
    }

    /**
     * Supprime un responsable RU
     */
    public function destroy(Agent $responsableRu)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Vérifier que c'est bien un agent RU
        if ($responsableRu->role_agent !== 'RU') {
            return redirect()->route('admin.responsables-ru.index')
                ->with('error', 'Cet agent n\'est pas un Responsable Université.');
        }

        // Retirer le responsable de l'université avant suppression
        if ($responsableRu->universite_responsable_id) {
            Universite::where('id', $responsableRu->universite_responsable_id)
                ->update(['responsable_id' => null]);
        }

        // Supprimer l'utilisateur (cascade vers agent)
        $responsableRu->user->delete();

        return redirect()->route('admin.responsables-ru.index')
            ->with('success', 'Responsable RU supprimé avec succès.');
    }

    /**
     * Génère un matricule unique pour RU
     */
    private function generateUniqueMatricule()
    {
        do {
            $matricule = 'RU' . strtoupper(Str::random(6));
        } while (Agent::where('matricule', $matricule)->exists());

        return $matricule;
    }
}
