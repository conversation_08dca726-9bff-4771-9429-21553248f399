<template>
  <Head title="<PERSON><PERSON><PERSON> un Agent RS" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center gap-4 mb-8">
          <div class="p-3 bg-green-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-slate-800">C<PERSON><PERSON> un Agent RS</h1>
            <p class="text-slate-600 mt-1">Responsable de Structure</p>
          </div>
        </div>

        <!-- Formulaire -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
            <h2 class="text-xl font-bold text-slate-800">Informations de l'agent RS</h2>
          </div>

          <form @submit.prevent="submit" class="p-6">
            <div class="space-y-8">
              <!-- Informations personnelles -->
              <div>
                <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                  Informations personnelles
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Prénom *</label>
                    <input 
                      v-model="form.prenom" 
                      type="text" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.prenom" class="text-red-500 text-sm mt-1">{{ form.errors.prenom }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Nom *</label>
                    <input 
                      v-model="form.nom" 
                      type="text" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.nom" class="text-red-500 text-sm mt-1">{{ form.errors.nom }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Email *</label>
                    <input 
                      v-model="form.email" 
                      type="email" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Téléphone *</label>
                    <input 
                      v-model="form.telephone" 
                      type="tel" 
                      placeholder="+229 XX XX XX XX"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.telephone" class="text-red-500 text-sm mt-1">{{ form.errors.telephone }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Date de naissance *</label>
                    <input 
                      v-model="form.date_de_naissance" 
                      type="date" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.date_de_naissance" class="text-red-500 text-sm mt-1">{{ form.errors.date_de_naissance }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Sexe *</label>
                    <select 
                      v-model="form.sexe" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required
                    >
                      <option value="">Sélectionner</option>
                      <option value="Homme">Homme</option>
                      <option value="Femme">Femme</option>
                    </select>
                    <div v-if="form.errors.sexe" class="text-red-500 text-sm mt-1">{{ form.errors.sexe }}</div>
                  </div>
                </div>
              </div>

              <!-- Informations professionnelles -->
              <div>
                <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                  Informations professionnelles
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Matricule *</label>
                    <input 
                      v-model="form.matricule" 
                      type="text" 
                      placeholder="RS123456"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.matricule" class="text-red-500 text-sm mt-1">{{ form.errors.matricule }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Fonction *</label>
                    <input 
                      v-model="form.fonction" 
                      type="text" 
                      placeholder="Responsable de Structure"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.fonction" class="text-red-500 text-sm mt-1">{{ form.errors.fonction }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Date d'embauche</label>
                    <input 
                      v-model="form.date_embauche" 
                      type="date" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                    />
                    <div v-if="form.errors.date_embauche" class="text-red-500 text-sm mt-1">{{ form.errors.date_embauche }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Structure de Direction Générale à superviser</label>
                    <select 
                      v-model="form.structure_responsable_id" 
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                    >
                      <option value="">Aucune structure de direction générale assignée</option>
                      <option v-for="structure in structures" :key="structure.id" :value="structure.id">
                        {{ structure.sigle }} - {{ structure.libelle }}
                      </option>
                    </select>
                    <div v-if="form.errors.structure_responsable_id" class="text-red-500 text-sm mt-1">{{ form.errors.structure_responsable_id }}</div>
                  </div>
                </div>
              </div>

              <!-- Informations de connexion -->
              <div>
                <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                  Informations de connexion
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Mot de passe *</label>
                    <input 
                      v-model="form.password" 
                      type="password" 
                      placeholder="Minimum 8 caractères"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                    <div v-if="form.errors.password" class="text-red-500 text-sm mt-1">{{ form.errors.password }}</div>
                  </div>

                  <div>
                    <label class="block text-sm font-semibold text-slate-700 mb-2">Confirmer le mot de passe *</label>
                    <input 
                      v-model="form.password_confirmation" 
                      type="password" 
                      placeholder="Confirmer le mot de passe"
                      class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" 
                      required 
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- Boutons -->
            <div class="flex justify-between pt-8 border-t border-slate-200 mt-8">
              <Link 
                :href="route('admin.users.index')" 
                class="px-6 py-3 bg-slate-100 text-slate-700 rounded-xl hover:bg-slate-200 transition-colors font-semibold flex items-center gap-2"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Retour
              </Link>
              
              <button 
                type="submit" 
                class="px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors font-semibold flex items-center gap-2 disabled:opacity-50"
                :disabled="form.processing"
              >
                <span v-if="form.processing">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Création...
                </span>
                <span v-else>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                  </svg>
                  Créer l'Agent RS
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';

const props = defineProps({
  structures: Array,
});

const form = useForm({
  nom: '',
  prenom: '',
  email: '',
  password: '',
  password_confirmation: '',
  telephone: '',
  date_de_naissance: '',
  sexe: '',
  matricule: '',
  fonction: 'Responsable de Structure',
  date_embauche: '',
  structure_responsable_id: null,
});

function submit() {
  form.post(route('admin.agent-rs.store'));
}
</script>
