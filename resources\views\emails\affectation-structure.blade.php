<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nouvelle demande de stage affectée</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #0d47a1;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 15px;
        }
        .ministry-title {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin: 5px 0;
            text-transform: uppercase;
        }
        .content {
            margin: 25px 0;
        }
        .greeting {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .message {
            font-size: 14px;
            line-height: 1.8;
            margin-bottom: 20px;
            text-align: justify;
        }
        .info-box {
            background-color: #e3f2fd;
            border-left: 4px solid #0d47a1;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .info-title {
            font-weight: bold;
            color: #0d47a1;
            margin-bottom: 10px;
        }
        .demande-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px dotted #dee2e6;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            color: #6c757d;
        }
        .action-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .action-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        .signature {
            margin-top: 25px;
            font-style: italic;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img src="{{ asset('images/logo-ministere.png') }}" alt="Logo Ministère" class="logo">
            <div class="ministry-title">République du Bénin</div>
            <div class="ministry-title">Ministère de l'Économie et des Finances</div>
            <div class="ministry-title">Secrétariat Général</div>
        </div>

        <div class="content">
            <div class="greeting">
                Bonjour {{ $responsable->prenom }} {{ $responsable->nom }},
            </div>

            <div class="info-box">
                <div class="info-title">📋 Nouvelle affectation de demande de stage</div>
                <div>
                    Une demande de stage a été affectée à votre structure par le service DPAF. 
                    Vous êtes désormais responsable du traitement de cette demande.
                </div>
            </div>

            <div class="demande-details">
                <h3 style="color: #374151; margin-bottom: 15px;">📄 Détails de la demande</h3>
                <div class="detail-row">
                    <span class="detail-label">Code de suivi :</span>
                    <span class="detail-value">{{ $demande->code_suivi }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Stagiaire :</span>
                    <span class="detail-value">{{ $demande->stagiaire->user->prenom }} {{ $demande->stagiaire->user->nom }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Email :</span>
                    <span class="detail-value">{{ $demande->stagiaire->user->email }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Type de stage :</span>
                    <span class="detail-value">{{ $demande->type }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Nature :</span>
                    <span class="detail-value">{{ $demande->nature }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Période :</span>
                    <span class="detail-value">{{ \Carbon\Carbon::parse($demande->date_debut)->locale('fr')->isoFormat('DD MMMM YYYY') }} - {{ \Carbon\Carbon::parse($demande->date_fin)->locale('fr')->isoFormat('DD MMMM YYYY') }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Filière :</span>
                    <span class="detail-value">{{ $demande->stagiaire->filiere }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Niveau d'étude :</span>
                    <span class="detail-value">{{ $demande->stagiaire->niveau_etude }}</span>
                </div>
                @if($demande->type === 'Académique' && $demande->stagiaire->universite)
                <div class="detail-row">
                    <span class="detail-label">Université :</span>
                    <span class="detail-value">{{ $demande->stagiaire->universite }}</span>
                </div>
                @endif
                <div class="detail-row">
                    <span class="detail-label">Date de soumission :</span>
                    <span class="detail-value">{{ \Carbon\Carbon::parse($demande->date_soumission)->locale('fr')->isoFormat('DD MMMM YYYY [à] HH:mm') }}</span>
                </div>
            </div>

            <div class="action-box">
                <div class="action-title">⚡ Actions requises</div>
                <div>
                    <strong>Prochaines étapes :</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>Examiner la demande et les documents fournis</li>
                        <li>Décider de l'acceptation ou du refus de la demande</li>
                        <li>Si acceptée, affecter un maître de stage approprié</li>
                        <li>Notifier votre décision via la plateforme</li>
                    </ul>
                </div>
            </div>

            <div class="message">
                Vous pouvez consulter tous les détails de cette demande et prendre les actions nécessaires 
                en vous connectant à votre espace Responsable Structure sur la plateforme de gestion des stages.
            </div>

            <div class="message">
                <strong>Important :</strong> Cette demande nécessite votre attention dans les meilleurs délais 
                pour respecter les délais de traitement et permettre au stagiaire de planifier son stage.
            </div>
        </div>

        <div class="footer">
            <div class="signature">
                <strong>Système de Gestion des Stages</strong><br>
                Ministère de l'Économie et des Finances du Bénin
            </div>
            <div style="margin-top: 15px;">
                <small>
                    Cet email a été envoyé automatiquement. Merci de ne pas répondre à cette adresse.<br>
                    Pour toute question, contactez le service DPAF.
                </small>
            </div>
        </div>
    </div>
</body>
</html>
