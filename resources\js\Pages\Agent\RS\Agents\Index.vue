<template>
  <RSLayout>
    <template #header>
      <div class="flex items-center gap-3 mb-2">
        <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-12 h-12 flex items-center justify-center shadow-lg">
          <UsersIcon class="w-6 h-6" />
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-800">
            Gestion des Agents
          </h1>
          <p class="text-sm text-gray-600 mt-1">Personnel de votre structure ({{ agents.length }} agents)</p>
        </div>
      </div>
    </template>

    <div class="py-6">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex justify-end mb-6">
          <Link
            :href="route('agent.rs.agents.create')"
            class="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium"
          >
            <PlusIcon class="h-5 w-5" />
            Ajouter un agent
          </Link>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center gap-3">
              <div class="bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-xl w-10 h-10 flex items-center justify-center shadow-lg">
                <UsersIcon class="h-5 w-5" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Liste des Agents</h3>
                <p class="text-sm text-gray-600 mt-1">Personnel de votre structure</p>
              </div>
            </div>
          </div>

          <div v-if="agents.length === 0" class="text-center py-12">
            <UsersIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun agent</h3>
            <p class="mt-1 text-sm text-gray-500">Aucun agent n'a été ajouté à votre structure pour le moment.</p>
            <div class="mt-6">
              <Link
                :href="route('agent.rs.agents.create')"
                class="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-br from-purple-600 via-purple-700 to-violet-600 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium"
              >
                <PlusIcon class="h-4 w-4" />
                Ajouter votre premier agent
              </Link>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Matricule</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fonction</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsable de</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-100">
                <tr v-for="agent in agents" :key="agent.id" class="hover:bg-gray-50 transition">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center gap-3">
                      <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {{ (agent.user?.prenom?.charAt(0) || '') + (agent.user?.nom?.charAt(0) || '') }}
                      </div>
                      <div>
                        <div class="text-sm font-semibold text-slate-900">{{ agent.user?.prenom }} {{ agent.user?.nom }}</div>
                        <div class="text-sm text-slate-600">{{ agent.fonction || 'Non défini' }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    <div>
                      <div class="font-medium">{{ agent.user?.email || '-' }}</div>
                      <div class="text-gray-500">{{ agent.user?.telephone || '-' }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {{ agent.matricule || 'N/A' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {{ agent.fonction || 'Non défini' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    <span v-if="agent.structure_responsable" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {{ agent.structure_responsable }}
                    </span>
                    <span v-else class="text-gray-400 italic">Non assigné</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-center">
                    <div class="flex justify-center gap-2">
                      <Link
                        :href="route('agent.rs.agents.edit', agent.id)"
                        class="text-purple-600 hover:text-purple-900 font-medium flex items-center gap-1"
                        title="Modifier"
                      >
                        <PencilIcon class="w-4 h-4" />
                        Modifier
                      </Link>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </RSLayout>
</template>

<script setup>
import { Link, router } from '@inertiajs/vue3';
import RSLayout from '@/Layouts/RSLayout.vue';
import { UsersIcon, PlusIcon, PencilIcon, TrashIcon } from '@heroicons/vue/24/outline';

const props = defineProps({ agents: Array });

function deleteAgent(id) {
  if (confirm('Êtes-vous sûr de vouloir supprimer cet agent ?')) {
    router.delete(route('agent.rs.agents.destroy', id));
  }
}
</script>