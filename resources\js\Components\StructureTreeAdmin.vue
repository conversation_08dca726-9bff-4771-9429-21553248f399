<template>
  <div class="structure-tree">
    <div v-for="structure in structures" :key="structure.id" class="structure-node">
      <!-- Structure principale -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="p-2 rounded-full bg-blue-100">
              <BuildingOfficeIcon class="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 class="text-base font-semibold text-gray-900">{{ structure.nom }}</h3>
              <p class="text-sm text-gray-600">{{ structure.description || 'Aucune description' }}</p>
            </div>
          </div>
          <button
            @click="toggleExpanded(structure.id)"
            class="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <ChevronDownIcon 
              :class="[
                'w-5 h-5 text-gray-500 transition-transform',
                expandedNodes.includes(structure.id) ? 'rotate-180' : ''
              ]"
            />
          </button>
        </div>

        <!-- Responsable de structure -->
        <div v-if="structure.responsable" class="mt-3 pl-8 border-l-2 border-blue-200">
          <div class="flex items-center space-x-2">
            <div class="p-1 rounded-full bg-green-100">
              <UserIcon class="w-4 h-4 text-green-600" />
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900">
                {{ structure.responsable.nom }} {{ structure.responsable.prenom }}
              </p>
              <p class="text-xs text-gray-600">Responsable de Structure</p>
            </div>
          </div>
        </div>

        <!-- Agents MS de cette structure -->
        <div 
          v-if="expandedNodes.includes(structure.id) && structure.agents_ms && structure.agents_ms.length > 0"
          class="mt-4 pl-8 space-y-2"
        >
          <h4 class="text-sm font-medium text-gray-700 mb-2">Agents MS :</h4>
          <div v-for="agent in structure.agents_ms" :key="agent.id" class="flex items-center space-x-2">
            <div class="p-1 rounded-full bg-purple-100">
              <UserIcon class="w-4 h-4 text-purple-600" />
            </div>
            <div>
              <p class="text-sm text-gray-900">{{ agent.nom }} {{ agent.prenom }}</p>
              <p class="text-xs text-gray-600">{{ agent.fonction || 'Maître de Stage' }}</p>
            </div>
          </div>
        </div>

        <!-- Sous-structures -->
        <div 
          v-if="expandedNodes.includes(structure.id) && structure.sous_structures && structure.sous_structures.length > 0"
          class="mt-4 pl-6"
        >
          <h4 class="text-sm font-medium text-gray-700 mb-2">Sous-structures :</h4>
          <StructureTreeAdmin 
            :structures="structure.sous_structures" 
            :level="level + 1"
            @toggle-expanded="$emit('toggle-expanded', $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { BuildingOfficeIcon, UserIcon, ChevronDownIcon } from '@heroicons/vue/24/outline';

const props = defineProps({
  structures: {
    type: Array,
    required: true
  },
  level: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['toggle-expanded']);

const expandedNodes = ref([]);

const toggleExpanded = (structureId) => {
  const index = expandedNodes.value.indexOf(structureId);
  if (index > -1) {
    expandedNodes.value.splice(index, 1);
  } else {
    expandedNodes.value.push(structureId);
  }
  emit('toggle-expanded', structureId);
};
</script>

<style scoped>
.structure-tree {
  font-family: 'Inter', sans-serif;
}

.structure-node {
  position: relative;
}

.structure-node:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20px;
  top: 100%;
  width: 2px;
  height: 20px;
  background-color: #e5e7eb;
}

@media print {
  .structure-tree {
    break-inside: avoid;
  }
  
  .structure-node {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}
</style>
