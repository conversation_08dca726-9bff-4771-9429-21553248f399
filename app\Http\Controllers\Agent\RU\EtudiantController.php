<?php

namespace App\Http\Controllers\Agent\RU;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\User;
use App\Models\Universite;

class EtudiantController extends Controller
{
    /**
     * Affiche la liste des étudiants de l'université
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        // Récupérer les étudiants de cette université
        $query = User::with(['stagiaire', 'demandesStage.structure'])
            ->whereHas('stagiaire', function ($q) use ($universite) {
                $q->where('universite', $universite->nom_complet);
            })
            ->where('role', 'stagiaire');

        // Filtres
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nom', 'like', "%{$search}%")
                  ->orWhere('prenom', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('niveau')) {
            $query->whereHas('stagiaire', function ($q) use ($request) {
                $q->where('niveau_etude', $request->niveau);
            });
        }

        $etudiants = $query->orderBy('created_at', 'desc')->paginate(15);

        return Inertia::render('Agent/RU/Etudiants/Index', [
            'pageTitle' => 'Étudiants',
            'universite' => $universite,
            'etudiants' => $etudiants,
            'filters' => $request->only(['search', 'niveau']),
        ]);
    }

    /**
     * Affiche les détails d'un étudiant
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Vérifier que l'utilisateur est bien un agent RU
        if (!$user->agent || $user->agent->role_agent !== 'RU') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        $agent = $user->agent;
        
        // Récupérer l'université dont il est responsable
        $universite = null;
        if ($agent->universite_responsable_id) {
            $universite = Universite::find($agent->universite_responsable_id);
        }

        if (!$universite) {
            return redirect()->route('agent.ru.dashboard')
                ->with('error', 'Aucune université assignée.');
        }

        // Récupérer l'étudiant avec ses demandes de stage
        $etudiant = User::with(['stagiaire', 'demandesStage.structure', 'demandesStage.maitreStage.user'])
            ->whereHas('stagiaire', function ($q) use ($universite) {
                $q->where('universite', $universite->nom_complet);
            })
            ->findOrFail($id);

        return Inertia::render('Agent/RU/Etudiants/Show', [
            'pageTitle' => 'Détails Étudiant',
            'universite' => $universite,
            'etudiant' => $etudiant,
        ]);
    }
}
