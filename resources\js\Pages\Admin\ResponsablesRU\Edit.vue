<template>
  <Head title="Modifier Responsable RU" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
          <div class="flex items-center gap-4">
            <div class="p-3 bg-emerald-600 rounded-xl shadow-lg">
              <PencilIcon class="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 class="text-3xl font-bold text-slate-800">Modifier Responsable RU</h1>
              <p class="text-slate-600 mt-1">{{ responsableRu.user.prenom }} {{ responsableRu.user.nom }}</p>
            </div>
          </div>
          <Link
            :href="route('admin.responsables-ru.index')"
            class="inline-flex items-center gap-2 border border-slate-300 text-slate-700 hover:bg-slate-50 px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <ArrowLeftIcon class="w-5 h-5" />
            Retour
          </Link>
        </div>

        <!-- Formulaire -->
        <form @submit.prevent="submit" class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-50 to-emerald-100">
            <h3 class="text-lg font-semibold text-emerald-900">Modification du Responsable RU</h3>
            <p class="text-sm text-emerald-700 mt-1">Tous les champs marqués d'un astérisque (*) sont obligatoires</p>
          </div>

          <div class="p-6 space-y-8">
            <!-- Informations personnelles -->
            <div>
              <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                Informations personnelles
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Nom <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.nom"
                    type="text"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="Nom de famille"
                  />
                  <div v-if="form.errors.nom" class="text-red-500 text-sm mt-1">{{ form.errors.nom }}</div>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Prénom <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.prenom"
                    type="text"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="Prénom"
                  />
                  <div v-if="form.errors.prenom" class="text-red-500 text-sm mt-1">{{ form.errors.prenom }}</div>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Email <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.email"
                    type="email"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="<EMAIL>"
                  />
                  <div v-if="form.errors.email" class="text-red-500 text-sm mt-1">{{ form.errors.email }}</div>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Téléphone <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.telephone"
                    type="tel"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="+229 XX XX XX XX"
                  />
                  <div v-if="form.errors.telephone" class="text-red-500 text-sm mt-1">{{ form.errors.telephone }}</div>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Date de naissance <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.date_de_naissance"
                    type="date"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  />
                  <div v-if="form.errors.date_de_naissance" class="text-red-500 text-sm mt-1">{{ form.errors.date_de_naissance }}</div>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Sexe <span class="text-red-500">*</span>
                  </label>
                  <select
                    v-model="form.sexe"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  >
                    <option value="">Sélectionner</option>
                    <option value="Homme">Homme</option>
                    <option value="Femme">Femme</option>
                  </select>
                  <div v-if="form.errors.sexe" class="text-red-500 text-sm mt-1">{{ form.errors.sexe }}</div>
                </div>
              </div>
            </div>

            <!-- Informations professionnelles -->
            <div>
              <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                Informations professionnelles
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Matricule <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.matricule"
                    type="text"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="RU123456"
                  />
                  <div v-if="form.errors.matricule" class="text-red-500 text-sm mt-1">{{ form.errors.matricule }}</div>
                  <p class="text-sm text-slate-500 mt-1">Format recommandé : RU + 6 caractères</p>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Fonction <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.fonction"
                    type="text"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="Responsable Université"
                  />
                  <div v-if="form.errors.fonction" class="text-red-500 text-sm mt-1">{{ form.errors.fonction }}</div>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">Date d'embauche</label>
                  <input
                    v-model="form.date_embauche"
                    type="date"
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  />
                  <div v-if="form.errors.date_embauche" class="text-red-500 text-sm mt-1">{{ form.errors.date_embauche }}</div>
                  <p class="text-sm text-slate-500 mt-1">Optionnel</p>
                </div>
              </div>
            </div>

            <!-- Université assignée -->
            <div>
              <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                Université assignée
              </h3>
              <div class="grid grid-cols-1 gap-6">
                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Université <span class="text-red-500">*</span>
                  </label>
                  <select
                    v-model="form.universite_id"
                    required
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  >
                    <option value="">Sélectionner une université</option>
                    <option
                      v-for="universite in universites"
                      :key="universite.id"
                      :value="universite.id"
                    >
                      {{ universite.nom_complet }} ({{ universite.sigle }})
                    </option>
                  </select>
                  <div v-if="form.errors.universite_id" class="text-red-500 text-sm mt-1">{{ form.errors.universite_id }}</div>
                  <p class="text-sm text-slate-500 mt-1">
                    <span class="text-red-500 font-medium">Obligatoire :</span> Seules les universités disponibles sont listées
                  </p>
                </div>
              </div>
            </div>

            <!-- Mot de passe -->
            <div>
              <h3 class="text-lg font-semibold text-slate-800 mb-4 pb-2 border-b border-slate-200">
                Authentification
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Nouveau mot de passe
                  </label>
                  <input
                    v-model="form.password"
                    type="password"
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="Laisser vide pour conserver l'actuel"
                  />
                  <div v-if="form.errors.password" class="text-red-500 text-sm mt-1">{{ form.errors.password }}</div>
                  <p class="text-sm text-slate-500 mt-1">Minimum 8 caractères si modifié</p>
                </div>

                <div>
                  <label class="block text-sm font-semibold text-slate-700 mb-2">
                    Confirmer le nouveau mot de passe
                  </label>
                  <input
                    v-model="form.password_confirmation"
                    type="password"
                    class="w-full border border-slate-300 rounded-lg px-4 py-3 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    placeholder="Confirmer le nouveau mot de passe"
                  />
                  <div v-if="form.errors.password_confirmation" class="text-red-500 text-sm mt-1">{{ form.errors.password_confirmation }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
            <div class="flex items-center gap-4">
              <Link
                :href="route('admin.responsables-ru.show', responsableRu.id)"
                class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 font-medium"
              >
                <EyeIcon class="w-4 h-4" />
                Voir détails
              </Link>
            </div>
            <div class="flex items-center gap-4">
              <Link
                :href="route('admin.responsables-ru.index')"
                class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors"
              >
                Annuler
              </Link>
              <button
                type="submit"
                :disabled="form.processing"
                class="px-6 py-2 bg-emerald-600 hover:bg-emerald-700 disabled:bg-emerald-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
              >
                <span v-if="form.processing">Modification...</span>
                <span v-else>Enregistrer les modifications</span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import {
  PencilIcon,
  ArrowLeftIcon,
  EyeIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  responsableRu: Object,
  universites: Array,
});

const form = useForm({
  nom: props.responsableRu.user.nom,
  prenom: props.responsableRu.user.prenom,
  email: props.responsableRu.user.email,
  telephone: props.responsableRu.user.telephone,
  date_de_naissance: props.responsableRu.user.date_de_naissance,
  sexe: props.responsableRu.user.sexe,
  matricule: props.responsableRu.matricule,
  fonction: props.responsableRu.fonction,
  date_embauche: props.responsableRu.date_embauche ? props.responsableRu.date_embauche.split('T')[0] : '',
  universite_id: props.responsableRu.universite_responsable_id || '',
  password: '',
  password_confirmation: '',
});

const submit = () => {
  form.put(route('admin.responsables-ru.update', props.responsableRu.id));
};
</script>
