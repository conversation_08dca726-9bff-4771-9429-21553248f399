<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Structure;
use App\Models\Agent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class OrganigrammeController extends Controller
{
    /**
     * Affiche l'organigramme général (vue seulement pour l'admin)
     */
    public function index()
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // VUE SEULEMENT : L'admin peut voir l'organigramme général mais sans actions
        // Récupérer toutes les structures avec leurs hiérarchies complètes
        $structures = Structure::with([
            'children.children.children.children', // Support jusqu'à 4 niveaux
            'responsable.user',
            'children.responsable.user',
            'children.children.responsable.user',
            'children.children.children.responsable.user'
        ])
            ->whereNull('parent_id') // Commencer par les structures DG
            ->orderBy('ordre')
            ->orderBy('libelle')
            ->get();

        // Récupérer tous les agents pour l'affichage et les statistiques
        $agents = Agent::with(['user', 'structure'])
            ->whereIn('role_agent', ['RS', 'MS'])
            ->get();

        return Inertia::render('Admin/Organigrammes/IndexNew', [
            'structures' => $structures,
            'agents' => $agents,
        ]);
    }

    /**
     * Affiche l'organigramme d'une structure spécifique (vue seulement)
     */
    public function show(Structure $structure)
    {
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('dashboard')->with('error', 'Accès non autorisé.');
        }

        // Charger la structure avec toute sa hiérarchie
        $structure->load(['children.children.children', 'responsable.user', 'parent']);

        // Récupérer tous les agents de cette structure et ses sous-structures
        $getAllSubStructureIds = function($parentId) use (&$getAllSubStructureIds) {
            $ids = [$parentId];
            $children = Structure::where('parent_id', $parentId)->pluck('id');
            foreach ($children as $childId) {
                $ids = array_merge($ids, $getAllSubStructureIds($childId));
            }
            return $ids;
        };

        $allStructureIds = $getAllSubStructureIds($structure->id);
        $agents = Agent::with(['user', 'structure'])
            ->whereIn('structure_id', $allStructureIds)
            ->whereIn('role_agent', ['RS', 'MS'])
            ->get();

        return Inertia::render('Admin/Organigrammes/Show', [
            'structure' => $structure,
            'agents' => $agents,
        ]);
    }
}
