<template>
  <Head title="Dashboard RU" />
  
  <ResponsableUniversiteLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-800">Tableau de bord</h2>
          <p class="text-gray-600 mt-1">
            {{ universite ? `Responsable de ${universite.nom_complet}` : 'Aucune université assignée' }}
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg font-semibold">
            {{ new Date().toLocaleDateString('fr-FR', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) }}
          </div>
        </div>
      </div>
    </template>

    <!-- Alerte si aucune université assignée -->
    <div v-if="!universite" class="mb-6 bg-amber-50 border border-amber-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-amber-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <div>
          <h3 class="text-amber-800 font-semibold">Aucune université assignée</h3>
          <p class="text-amber-700 text-sm">Contactez l'administrateur pour vous assigner une université.</p>
        </div>
      </div>
    </div>

    <!-- Statistiques principales -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Étudiants</p>
            <p class="text-3xl font-bold text-blue-600">{{ stats.total_etudiants }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Évaluations à valider</p>
            <p class="text-3xl font-bold text-orange-600">{{ stats.evaluations_en_attente || 0 }}</p>
          </div>
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Évaluations validées</p>
            <p class="text-3xl font-bold text-green-600">{{ stats.evaluations_validees || 0 }}</p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Stages en cours</p>
            <p class="text-3xl font-bold text-blue-600">{{ stats.stages_en_cours }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Évaluations en attente de validation -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-800">Évaluations à valider</h3>
              <p class="text-sm text-gray-600">Évaluations académiques nécessitant votre validation</p>
            </div>
            <Link :href="route('agent.ru.evaluations.index')"
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
              Voir toutes
            </Link>
          </div>
        </div>
        <div class="p-6">
          <div v-if="!evaluationsEnAttente || evaluationsEnAttente.length === 0" class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p class="text-gray-500">Aucune évaluation en attente</p>
          </div>
          <div v-else class="space-y-4">
            <div v-for="evaluation in evaluationsEnAttente.slice(0, 5)" :key="evaluation.id"
                 class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div class="flex items-center space-x-4">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 font-semibold text-sm">
                    {{ evaluation.stagiaire.prenom.charAt(0) }}{{ evaluation.stagiaire.nom.charAt(0) }}
                  </span>
                </div>
                <div>
                  <p class="font-medium text-gray-800">
                    {{ evaluation.stagiaire.prenom }} {{ evaluation.stagiaire.nom }}
                  </p>
                  <p class="text-sm text-gray-600">{{ evaluation.structure }}</p>
                </div>
              </div>
              <div class="text-right">
                <div class="flex items-center space-x-2">
                  <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
                    {{ evaluation.note_totale }}/20
                  </span>
                  <button @click="validerEvaluation(evaluation.id)"
                          class="px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-xs font-medium">
                    Valider
                  </button>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ evaluation.date_evaluation }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Activités récentes -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Activités récentes</h3>
          <p class="text-sm text-gray-600">Dernières activités de votre université</p>
        </div>
        <div class="p-6">
          <div v-if="activitesRecentes.length === 0" class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p class="text-gray-500">Aucune activité récente</p>
          </div>
          <div v-else class="space-y-4">
            <div v-for="activite in activitesRecentes" :key="activite.date" 
                 class="flex items-start space-x-4">
              <div :class="`w-8 h-8 bg-${activite.color}-100 rounded-full flex items-center justify-center flex-shrink-0`">
                <svg class="w-4 h-4" :class="`text-${activite.color}-600`" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path v-if="activite.icon === 'user-plus'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  <path v-else-if="activite.icon === 'document-plus'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div class="flex-1">
                <p class="text-sm text-gray-800">{{ activite.description }}</p>
                <p class="text-xs text-gray-500">{{ formatDate(activite.date) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ResponsableUniversiteLayout>

  <!-- Modals professionnels -->
  <ProfessionalConfirmModal
    :show="confirmModal.show"
    :type="confirmModal.type"
    :title="confirmModal.title"
    :subtitle="confirmModal.subtitle"
    :message="confirmModal.message"
    :details="confirmModal.details"
    :confirm-text="confirmModal.confirmText"
    :cancel-text="confirmModal.cancelText"
    :is-destructive="confirmModal.isDestructive"
    @confirm="confirmModal.onConfirm"
    @cancel="confirmModal.onCancel"
    @close="confirmModal.onCancel"
  />

  <ProfessionalAlertModal
    :show="alertModal.show"
    :type="alertModal.type"
    :title="alertModal.title"
    :subtitle="alertModal.subtitle"
    :message="alertModal.message"
    :details="alertModal.details"
    :suggestions="alertModal.suggestions"
    :primary-action-text="alertModal.primaryActionText"
    :secondary-action-text="alertModal.secondaryActionText"
    :show-secondary-action="alertModal.showSecondaryAction"
    @primary-action="alertModal.onPrimaryAction"
    @secondary-action="alertModal.onSecondaryAction"
    @close="alertModal.onPrimaryAction"
  />
</template>

<script setup>
import { Head, Link, router } from '@inertiajs/vue3'
import ResponsableUniversiteLayout from '@/Layouts/ResponsableUniversiteLayout.vue'
import ProfessionalConfirmModal from '@/Components/ProfessionalConfirmModal.vue'
import ProfessionalAlertModal from '@/Components/ProfessionalAlertModal.vue'
import { useProfessionalModals } from '@/Composables/useProfessionalModals.js'

const props = defineProps({
  pageTitle: String,
  universite: Object,
  stats: Object,
  dernieresDemandes: Array,
  activitesRecentes: Array,
  agent: Object,
  evaluationsEnAttente: {
    type: Array,
    default: () => []
  },
  stagesRecents: {
    type: Array,
    default: () => []
  },
})

const { confirmModal, alertModal, confirmValidateEvaluation, alertValidationError } = useProfessionalModals();

const getStatusClass = (status) => {
  const classes = {
    'En attente': 'bg-amber-100 text-amber-800',
    'Acceptée': 'bg-green-100 text-green-800',
    'Refusée': 'bg-red-100 text-red-800',
    'En cours': 'bg-blue-100 text-blue-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('fr-FR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const validerEvaluation = async (evaluationId) => {
  const confirmed = await confirmValidateEvaluation();
  if (confirmed) {
    router.post(route('agent.ru.evaluations.valider', evaluationId), {}, {
      onSuccess: () => {
        // Recharger la page pour mettre à jour les données
        router.reload()
      },
      onError: (errors) => {
        alertValidationError(errors.message || 'Erreur inconnue lors de la validation')
      }
    })
  }
}
</script>
