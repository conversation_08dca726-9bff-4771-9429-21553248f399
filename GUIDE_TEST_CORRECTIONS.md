# 🔧 GUIDE DE TEST - CORRECTIONS SYSTÈME D'ÉVALUATION

## 🎯 **PROBLÈMES RÉSOLUS**

### ✅ **PROBLÈME 1 : Erreur 404 sur `/agent/ms/stages/{stage}/evaluations/principal`**

**Cause identifiée** : Le frontend passait `membre.id` au lieu de `membre.user_id` à la route

**Correction appliquée** :
- **Fichier** : `resources/js/Pages/Agent/MS/Stages/Show.vue`
- **Ligne** : 1878
- **Changement** : Utilisation de `membre.user_id || (membre.user && membre.user.id)` au lieu de `membre.id`

### ✅ **PROBLÈME 2 : Système d'emails non fonctionnel**

**Causes identifiées** :
1. Queue configurée en mode `database` sans workers actifs
2. Configuration SMTP pointant vers MailHog (port 1025) non démarré

**Corrections appliquées** :
- **Configuration Queue** : `QUEUE_CONNECTION=sync` (envoi immédiat)
- **Configuration Mail** : `MAIL_MAILER=log` (logs dans `storage/logs/laravel.log`)
- **Logs détaillés** : Ajout de logs de debugging dans `StageController.php`

---

## 🧪 **PROCÉDURE DE TEST**

### **ÉTAPE 1 : Vérification de l'application**

```bash
# Démarrer le serveur Laravel
php artisan serve --host=127.0.0.1 --port=8000

# Vérifier que l'application répond
curl http://127.0.0.1:8000
```

### **ÉTAPE 2 : Test de la correction 404**

1. **Se connecter en tant que Maître de Stage (MS)**
2. **Accéder à un stage avec des membres de groupe**
3. **Cliquer sur "Consulter l'évaluation" d'un membre**
4. **Vérifier** : Plus d'erreur 404, la modal s'ouvre correctement

**Logs à vérifier** :
```bash
tail -f storage/logs/laravel.log | grep "getEvaluationMembre"
```

### **ÉTAPE 3 : Test du système d'emails**

#### **3.1 Test Évaluation Académique**

1. **Se connecter en tant que MS**
2. **Évaluer un stagiaire académique**
3. **Soumettre l'évaluation**
4. **Vérifier les logs** :

```bash
tail -f storage/logs/laravel.log | grep "TENTATIVE\|SUCCÈS\|ERREUR"
```

**Logs attendus** :
```
[timestamp] TENTATIVE envoi email stagiaire académique
[timestamp] SUCCÈS Email stagiaire académique envoyé
[timestamp] TENTATIVE envoi email RU
[timestamp] SUCCÈS Email RU envoyé
```

#### **3.2 Test Évaluation Professionnelle**

1. **Se connecter en tant que MS**
2. **Évaluer un stagiaire professionnel**
3. **Soumettre l'évaluation**
4. **Vérifier les logs** :

**Logs attendus** :
```
[timestamp] TENTATIVE envoi email stagiaire professionnel
[timestamp] SUCCÈS Email stagiaire professionnel envoyé
```

### **ÉTAPE 4 : Vérification des emails dans les logs**

Les emails sont maintenant enregistrés dans `storage/logs/laravel.log` avec le driver `log`.

**Commande de recherche** :
```bash
grep -A 20 -B 5 "Subject:" storage/logs/laravel.log
```

**Emails attendus** :
- **Académique** : "Votre évaluation de stage a été complétée"
- **Professionnel** : "Votre évaluation de stage est disponible"
- **RU** : "Nouvelle évaluation académique à valider"

---

## 🔍 **DIAGNOSTIC EN CAS DE PROBLÈME**

### **Si l'erreur 404 persiste**

1. **Vérifier la route** :
```bash
php artisan route:list | grep evaluations
```

2. **Vérifier les données du membre** :
```javascript
// Dans la console du navigateur
console.log(membre);
// Doit contenir user_id ou user.id
```

### **Si les emails ne sont pas envoyés**

1. **Vérifier la configuration** :
```bash
php artisan config:cache
php artisan config:clear
```

2. **Vérifier les logs d'erreur** :
```bash
tail -f storage/logs/laravel.log | grep "ERREUR"
```

3. **Tester manuellement** :
```bash
php artisan tinker
>>> Mail::raw('Test email', function($msg) { $msg->to('<EMAIL>')->subject('Test'); });
```

### **Si les classes Mailable sont introuvables**

1. **Vérifier l'autoload** :
```bash
composer dump-autoload
```

2. **Vérifier les imports** dans `StageController.php` :
```php
use App\Mail\StagiaireAcademiqueEvalue;
use App\Mail\RuEvaluationAcademique;
use App\Mail\StagiaireProfessionnelEvalue;
```

---

## ✅ **VALIDATION COMPLÈTE**

### **Checklist de validation**

- [ ] **Application démarre** sans erreur
- [ ] **Connexion MS** fonctionne
- [ ] **Consultation évaluations membres** sans erreur 404
- [ ] **Évaluation académique** génère les logs d'emails
- [ ] **Évaluation professionnelle** génère les logs d'emails
- [ ] **Emails visibles** dans `storage/logs/laravel.log`
- [ ] **Contenu des emails** correct selon le type de stage

### **Tests de régression**

- [ ] **Système d'évaluation existant** fonctionne toujours
- [ ] **Interface MS** inchangée et fonctionnelle
- [ ] **Interface RU** accessible et harmonisée
- [ ] **Notifications** fonctionnent correctement

---

## 🚀 **MISE EN PRODUCTION**

### **Avant déploiement**

1. **Restaurer la configuration email production** :
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-server.com
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
```

2. **Configurer la queue pour la production** :
```env
QUEUE_CONNECTION=database
```

3. **Démarrer les workers** :
```bash
php artisan queue:work --daemon
```

### **Monitoring post-déploiement**

1. **Surveiller les logs d'emails** :
```bash
tail -f storage/logs/laravel.log | grep "Email.*envoyé\|ERREUR"
```

2. **Vérifier les jobs en queue** :
```bash
php artisan queue:monitor
```

3. **Tester les workflows complets** :
   - Évaluation académique → Email stagiaire + Email RU
   - Validation RU → Email stagiaire avec notes
   - Évaluation professionnelle → Email stagiaire immédiat

---

## 📞 **SUPPORT**

En cas de problème persistant :

1. **Collecter les logs** :
```bash
tail -n 100 storage/logs/laravel.log > debug_logs.txt
```

2. **Vérifier la configuration** :
```bash
php artisan config:show mail
php artisan config:show queue
```

3. **Tester les composants individuellement** :
   - Classes Mailable
   - Templates Blade
   - Configuration SMTP
   - Workers de queue
