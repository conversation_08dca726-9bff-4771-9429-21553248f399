<template>
  <Head title="Détails Agent MS" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <!-- Header avec navigation -->
        <div class="flex items-center gap-4 mb-8">
          <Link 
            :href="route('admin.agents-ms.index')" 
            class="p-2 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow border border-slate-200"
          >
            <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          </Link>
          <div class="p-3 bg-green-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-slate-800">Détails Agent MS</h1>
            <p class="text-slate-600 mt-1">Consultation des informations détaillées</p>
          </div>
        </div>

        <!-- Carte principale avec informations -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <!-- Header de la carte -->
          <div class="px-8 py-6 bg-gradient-to-r from-green-600 to-green-700 text-white">
            <div class="flex items-center gap-6">
              <div class="w-20 h-20 rounded-2xl bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm">
                <span class="text-2xl font-bold text-white">
                  {{ getInitials(agent.user) }}
                </span>
              </div>
              <div>
                <h2 class="text-2xl font-bold">{{ getFullName(agent.user) }}</h2>
                <p class="text-green-100 mt-1">{{ agent.fonction || 'Maître de Stage' }}</p>
                <div class="flex items-center gap-2 mt-2">
                  <span class="px-3 py-1 bg-white bg-opacity-20 rounded-lg text-sm font-semibold">
                    {{ agent.role_agent }}
                  </span>
                  <span class="px-3 py-1 bg-white bg-opacity-20 rounded-lg text-sm font-semibold">
                    {{ agent.matricule || 'N/A' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Contenu principal -->
          <div class="p-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Informations personnelles -->
              <div class="space-y-6">
                <div class="border-b border-slate-200 pb-4">
                  <h3 class="text-lg font-bold text-slate-800 mb-4 flex items-center gap-2">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                      </svg>
                    </div>
                    Informations Personnelles
                  </h3>
                </div>

                <div class="space-y-4">
                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Email</p>
                      <p class="text-slate-800 font-medium">{{ agent.user?.email || 'Non renseigné' }}</p>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Téléphone</p>
                      <p class="text-slate-800 font-medium">{{ agent.user?.telephone || 'Non renseigné' }}</p>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Sexe</p>
                      <p class="text-slate-800 font-medium">{{ agent.user?.sexe || 'Non renseigné' }}</p>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Date de naissance</p>
                      <p class="text-slate-800 font-medium">{{ formatDate(agent.user?.date_de_naissance) || 'Non renseigné' }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Informations professionnelles -->
              <div class="space-y-6">
                <div class="border-b border-slate-200 pb-4">
                  <h3 class="text-lg font-bold text-slate-800 mb-4 flex items-center gap-2">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6"/>
                      </svg>
                    </div>
                    Informations Professionnelles
                  </h3>
                </div>

                <div class="space-y-4">
                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Matricule</p>
                      <p class="text-slate-800 font-medium">{{ agent.matricule || 'Non renseigné' }}</p>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Fonction</p>
                      <p class="text-slate-800 font-medium">{{ agent.fonction || 'Non renseigné' }}</p>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Date d'embauche</p>
                      <p class="text-slate-800 font-medium">{{ formatDate(agent.date_embauche) || 'Non renseigné' }}</p>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-slate-100 rounded-xl flex items-center justify-center">
                      <svg class="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm font-semibold text-slate-600">Créé par</p>
                      <p class="text-slate-800 font-medium">{{ getFullName(agent.creator) || 'Non renseigné' }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Section structures -->
            <div class="mt-8 pt-8 border-t border-slate-200">
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Structure de Direction Générale -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
                  <h3 class="text-lg font-bold text-slate-800 mb-4 flex items-center gap-2">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                      </svg>
                    </div>
                    Structure de Direction Générale
                  </h3>
                  <div v-if="structureDirectionGenerale">
                    <div class="bg-white rounded-xl p-4 border border-blue-100">
                      <p class="text-sm font-semibold text-blue-600 mb-2">{{ structureDirectionGenerale.sigle }}</p>
                      <p class="text-slate-800 font-medium">{{ structureDirectionGenerale.libelle }}</p>
                      <p class="text-xs text-slate-500 mt-2">Structure principale de rattachement</p>
                    </div>
                  </div>
                  <div v-else class="text-slate-500 italic">
                    Aucune structure de direction générale identifiée
                  </div>
                </div>

                <!-- Sous-structure de travail -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                  <h3 class="text-lg font-bold text-slate-800 mb-4 flex items-center gap-2">
                    <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                      </svg>
                    </div>
                    Sous-structure de Travail
                  </h3>
                  <div v-if="agent.structure">
                    <div class="bg-white rounded-xl p-4 border border-green-100">
                      <p class="text-sm font-semibold text-green-600 mb-2">{{ agent.structure.sigle }}</p>
                      <p class="text-slate-800 font-medium">{{ agent.structure.libelle }}</p>
                      <p class="text-xs text-slate-500 mt-2">Sous-structure d'affectation</p>
                    </div>
                  </div>
                  <div v-else class="text-slate-500 italic">
                    Aucune sous-structure assignée
                  </div>
                </div>

                <!-- Statut de responsabilité -->
                <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl p-6 border border-amber-200">
                  <h3 class="text-lg font-bold text-slate-800 mb-4 flex items-center gap-2">
                    <div class="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                    Responsabilité
                  </h3>
                  <div class="bg-white rounded-xl p-4 border border-amber-100">
                    <div class="flex items-center gap-3">
                      <div :class="[
                        'w-3 h-3 rounded-full',
                        estResponsableSousStructure ? 'bg-green-500' : 'bg-slate-400'
                      ]"></div>
                      <div>
                        <p class="font-semibold text-slate-800">
                          {{ estResponsableSousStructure ? 'Responsable' : 'Agent' }}
                        </p>
                        <p class="text-xs text-slate-500">
                          {{ estResponsableSousStructure ? 'Responsable de sa sous-structure' : 'Agent sans responsabilité de structure' }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Sous-structures supervisées (si applicable) -->
              <div v-if="agent.structuresResponsable && agent.structuresResponsable.length > 0" class="mt-6">
                <div class="bg-slate-50 rounded-2xl p-6 border border-slate-200">
                  <h3 class="text-lg font-bold text-slate-800 mb-4 flex items-center gap-2">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                      </svg>
                    </div>
                    Sous-structures Supervisées
                  </h3>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div v-for="structure in agent.structuresResponsable" :key="structure.id"
                         class="bg-white rounded-xl p-4 border border-slate-200">
                      <p class="text-sm font-semibold text-purple-600 mb-1">{{ structure.sigle }}</p>
                      <p class="text-slate-800 font-medium">{{ structure.libelle }}</p>
                      <p class="text-xs text-slate-500 mt-2">Sous-structure sous supervision</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';

const props = defineProps({
  agent: Object,
  structureDirectionGenerale: Object,
  estResponsableSousStructure: Boolean,
});

// Fonctions helper pour l'affichage des noms
const getFullName = (user) => {
  if (!user) return 'Non défini';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  return `${prenom} ${nom}`.trim() || 'Non défini';
};

const getInitials = (user) => {
  if (!user) return 'N';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  const prenomInitial = prenom.charAt(0).toUpperCase();
  const nomInitial = nom.charAt(0).toUpperCase();
  return `${prenomInitial}${nomInitial}` || 'N';
};

const formatDate = (dateString) => {
  if (!dateString) return null;
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
</script>
