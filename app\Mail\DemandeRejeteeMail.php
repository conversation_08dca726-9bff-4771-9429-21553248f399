<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class DemandeRejeteeMail extends Mailable
{
    use Queueable, SerializesModels;

    public $stagiaire;
    public $demande;
    public $motifRejet;

    /**
     * Create a new message instance.
     */
    public function __construct($stagiaire, $demande, $motifRejet = null)
    {
        $this->stagiaire = $stagiaire;
        $this->demande = $demande;
        $this->motifRejet = $motifRejet;
    }

    public function build()
    {
        return $this->view('emails.demande_rejetee')
            ->subject('📋 Réponse à votre demande de stage - MEF Bénin')
            ->with([
                'stagiaire' => $this->stagiaire,
                'demande' => $this->demande,
                'motifRejet' => $this->motifRejet,
            ]);
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
