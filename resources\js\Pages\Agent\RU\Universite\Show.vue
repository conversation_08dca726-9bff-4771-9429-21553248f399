<template>
  <Head title="Mon Université" />
  
  <ResponsableUniversiteLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-800">Mon Université</h2>
          <p class="text-gray-600 mt-1">Informations détaillées de votre université</p>
        </div>
      </div>
    </template>

    <div class="max-w-4xl mx-auto">
      <!-- Carte principale de l'université -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-8">
        <!-- En-tête avec gradient bleu ministériel -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 px-8 py-6">
          <div class="flex items-center justify-between text-white">
            <div class="flex-1">
              <div v-if="!editMode">
                <h1 class="text-3xl font-bold">{{ universite.nom_complet }}</h1>
                <p class="text-blue-100 text-lg mt-1">{{ universite.sigle }}</p>
              </div>
              <div v-else class="space-y-3">
                <input
                  v-model="form.nom_complet"
                  type="text"
                  class="w-full text-3xl font-bold bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/30"
                  placeholder="Nom complet de l'université"
                />
                <input
                  v-model="form.sigle"
                  type="text"
                  class="w-full text-lg bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white/30"
                  placeholder="Sigle"
                />
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
                </svg>
              </div>
              <button
                @click="toggleEditMode"
                :disabled="form.processing"
                class="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors flex items-center space-x-2"
              >
                <svg v-if="!editMode" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
                <span class="text-sm font-medium">{{ editMode ? 'Annuler' : 'Modifier' }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Contenu de la carte -->
        <div class="p-8">
          <!-- Mode consultation -->
          <div v-if="!editMode" class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Informations générales -->
            <div>
              <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Informations générales
              </h3>
              <div class="space-y-4">
                <div class="flex items-start">
                  <span class="text-sm font-medium text-gray-600 w-24 flex-shrink-0">Nom complet:</span>
                  <span class="text-sm text-gray-800">{{ universite.nom_complet }}</span>
                </div>
                <div class="flex items-start">
                  <span class="text-sm font-medium text-gray-600 w-24 flex-shrink-0">Sigle:</span>
                  <span class="text-sm text-gray-800">{{ universite.sigle }}</span>
                </div>
                <div class="flex items-start">
                  <span class="text-sm font-medium text-gray-600 w-24 flex-shrink-0">Localisation:</span>
                  <span class="text-sm text-gray-800">{{ universite.localisation || '—' }}</span>
                </div>

                <div class="flex items-start">
                  <span class="text-sm font-medium text-gray-600 w-24 flex-shrink-0">Statut:</span>
                  <span :class="universite.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                        class="inline-flex px-2 py-1 text-xs font-medium rounded-full">
                    {{ universite.active ? 'Active' : 'Inactive' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Description -->
            <div>
              <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"/>
                </svg>
                Description
              </h3>
              <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <p class="text-sm text-gray-700 leading-relaxed">
                  {{ universite.description || 'Aucune description disponible.' }}
                </p>
              </div>
            </div>
          </div>

          <!-- Mode édition -->
          <form v-else @submit.prevent="saveChanges" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Informations générales -->
              <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  Informations générales
                </h3>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Nom complet</label>
                  <input
                    v-model="form.nom_complet"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    :class="{ 'border-red-500': form.errors.nom_complet }"
                    required
                  />
                  <p v-if="form.errors.nom_complet" class="text-red-500 text-xs mt-1">{{ form.errors.nom_complet }}</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Sigle</label>
                  <input
                    v-model="form.sigle"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    :class="{ 'border-red-500': form.errors.sigle }"
                    required
                  />
                  <p v-if="form.errors.sigle" class="text-red-500 text-xs mt-1">{{ form.errors.sigle }}</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Localisation</label>
                  <input
                    v-model="form.localisation"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    :class="{ 'border-red-500': form.errors.localisation }"
                    required
                  />
                  <p v-if="form.errors.localisation" class="text-red-500 text-xs mt-1">{{ form.errors.localisation }}</p>
                </div>


              </div>

              <!-- Description -->
              <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"/>
                  </svg>
                  Description
                </h3>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description de l'université</label>
                  <textarea
                    v-model="form.description"
                    rows="8"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    :class="{ 'border-red-500': form.errors.description }"
                    placeholder="Décrivez l'université, ses spécialités, son histoire..."
                  ></textarea>
                  <p v-if="form.errors.description" class="text-red-500 text-xs mt-1">{{ form.errors.description }}</p>
                </div>
              </div>
            </div>

            <!-- Boutons d'action -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                @click="cancelEdit"
                class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                :disabled="form.processing"
              >
                Annuler
              </button>
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2"
                :disabled="form.processing"
              >
                <svg v-if="form.processing" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{{ form.processing ? 'Enregistrement...' : 'Enregistrer' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Actions rapides -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Link :href="route('agent.ru.stages.index')"
              class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors">
                Stages des étudiants
              </h3>
              <p class="text-sm text-gray-600 mt-1">Consulter et imprimer les notes d'évaluation</p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
              </svg>
            </div>
          </div>
        </Link>

        <button
          @click="generateReport"
          :disabled="reportGenerating"
          class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow group text-left w-full">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-800 group-hover:text-purple-600 transition-colors">
                Rapport général université
              </h3>
              <p class="text-sm text-gray-600 mt-1">Générer un rapport PDF complet</p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
              <svg v-if="reportGenerating" class="w-6 h-6 text-purple-600 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
          </div>
        </button>
      </div>
    </div>
  </ResponsableUniversiteLayout>
</template>

<script setup>
import { Head, Link, useForm, router } from '@inertiajs/vue3'
import { ref, reactive } from 'vue'
import ResponsableUniversiteLayout from '@/Layouts/ResponsableUniversiteLayout.vue'

const props = defineProps({
  pageTitle: String,
  universite: Object,
  agent: Object,
})

// État de l'édition
const editMode = ref(false)
const reportGenerating = ref(false)

// Formulaire pour l'édition
const form = useForm({
  nom_complet: props.universite.nom_complet,
  sigle: props.universite.sigle,
  description: props.universite.description || '',
  localisation: props.universite.localisation,
})

// Fonctions
const toggleEditMode = () => {
  if (editMode.value) {
    cancelEdit()
  } else {
    editMode.value = true
  }
}

const cancelEdit = () => {
  editMode.value = false
  // Réinitialiser le formulaire avec les valeurs originales
  form.reset()
  form.clearErrors()
}

const saveChanges = () => {
  form.put(route('agent.ru.universite.update'), {
    preserveScroll: true,
    onSuccess: () => {
      editMode.value = false
      // Recharger la page pour avoir les nouvelles données
      router.reload({ only: ['universite'] })
    },
    onError: (errors) => {
      console.error('Erreurs de validation:', errors)
    }
  })
}

const generateReport = () => {
  reportGenerating.value = true

  // Simuler la génération du rapport (à remplacer par l'appel réel)
  setTimeout(() => {
    reportGenerating.value = false
    // Ici, on ferait l'appel à l'API pour générer le rapport PDF
    window.open(route('agent.ru.rapport.universite'), '_blank')
  }, 2000)
}
</script>
