<template>
  <Head title="Structures" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Header professionnel -->
        <div class="flex items-center gap-4 mb-8">
          <div class="p-3 bg-blue-600 rounded-xl shadow-lg">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-slate-800">Structures / Directions Générales</h1>
            <p class="text-slate-600 mt-1">Gestion des structures principales de l'organisation</p>
          </div>
        </div>

        <AdminToast ref="toast" />

        <!-- Statistiques professionnelles -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-blue-100 rounded-xl">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Total Structures</p>
                <p class="text-2xl font-bold text-slate-800">{{ props.structures.length }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-green-100 rounded-xl">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Avec Responsable</p>
                <p class="text-2xl font-bold text-slate-800">{{ structuresAvecResponsable }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
            <div class="flex items-center">
              <div class="p-3 bg-amber-100 rounded-xl">
                <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-slate-600">Sans Responsable</p>
                <p class="text-2xl font-bold text-slate-800">{{ structuresSansResponsable }}</p>
              </div>
            </div>
          </div>
        </div>
        <!-- Interface principale -->
        <div class="bg-white rounded-2xl shadow-lg border border-slate-200 overflow-hidden">
          <!-- Header avec actions -->
          <div class="px-6 py-4 border-b border-slate-200 bg-slate-50">
            <div class="flex justify-between items-center">
              <div>
                <h2 class="text-xl font-bold text-slate-800">Liste des Structures</h2>
                <p class="text-sm text-slate-600 mt-1">{{ props.structures.length }} structure(s) enregistrée(s)</p>
              </div>
              <button
                @click="openModal()"
                class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-semibold flex items-center gap-2 transition-colors shadow-lg"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
                Ajouter une structure
              </button>
            </div>
          </div>

          <!-- État vide -->
          <div v-if="props.structures.length === 0" class="text-center py-16">
            <div class="w-20 h-20 mx-auto bg-slate-100 rounded-full flex items-center justify-center mb-4">
              <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-slate-800 mb-2">Aucune structure enregistrée</h3>
            <p class="text-slate-600 mb-6">Commencez par créer votre première structure de direction générale.</p>
            <button
              @click="openModal()"
              class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-semibold flex items-center gap-2 mx-auto transition-colors"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              Créer une structure
            </button>
          </div>

          <!-- Tableau responsive avec scroll horizontal -->
          <div v-else class="overflow-x-auto">
            <div class="inline-block min-w-full align-middle">
              <table class="min-w-full">
                <thead class="bg-slate-100">
                  <tr>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[300px]">Structure</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[250px]">Description</th>
                    <th class="px-6 py-4 text-left text-sm font-bold text-slate-700 min-w-[200px]">Responsable</th>
                    <th class="px-6 py-4 text-center text-sm font-bold text-slate-700 min-w-[150px]">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-200">
                  <tr v-for="structure in props.structures" :key="structure.id" class="hover:bg-slate-50 transition-colors">
                    <td class="px-6 py-4">
                      <div class="flex items-center gap-4">
                        <div class="flex-shrink-0">
                          <div class="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m5 0v-9a2 2 0 00-2-2H8a2 2 0 00-2 2v9m8 0V9a2 2 0 012-2h2a2 2 0 012 2v12"/>
                            </svg>
                          </div>
                        </div>
                        <div>
                          <div class="text-sm font-bold text-slate-800">{{ structure.sigle }}</div>
                          <div class="text-sm text-slate-600 mt-1">{{ structure.libelle }}</div>
                        </div>
                      </div>
                    </td>

                    <td class="px-6 py-4">
                      <div class="text-sm text-slate-700">
                        {{ structure.description || 'Aucune description disponible' }}
                      </div>
                    </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    <div v-if="structure.responsable && structure.responsable.user" class="flex items-center">
                      <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mr-2">
                        <span class="text-xs font-medium text-green-600">
                          {{ getInitials(structure.responsable.user) }}
                        </span>
                      </div>
                      <div>
                        <div class="font-medium text-gray-900">{{ getFullName(structure.responsable.user) }}</div>
                        <div class="text-xs text-gray-500">Agent RS</div>
                      </div>
                    </div>
                    <div v-else class="flex items-center">
                      <div class="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center mr-2">
                        <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                        </svg>
                      </div>
                      <div>
                        <div class="font-medium text-amber-700">Non défini</div>
                        <div class="text-xs text-gray-500">Aucun responsable assigné</div>
                      </div>
                    </div>
                  </td>

                    <td class="px-6 py-4">
                      <div class="flex justify-center gap-2">
                        <button
                          @click="openModal(structure)"
                          class="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors font-semibold flex items-center gap-2"
                          title="Modifier la structure"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                          </svg>
                          Modifier
                        </button>
                        <button
                          @click="openDeleteModal(structure)"
                          class="px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors font-semibold flex items-center gap-2"
                          title="Supprimer la structure"
                        >
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                          </svg>
                          Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Modal d'édition/création -->
        <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <!-- En-tête de la modale -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 class="text-xl font-bold text-gray-800">
                {{ editingId ? 'Modifier une structure' : 'Ajouter une structure' }}
              </h3>
              <button @click="closeModal" class="text-gray-500 hover:text-gray-700 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <!-- Formulaire -->
            <form @submit.prevent="submit" class="px-6 py-4">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Sigle</label>
                  <input 
                    v-model="form.sigle" 
                    class="w-full border rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Ex: ONU"
                  />
                  <div v-if="form.errors.sigle" class="mt-1 text-sm text-red-600">{{ form.errors.sigle }}</div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Libellé</label>
                  <input 
                    v-model="form.libelle" 
                    class="w-full border rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Ex: Organisation des Nations Unies"
                  />
                  <div v-if="form.errors.libelle" class="mt-1 text-sm text-red-600">{{ form.errors.libelle }}</div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea 
                    v-model="form.description" 
                    class="w-full border rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                    placeholder="Description de la structure..."
                  ></textarea>
                  <div v-if="form.errors.description" class="mt-1 text-sm text-red-600">{{ form.errors.description }}</div>
                </div>
                
                <!-- Champ pour sélectionner une structure si le rôle est "RS" -->
                <div v-if="form.role_agent === 'RS'">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Structure</label>
                  <select v-model="form.structure_id" class="w-full border rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                    <option value="">Sélectionner une structure</option>
                    <option v-for="structure in availableStructures" :key="structure.id" :value="structure.id">
                      {{ structure.libelle }}
                    </option>
                  </select>
                  <div v-if="form.errors.structure_id" class="text-red-600 text-sm mt-1">{{ form.errors.structure_id }}</div>
                </div>
              </div>

              <!-- Boutons de navigation -->
              <div class="flex justify-between pt-6 border-t mt-6">
                <button 
                  type="button" 
                  @click="closeModal()" 
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400 flex items-center gap-1"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 6 6 18M6 6l12 12"/>
                  </svg>
                  Annuler
                </button>

                <button 
                  type="submit" 
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center gap-1"
                  :disabled="form.processing"
                >
                  <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span v-if="editingId">Mettre à jour</span>
                  <span v-else>Ajouter</span>
                  <svg v-if="!form.processing" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 12h14"/>
                    <path d="m12 5 7 7-7 7"/>
                  </svg>
                </button>
              </div>
            </form>
          </div>
        </div>
        
        <!-- Modal de confirmation de suppression -->
        <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
            <div class="px-6 py-4 bg-red-50 border-b border-red-100">
              <div class="flex items-center">
                <div class="flex-shrink-0 bg-red-100 rounded-full p-2 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
                <h3 class="text-lg font-medium text-red-800">Supprimer la structure</h3>
              </div>
            </div>
            
            <div class="px-6 py-4">
              <p class="text-gray-700 mb-4">
                Voulez-vous vraiment supprimer la structure "{{ structureToDelete?.libelle || '' }}" ?<br>
                Cette action est irréversible.
              </p>
              
              <div class="flex justify-end space-x-3">
                <button 
                  @click="closeDeleteModal" 
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Annuler
                </button>
                <button 
                  @click="confirmDelete" 
                  class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { Head, useForm, router, usePage } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
//import SimpleLayout from '@/Layouts/SimpleLayout.vue';
import AdminToast from '@/Components/AdminToast.vue';
import axios from 'axios';

const props = defineProps({
  structures: Array,
});

const page = usePage();
const toast = ref(null);
const showModal = ref(false);
const editingId = ref(null);
const availableStructures = ref([]); // Variable pour stocker les structures disponibles

// Ajout des variables pour la confirmation de suppression
const showDeleteModal = ref(false);
const structureToDelete = ref(null);

// Fonctions helper pour l'affichage des noms
const getFullName = (user) => {
  if (!user) return 'Non défini';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  return `${prenom} ${nom}`.trim() || 'Non défini';
};

const getInitials = (user) => {
  if (!user) return 'N';
  const prenom = user.prenom || '';
  const nom = user.nom || '';
  const prenomInitial = prenom.charAt(0).toUpperCase();
  const nomInitial = nom.charAt(0).toUpperCase();
  return `${prenomInitial}${nomInitial}` || 'N';
};

// Computed properties pour les statistiques
const structuresAvecResponsable = computed(() => {
  return props.structures.filter(structure =>
    structure.responsable && structure.responsable.user
  ).length;
});

const structuresSansResponsable = computed(() => {
  return props.structures.filter(structure =>
    !structure.responsable || !structure.responsable.user
  ).length;
});

const form = useForm({
  sigle: '',
  libelle: '',
  description: '',
  role_agent: '',
  structure_id: null,
});

// Fonction pour charger les structures disponibles
async function loadAvailableStructures() {
  try {
    // CORRECTION : URL absolue pour éviter les problèmes de route()
    const response = await axios.get('/admin/structures/available');
    availableStructures.value = response.data; // Stockez les données dans la variable
  } catch (error) {
    console.error('Erreur lors du chargement des structures disponibles :', error);
  }
}

// Surveillez les changements dans le rôle
watch(() => form.role_agent, (newRole) => {
  if (newRole === 'RS') {
    loadAvailableStructures(); // Chargez les structures disponibles
  } else {
    form.structure_id = null; // Réinitialisez la structure sélectionnée
  }
});

// Surveiller les messages flash et les afficher automatiquement
onMounted(() => {
  // Vérifier si des messages flash existent au chargement
  setTimeout(() => {
    const flash = usePage().props.flash || {};
    if (flash) {
      if (flash.success && toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Succès',
          message: flash.success
        });
      }
      
      if (flash.error && toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur',
          message: flash.error
        });
      }
    }
  }, 100); // Petit délai pour s'assurer que le composant est monté
});

function openModal(structure = null) {
  if (structure) {
    form.sigle = structure.sigle;
    form.libelle = structure.libelle;
    form.description = structure.description;
    editingId.value = structure.id;
  } else {
    form.reset();
    editingId.value = null;
  }
  showModal.value = true;
}

function closeModal() {
  showModal.value = false;
  form.reset();
  editingId.value = null;
}

// Fonctions pour le modal de confirmation de suppression
function openDeleteModal(structure) {
  structureToDelete.value = structure;
  showDeleteModal.value = true;
}

function closeDeleteModal() {
  showDeleteModal.value = false;
  structureToDelete.value = null;
}

function confirmDelete() {
  if (!structureToDelete.value) return;
  
  destroy(structureToDelete.value.id);
  closeDeleteModal();
}

function submit() {
  if (editingId.value) {
    form.put(route('admin.structures.update', editingId.value), {
      preserveScroll: true,
      onSuccess: () => {
        closeModal();
        // Afficher un message personnalisé
        if (toast.value) {
          toast.value.addToast({
            type: 'success',
            title: 'Structure modifiée',
            message: `La structure "${form.libelle}" a été mise à jour avec succès.`
          });
        }
      },
      onError: () => {
        if (toast.value) {
          toast.value.addToast({
            type: 'error',
            title: 'Erreur de validation',
            message: 'Veuillez vérifier les informations saisies'
          });
        }
      }
    });
  } else {
    form.post(route('admin.structures.store'), {
      preserveScroll: true,
      onSuccess: () => {
        closeModal();
        // Afficher un message personnalisé
        if (toast.value) {
          toast.value.addToast({
            type: 'success',
            title: 'Structure ajoutée',
            message: `La structure "${form.libelle}" a été ajoutée avec succès.`
          });
        }
      },
      onError: () => {
        if (toast.value) {
          toast.value.addToast({
            type: 'error',
            title: 'Erreur de validation',
            message: 'Veuillez vérifier les informations saisies'
          });
        }
      }
    });
  }
}

function destroy(id) {
  // Trouver la structure pour afficher son nom dans le message de confirmation
  const structure = props.structures.find(s => s.id === id);
  
  router.delete(route('admin.structures.destroy', id), {
    onSuccess: () => {
      // Afficher un message personnalisé
      if (toast.value) {
        toast.value.addToast({
          type: 'success',
          title: 'Structure supprimée',
          message: `La structure "${structure?.libelle || ''}" a été supprimée avec succès.`
        });
      }
    },
    onError: () => {
      if (toast.value) {
        toast.value.addToast({
          type: 'error',
          title: 'Erreur de suppression',
          message: 'Impossible de supprimer cette structure'
        });
      }
    }
  });
}
</script>