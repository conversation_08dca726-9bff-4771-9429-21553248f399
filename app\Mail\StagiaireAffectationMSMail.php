<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Stage;
use App\Models\AffectationMaitreStage;

class StagiaireAffectationMSMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $stage;
    public $affectation;
    public $stagiaire;
    public $maitreStage;

    /**
     * Create a new message instance.
     */
    public function __construct(Stage $stage, AffectationMaitreStage $affectation)
    {
        $this->stage = $stage;
        $this->affectation = $affectation;
        $this->stagiaire = $stage->stagiaire->user;
        $this->maitreStage = $affectation->agent->user;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Affectation de votre maître de stage - ' . $this->stage->demandeStage->code_suivi,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: []
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.stagiaire-affectation-ms',
            with: [
                'stage' => $this->stage,
                'affectation' => $this->affectation,
                'stagiaire' => $this->stagiaire,
                'maitreStage' => $this->maitreStage,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
