<template>
  <div class="relative">
    <!-- Bouton de notification -->
    <button
      @click="toggleDropdown"
      class="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg transition-colors"
      :class="{ 'text-blue-600': hasUnreadNotifications }"
    >
      <!-- Icône cloche -->
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
      </svg>
      
      <!-- Badge de compteur -->
      <span
        v-if="unreadCount > 0"
        class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full min-w-[1.25rem] h-5"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <!-- Dropdown des notifications -->
    <div
      v-if="showDropdown"
      class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
      @click.stop
    >
      <!-- En-tête -->
      <div class="px-4 py-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
          <button
            v-if="unreadCount > 0"
            @click="markAllAsRead"
            class="text-xs text-blue-600 hover:text-blue-800 font-medium"
          >
            Tout marquer comme lu
          </button>
        </div>
      </div>

      <!-- Liste des notifications -->
      <div class="max-h-96 overflow-y-auto">
        <div v-if="notifications.length === 0" class="px-4 py-6 text-center text-gray-500">
          <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-2.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7"/>
          </svg>
          <p class="text-sm">Aucune notification</p>
        </div>

        <div v-else>
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
            :class="{ 'bg-blue-50': !notification.read_at }"
            @click="handleNotificationClick(notification)"
          >
            <div class="flex items-start space-x-3">
              <!-- Icône de notification -->
              <div class="flex-shrink-0 mt-1">
                <div
                  class="w-2 h-2 rounded-full"
                  :class="notification.read_at ? 'bg-gray-300' : 'bg-blue-500'"
                ></div>
              </div>

              <!-- Contenu de la notification -->
              <div class="flex-1 min-w-0">
                <p
                  class="text-sm text-gray-900 line-clamp-2"
                  :class="{ 'font-semibold': !notification.read_at }"
                >
                  {{ notification.data.message || notification.data.title }}
                </p>
                <p class="text-xs text-gray-500 mt-1">
                  {{ formatDate(notification.created_at) }}
                </p>
              </div>

              <!-- Bouton marquer comme lu -->
              <div v-if="!notification.read_at" class="flex-shrink-0">
                <button
                  @click.stop="markAsRead(notification.id)"
                  class="text-xs text-blue-600 hover:text-blue-800"
                >
                  ✓
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pied de page -->
      <div v-if="notifications.length > 0" class="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
        <Link
          :href="notificationsRoute"
          class="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          Voir toutes les notifications
        </Link>
      </div>
    </div>

    <!-- Overlay pour fermer le dropdown -->
    <div
      v-if="showDropdown"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'

const props = defineProps({
  notifications: {
    type: Array,
    default: () => []
  },
  notificationsRoute: {
    type: String,
    default: '#'
  }
})

const showDropdown = ref(false)

// Computed properties
const unreadCount = computed(() => {
  return props.notifications.filter(n => !n.read_at).length
})

const hasUnreadNotifications = computed(() => {
  return unreadCount.value > 0
})

// Methods
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const closeDropdown = () => {
  showDropdown.value = false
}

const handleNotificationClick = (notification) => {
  // Marquer comme lu si pas encore lu
  if (!notification.read_at) {
    markAsRead(notification.id)
  }

  // Rediriger vers l'URL de la notification si elle existe
  if (notification.data.url) {
    router.visit(notification.data.url)
  }

  closeDropdown()
}

const markAsRead = (notificationId) => {
  router.patch(route('notifications.mark-read', notificationId), {}, {
    preserveScroll: true,
    preserveState: true,
    onSuccess: () => {
      // Mettre à jour localement
      const notification = props.notifications.find(n => n.id === notificationId)
      if (notification) {
        notification.read_at = new Date().toISOString()
      }
    }
  })
}

const markAllAsRead = () => {
  router.patch(route('notifications.mark-all-read'), {}, {
    preserveScroll: true,
    preserveState: true,
    onSuccess: () => {
      // Mettre à jour localement
      props.notifications.forEach(notification => {
        if (!notification.read_at) {
          notification.read_at = new Date().toISOString()
        }
      })
    }
  })
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now - date) / (1000 * 60))

  if (diffInMinutes < 1) {
    return 'À l\'instant'
  } else if (diffInMinutes < 60) {
    return `Il y a ${diffInMinutes} min`
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60)
    return `Il y a ${hours}h`
  } else {
    const days = Math.floor(diffInMinutes / 1440)
    return `Il y a ${days}j`
  }
}

// Fermer le dropdown avec Escape
const handleEscape = (e) => {
  if (e.key === 'Escape') {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
