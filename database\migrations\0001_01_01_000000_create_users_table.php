<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->string('prenom')->nullable();
            $table->date('date_de_naissance')->nullable();
            $table->enum('sexe', ['Homme', 'Femme'])->nullable();
            $table->string('adresse')->nullable();
            $table->string('email')->unique(); // L'email est requis par défaut dans Laravel
            $table->timestamp('email_verified_at')->nullable();
            $table->string('avatar')->nullable(); // Ajout de la colonne avatar
            $table->string('password'); // Le mot de passe est requis par défaut dans Laravel
            $table->string('telephone', 20)->nullable(); // Format béninois +229 XX XX XX XX
            $table->date('date_d_inscription')->nullable();
            $table->enum('role', ['stagiaire', 'Agent', 'université', 'admin'])->default('stagiaire');
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};