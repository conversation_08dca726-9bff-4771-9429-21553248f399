<template>
  <Head :title="`Université - ${universite.nom_complet}`" />
  <AdminLayout>
    <div class="py-8 bg-gradient-to-br from-slate-50 to-emerald-50 min-h-screen">
      <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
          <div class="flex items-center gap-4">
            <div class="p-3 bg-emerald-600 rounded-xl shadow-lg">
              <AcademicCapIcon class="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 class="text-3xl font-bold text-slate-800">{{ universite.nom_complet }}</h1>
              <p class="text-slate-600 mt-1">{{ universite.sigle }} • {{ universite.localisation }}</p>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <Link
              :href="route('admin.universites.edit', universite.id)"
              class="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <PencilIcon class="w-5 h-5" />
              Modifier
            </Link>
            <Link
              :href="route('admin.universites.index')"
              class="inline-flex items-center gap-2 border border-slate-300 text-slate-700 hover:bg-slate-50 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <ArrowLeftIcon class="w-5 h-5" />
              Retour
            </Link>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Informations principales -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Détails de l'université -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Informations générales</h3>
              </div>
              <div class="p-6">
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Nom complet</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ universite.nom_complet }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Sigle</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ universite.sigle }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Localisation</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ universite.localisation }}</dd>
                  </div>
                  <div>
                    <dt class="text-sm font-medium text-gray-500">Statut</dt>
                    <dd class="mt-1">
                      <span
                        :class="[
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          universite.active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        ]"
                      >
                        {{ universite.active ? 'Active' : 'Inactive' }}
                      </span>
                    </dd>
                  </div>
                  <div v-if="universite.description" class="md:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ universite.description }}</dd>
                  </div>
                </dl>
              </div>
            </div>

            <!-- Stagiaires associés -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Stagiaires associés</h3>
                <p class="text-sm text-gray-600 mt-1">Liste des stagiaires de cette université</p>
              </div>
              <div v-if="universite.stagiaires && universite.stagiaires.length > 0" class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stagiaire
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Téléphone
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date d'inscription
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="stagiaire in universite.stagiaires" :key="stagiaire.id" class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 h-8 w-8">
                            <div class="h-8 w-8 rounded-full bg-emerald-100 flex items-center justify-center">
                              <UserIcon class="h-4 w-4 text-emerald-600" />
                            </div>
                          </div>
                          <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                              {{ stagiaire.prenom }} {{ stagiaire.nom }}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ stagiaire.email }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ stagiaire.telephone || 'Non renseigné' }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ formatDate(stagiaire.created_at) }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="text-center py-12">
                <UserIcon class="mx-auto h-12 w-12 text-gray-400" />
                <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun stagiaire</h3>
                <p class="mt-1 text-sm text-gray-500">Aucun stagiaire n'est encore associé à cette université.</p>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Responsable -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Responsable</h3>
              </div>
              <div class="p-6">
                <div v-if="universite.responsable" class="flex items-center">
                  <div class="flex-shrink-0 h-12 w-12">
                    <div class="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                      <UserIcon class="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ universite.responsable.user.prenom }} {{ universite.responsable.user.nom }}
                    </div>
                    <div class="text-sm text-gray-500">{{ universite.responsable.user.email }}</div>
                    <div class="text-xs text-blue-600 font-medium mt-1">Responsable Université (RU)</div>
                  </div>
                </div>
                <div v-else class="text-center py-4">
                  <UserIcon class="mx-auto h-8 w-8 text-gray-400" />
                  <p class="mt-2 text-sm text-gray-500">Aucun responsable assigné</p>
                </div>
              </div>
            </div>

            <!-- Statistiques -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-900">Statistiques</h3>
              </div>
              <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Nombre de stagiaires</span>
                  <span class="text-lg font-semibold text-gray-900">
                    {{ universite.stagiaires ? universite.stagiaires.length : 0 }}
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Date de création</span>
                  <span class="text-sm text-gray-900">{{ formatDate(universite.created_at) }}</span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">Dernière modification</span>
                  <span class="text-sm text-gray-900">{{ formatDate(universite.updated_at) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { Head, Link } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import {
  AcademicCapIcon,
  PencilIcon,
  ArrowLeftIcon,
  UserIcon
} from '@heroicons/vue/24/outline';

const props = defineProps({
  universite: Object
});

const formatDate = (dateString) => {
  if (!dateString) return 'Non disponible';
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
</script>
